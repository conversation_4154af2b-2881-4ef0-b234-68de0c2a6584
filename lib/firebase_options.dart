// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDVzPuXhIGf1h3144iFkg3y7YMSJjy7EEc',
    appId: '1:276514509704:web:9fdd568f95d8ab4714a03d',
    messagingSenderId: '276514509704',
    projectId: 'tvd-dev',
    authDomain: 'tvd-dev.firebaseapp.com',
    storageBucket: 'tvd-dev.firebasestorage.app',
    measurementId: 'G-RZFDNKKFE0',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBcH0hz49sZZxKZTRSGW7BiZUBPVJvLo50',
    appId: '1:276514509704:android:21b58161e3f57a5d14a03d',
    messagingSenderId: '276514509704',
    projectId: 'tvd-dev',
    storageBucket: 'tvd-dev.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCVkZb-kzpUuAJW_XMDL5PKNJklb3izNLE',
    appId: '1:276514509704:ios:9bb9c378df2290f914a03d',
    messagingSenderId: '276514509704',
    projectId: 'tvd-dev',
    storageBucket: 'tvd-dev.firebasestorage.app',
    androidClientId: '276514509704-6c7bm84ujjc4s48811pi1k0ljgkhreed.apps.googleusercontent.com',
    iosClientId: '276514509704-dllemph2pc38bjornh76kl0aped8tl35.apps.googleusercontent.com',
    iosBundleId: 'com.thevoicedirectory.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDVzPuXhIGf1h3144iFkg3y7YMSJjy7EEc',
    appId: '1:276514509704:web:723fa3881cd4bf9d14a03d',
    messagingSenderId: '276514509704',
    projectId: 'tvd-dev',
    authDomain: 'tvd-dev.firebaseapp.com',
    storageBucket: 'tvd-dev.firebasestorage.app',
    measurementId: 'G-VPB0K1XW2P',
  );
}