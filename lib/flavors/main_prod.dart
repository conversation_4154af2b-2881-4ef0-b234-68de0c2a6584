import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:the_voice_directory_flutter/core/config/app_flavors.dart';
import 'package:the_voice_directory_flutter/core/dependency_injection/service_locator.dart';
import 'package:the_voice_directory_flutter/main.dart';
import 'config_widget.dart';

Future<void> main() async {
  await runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await Firebase.initializeApp();
    await setupLocator();

    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

    runApp(
      const ConfigWidget(
        flavor: Flavor.prod,
        child: AnnotatedRegion(
          value: SystemUiOverlayStyle.light,
          child: MyApp(),
        ),
      ),
    );
  }, (error, stackTrace) {
    
  });
}
