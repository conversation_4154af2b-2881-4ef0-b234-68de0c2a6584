import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

import '../../../core/services/hive/hive_keys.dart';
import 'delete_account_state.dart';

class DeleteAccountBloc extends Cubit<DeleteAccountState> {
  @override
  DeleteAccountBloc() : super(DeleteAccountInitState());

  void deleteAccount() async {
    emit(DeleteAccountLoadingState());
    try {
      final response = await ApiService.instance.deleteAccount();
      if (response.success) {
        await FirebaseAuth.instance.signOut();
        String? playerId = HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.playerId);
        await HiveStorageHelper.clearBox(HiveBoxName.user);
        ApiService.instance.removeToken();
        if (playerId != null) {
          await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.playerId, playerId);
        }
        emit(DeleteAccountSuccessState());
      } else {
        emit(DeleteAccountErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(DeleteAccountErrorState(AppStrings.genericErrorMsg));
    }
  }
}
