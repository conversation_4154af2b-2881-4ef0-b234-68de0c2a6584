import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  final bool isTerms;
  const PrivacyPolicyScreen({super.key, required this.isTerms});

  static const String _htmlContentForTermsCondition = """
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>The Voice Directory - Terms & Conditions</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans', sans-serif;
      background: #f4f7fa;
      color: #3A393C;
      font-size: 8px; /* 👈 Font size reduced to 8px */
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 900px;
      margin: 40px auto;
      background: white;
      padding: 40px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
    }

    h1, h2, h3 {
      color: #000;
      font-weight: bold;
    }

    h1 {
      text-align: center;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .meta {
      font-size: 7px;
      color: #666;
      text-align: center;
      margin-bottom: 30px;
    }

    h2 {
      font-size: 10px;
      margin-top: 30px;
      border-left: 5px solid #1a73e8;
      padding-left: 12px;
    }

    ul {
      list-style: disc;
      padding-left: 20px;
    }

    li {
      margin-bottom: 4px;
    }

    .section {
      margin-bottom: 25px;
    }

    .contact {
      background-color: #f1f3f6;
      padding: 12px 16px;
      border-left: 5px solid #1a73e8;
      border-radius: 6px;
      margin-top: 20px;
    }

    .contact p {
      margin: 6px 0;
    }

    a {
      color: #1a73e8;
      text-decoration: none;
    }

    @media screen and (max-width: 600px) {
      .container {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>The Voice Directory - Terms & Conditions</h1>
    <div class="meta">
      <p><strong>Effective Date:</strong> 01 July 2025</p>
    </div>

    <div class="section">
      <p>Welcome to The Voice Directory, a platform offered by Sound & Vision India Private Limited ("we", "us", or "our"). These Terms & Conditions govern your use of our mobile and web applications, services, and content (collectively, the "App").</p>
      <p>Please read these Terms carefully before using The Voice Directory.</p>
    </div>

    <div class="section"><h2>1. Acceptance of Terms</h2>
      <p>By accessing or using The Voice Directory, you agree to be bound by these Terms. If you do not agree, please do not use the App.</p>
    </div>

    <div class="section"><h2>2. About The Voice Directory</h2>
      <p>The Voice Directory is a talent listing platform that allows voice artists to create public profiles and get discovered by studios, agencies, and other users.</p>
    </div>

    <div class="section"><h2>3. Eligibility</h2>
      <p>To use this app, you must be at least 18 years old or have permission from a legal guardian. By using the app, you represent and warrant that you meet these requirements.</p>
    </div>

    <div class="section"><h2>4. User Accounts</h2>
      <ul>
        <li>You may be required to create an account to access certain features.</li>
        <li>You agree to provide accurate, complete, and up-to-date information.</li>
        <li>You are responsible for maintaining the confidentiality of your account credentials.</li>
        <li>We reserve the right to suspend or delete accounts that violate these Terms.</li>
      </ul>
    </div>

    <div class="section"><h2>5. User Content</h2>
      <ul>
        <li>You retain ownership of the content (including voice samples, profile details, and photos) you submit.</li>
        <li>By uploading content, you grant us a non-exclusive, royalty-free license to use, store, publish, and display your content as part of the directory.</li>
        <li>Do not upload content you do not have rights to or that is offensive, unlawful, or violates any third-party rights.</li>
      </ul>
    </div>

    <div class="section"><h2>6. Acceptable Use</h2>
      <p>You agree not to:</p>
      <ul>
        <li>Use the app for any unlawful purpose</li>
        <li>Impersonate others or provide false information</li>
        <li>Upload viruses or malicious code</li>
        <li>Attempt to scrape, extract, or copy user data or system functionality</li>
      </ul>
    </div>

    <div class="section"><h2>7. Intellectual Property</h2>
      <p>All content, branding, and design elements of The Voice Directory (excluding user-submitted content) are the property of Sound & Vision India Private Limited. You may not use our trademarks or assets without prior written permission.</p>
    </div>

    <div class="section"><h2>8. Termination</h2>
      <p>We reserve the right to suspend or terminate your access to the app at any time for any reason, including but not limited to violation of these Terms.</p>
    </div>

    <div class="section"><h2>9. Limitation of Liability</h2>
      <p>The Voice Directory is provided "as is". While we strive for accuracy and uptime, we make no warranties regarding the reliability or availability of the service. We are not responsible for any loss or damages resulting from your use of the app.</p>
    </div>

    <div class="section"><h2>10. Privacy</h2>
      <p>Your privacy is important to us. Please refer to our Privacy Policy to understand how we collect, use, and protect your data.</p>
    </div>

    <div class="section"><h2>11. Modifications</h2>
      <p>We may update these Terms from time to time. Changes will be reflected on this page with an updated effective date. Continued use of the app after changes indicates your acceptance.</p>
    </div>

    <div class="section"><h2>12. Contact Us</h2>
      <div class="contact">
        <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p>Address: 101/102, E wing, Abhishek Apartments,<br>
        Juhu Versova Link Road, Andheri (W),<br>
        Mumbai, 400053</p>
      </div>
    </div>
  </div>
</body>
</html>
""";

  final String _htmlContentForPrivacyPolicy = """<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>The Voice Directory - Privacy Policy</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans', sans-serif;
      background: #f4f7fa;
      color: #3A393C;
      font-size: 8px;
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 900px;
      margin: 40px auto;
      background: white;
      padding: 40px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
    }

    h1, h2, h3 {
      color: #000;
      font-weight: bold;
    }

    h1 {
      text-align: center;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .meta {
      font-size: 7px;
      color: #666;
      text-align: center;
      margin-bottom: 30px;
    }

    h2 {
      font-size: 10px;
      margin-top: 30px;
      border-left: 5px solid #1a73e8;
      padding-left: 12px;
    }

    ul {
      list-style: disc;
      padding-left: 20px;
    }

    li {
      margin-bottom: 4px;
    }

    .section {
      margin-bottom: 25px;
    }

    .contact {
      background-color: #f1f3f6;
      padding: 12px 16px;
      border-left: 5px solid #1a73e8;
      border-radius: 6px;
      margin-top: 20px;
    }

    .contact p {
      margin: 6px 0;
    }

    a {
      color: #1a73e8;
      text-decoration: none;
    }

    @media screen and (max-width: 600px) {
      .container {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>TVD Privacy Policy</h1>
    <div class="meta">
      <p><strong>Effective Date:</strong> 02 July 2025</p>
    </div>

    <div class="section"><h2>1. Introduction</h2>
      <p>Welcome to The Voice Directory, a platform provided by Sound & Vision India Private Limited. Your privacy is important to us. This Privacy Policy explains what personal data we collect, how we use it, and your rights regarding that data.</p>
    </div>

    <div class="section"><h2>2. Information We Collect</h2>
      <p><strong>a. Information You Provide</strong></p>
      <ul>
        <li>Full name, email, phone number, password</li>
        <li>Voice profile data (audio demos, styles, language, bio)</li>
        <li>Communications sent via in-app chat or support</li>
      </ul>
      <p><strong>b. Information Collected Automatically</strong></p>
      <ul>
        <li>Device type, IP address, OS version, app version</li>
        <li>Usage behavior (e.g., pages visited, time spent)</li>
        <li>Crash and performance logs</li>
        <li>Approximate location (if location access is granted)</li>
      </ul>
    </div>

    <div class="section"><h2>3. How We Use Your Information</h2>
      <ul>
        <li>Register and manage your account</li>
        <li>Display your profile to clients</li>
        <li>Manage jobs</li>
        <li>Communicate updates or support responses</li>
        <li>Improve app experience and features</li>
        <li>Meet legal and compliance requirements</li>
      </ul>
    </div>

    <div class="section"><h2>4. Sharing of Information</h2>
      <p>We do not sell your personal data. We may share your information:</p>
      <ul>
        <li>With clients for job matching</li>
        <li>With service vendors (e.g., hosting, analytics)</li>
        <li>When required by law or to enforce terms</li>
      </ul>
    </div>

    <div class="section"><h2>5. Data Security</h2>
      <p>We use secure technologies and practices to protect your information. However, no system is fully secure, so we cannot guarantee absolute safety.</p>
    </div>

    <div class="section"><h2>6. Your Rights</h2>
      <ul>
        <li>Access or modify your data via the app</li>
        <li>Delete your account from your Account Settings</li>
        <li>Opt out of non-essential notifications</li>
        <li>Withdraw consent for optional permissions</li>
      </ul>
    </div>

    <div class="section"><h2>7. Children’s Privacy</h2>
      <p>Our service is not intended for children under 18. We do not knowingly collect data from minors.</p>
    </div>

    <div class="section"><h2>8. International Access</h2>
      <p>By using our services from outside India, you consent to the processing and storage of your data in India.</p>
    </div>

    <div class="section"><h2>9. Changes to This Policy</h2>
      <p>We may update this policy occasionally. Major changes will be notified via email or in-app alert, along with the updated effective date.</p>
    </div>

    <div class="section"><h2>10. Contact Us</h2>
      <div class="contact">
        <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p>Address: 101/102, E wing, Abhishek Apartments,<br>
        Juhu Versova Link Road, Andheri (W),<br>
        Mumbai, 400053</p>
      </div>
    </div>
  </div>
</body>
</html>
 """;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: HtmlWidget(_htmlContentForPrivacyPolicy),
        ),
      ),
    );
  }
}
