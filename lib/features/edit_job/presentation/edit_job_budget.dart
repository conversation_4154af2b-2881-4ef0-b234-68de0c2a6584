import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../utils/validations.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/radio_container.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../post_job/data/enums/budget_type_enum.dart';
import '../../post_job/models/job_post_model.dart';
import '../../post_job/presentation/widgets/post_job_page_frame.dart';
import '../bloc/edit_job_cubit.dart';

class EditJobBudget extends StatefulWidget {
  const EditJobBudget({super.key});

  @override
  State<EditJobBudget> createState() => _EditJobBudgetState();
}

class _EditJobBudgetState extends State<EditJobBudget> {
  BudgetType? budgetType;
  bool autovalidation = false;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController fixedBudgetController;
  late TextEditingController minRangeController;
  late TextEditingController maxRangeController;

  @override
  void initState() {
    super.initState();
    fixedBudgetController = TextEditingController();
    minRangeController = TextEditingController();
    maxRangeController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<EditJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      if (jobPostModel.budgetType == BudgetType.fixed) {
        budgetType = BudgetType.fixed;
        fixedBudgetController.text = jobPostModel.fixedBudget?.toString() ?? '';
      } else if (jobPostModel.budgetType == BudgetType.range) {
        budgetType = BudgetType.range;
        minRangeController.text = jobPostModel.minBudgetRange?.toString() ?? '';
        maxRangeController.text = jobPostModel.maxBudgetRange?.toString() ?? '';
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocListener<EditJobCubit, EditJobState>(
        listener: (context, state) {
          if (state is EditJobLoadingState) {
            Dialogs.showOnlyLoader(context);
          }
          if (state is EditJobSuccessState) {
            context.pop();
            context.pop();
            HiveStorageHelper.deleteKeyInBox(boxName: HiveBoxName.user, key: HiveKeys.editJobData);
            CustomToast.show(context: context, message: AppStrings.jobEditedSuccessfully, isSuccess: true);
          }
          if (state is EditJobErrorState) {
            context.pop();
            CustomToast.show(context: context, message: state.errorMessage);
          }
        },
        child: Padding(
          padding: Responsive.isDesktop(context)
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              if (!!Responsive.isDesktop(context)) ...[
                Padding(
                  padding: EdgeInsets.only(top: 40.h, left: 80.w),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: CustomBackButtonArrow(
                      onTap: () => context.pop(),
                    ),
                  ),
                ),
              ],
              Expanded(
                child: PostJobPageFrame(
                  onCancelPressed: () => context.pop(),
                  onBackPressed: () => context.pop(),
                  isEditing: true,
                  formKey: _formKey,
                  nextBtnText: AppStrings.save,
                  onNextPressed: () async {
                    if (budgetType == null) {
                      CustomToast.show(
                        context: context,
                        message: ValidationMsg.plsSelect("budget type"),
                      );
                      return;
                    }
                    if (_formKey.currentState!.validate()) {
                      final cubit = context.read<EditJobCubit>();
                      JobPostModel? jobPostModel = cubit.loadJobPostFromHive();

                      if (budgetType == jobPostModel?.budgetType &&
                          fixedBudgetController.text ==
                              jobPostModel?.fixedBudget.toString()) {
                        CustomToast.show(
                            context: context,
                            message: AppStrings.noChangesMade);
                        return;
                      }

                      if (budgetType == jobPostModel?.budgetType &&
                          minRangeController.text ==
                              jobPostModel?.minBudgetRange.toString() &&
                          maxRangeController.text ==
                              jobPostModel?.maxBudgetRange.toString()) {
                        CustomToast.show(
                            context: context,
                            message: AppStrings.noChangesMade);
                        return;
                      }

                      if (budgetType == BudgetType.fixed) {
                        cubit.updateJobDetails(
                          jobModel: JobPostModel(
                            id: jobPostModel?.id,
                            budgetType: budgetType,
                            fixedBudget: fixedBudgetController.text.trim(),
                          ),
                        );
                      } else if (budgetType == BudgetType.range) {
                        cubit.updateJobDetails(
                          jobModel: JobPostModel(
                            id: jobPostModel?.id,
                            budgetType: budgetType,
                            minBudgetRange: minRangeController.text.trim(),
                            maxBudgetRange: maxRangeController.text.trim(),
                          ),
                        );
                      }
                    } else {
                      autovalidation = true;
                      setState(() {});
                    }
                  },
                  children: [
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text24And20SemiBold(
                        AppStrings.budgetType,
                      ),
                    ),
                    16.ph,
                    !Responsive.isDesktop(context)
                        ?
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: RadioContainer(
                            groupValue: budgetType,
                            onChanged: onBudgetTypeChange,
                            value: BudgetType.fixed,
                            title: AppStrings.fixed,
                          ),
                        ),
                        11.pw,
                        Expanded(
                          child: RadioContainer(
                            groupValue: budgetType,
                            onChanged: onBudgetTypeChange,
                            value: BudgetType.range,
                            title: AppStrings.range,
                          ),
                        ),
                      ],
                    ):
                     Row(
                      children: [
                        RadioContainer(
                          width: 308.w,
                          groupValue: budgetType,
                          onChanged: onBudgetTypeChange,
                          value: BudgetType.fixed,
                          title: AppStrings.fixed,
                        ),
                        14.pw,
                        RadioContainer(
                          width: 308.w,
                          groupValue: budgetType,
                          onChanged: onBudgetTypeChange,
                          value: BudgetType.range,
                          title: AppStrings.range,
                        ),
                      ],
                    ),
                    24.ph,
                    if (budgetType == BudgetType.fixed) ...[
                      const Align(
                        alignment: Alignment.centerLeft,
                        child: Text24And20SemiBold(AppStrings.budgetPrice),
                      ),
                      16.ph,
                      AppTextFormField(
                        width: !Responsive.isDesktop(context)
                            ? double.infinity
                            : 308.w,
                        controller: fixedBudgetController,
                        prefixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            1.ph,
                            const TextTitle18And14("₹"),
                            Container(
                              width: 1.h,
                              height: 20.h,
                              color: colorScheme.lightGreyD9D9D9,
                            )
                          ],
                        ),
                        validator: (value) {
                          return Validator.emptyValidator(
                              value, ValidationMsg.plsEntervalid("amount"));
                        },
                        isAutovalidateModeOn: autovalidation,
                        keyboardType: TextInputType.number,
                        hintText: AppStrings.enterAmount,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(50),
                          FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                        ],
                      ),
                    ] else if (budgetType == BudgetType.range) ...[
                      const Align(
                        alignment: Alignment.centerLeft,
                        child: Text24And20SemiBold(AppStrings.rangePrice),
                      ),
                      16.ph,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Flexible(
                            child: AppTextFormField(
                              width: !Responsive.isDesktop(context)
                                  ? double.infinity
                                  : 308.w,
                              controller: minRangeController,
                              prefixIcon: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  1.ph,
                                  const TextTitle18And14("₹"),
                                  Container(
                                    width: 1.h,
                                    height: 20.h,
                                    color: colorScheme.lightGreyD9D9D9,
                                  )
                                ],
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Please enter a minimum price.";
                                }
                                final min = int.tryParse(value);
                                final max =
                                    int.tryParse(maxRangeController.text);

                                if (min == null || min <= 0) {
                                  return "Minimum price must be greater than ₹0.";
                                }
                                if (max != null && min > max) {
                                  return "Minimum price cannot be greater than maximum price.";
                                }
                                return null;
                              },
                              isAutovalidateModeOn: autovalidation,
                              keyboardType: TextInputType.number,
                              hintText: AppStrings.min,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 11.pw : 12.pw,
                          Flexible(
                            child: AppTextFormField(
                              width: !Responsive.isDesktop(context)
                                  ? double.infinity
                                  : 308.w,
                              controller: maxRangeController,
                              prefixIcon: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  1.ph,
                                  const TextTitle18And14("₹"),
                                  Container(
                                    width: 1.h,
                                    height: 20.h,
                                    color: colorScheme.lightGreyD9D9D9,
                                  )
                                ],
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Please enter a maximum price.";
                                }
                                final max = int.tryParse(value);
                                final min =
                                    int.tryParse(minRangeController.text);

                                if (max == null || max <= 0) {
                                  return "Maximum price must be greater than ₹0.";
                                }
                                if (min != null && max <= min) {
                                  return "Maximum price must be greater than minimum price.";
                                }
                                if (max > 10000000) {
                                  return "Maximum price cannot exceed ₹10,00,000.";
                                }
                                return null;
                              },
                              isAutovalidateModeOn: autovalidation,
                              keyboardType: TextInputType.number,
                              hintText: AppStrings.max,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                    // if (budgetType != null) ...[
                    //   12.ph,
                    //   Align(
                    //     alignment: Alignment.centerLeft,
                    //     child: TextTitle14(
                    //       AppStrings.gstText,
                    //       color: colorScheme.hyperlinkBlueColor,
                    //       fontWeight: FontWeight.w600,
                    //     ),
                    //   )
                    // ]
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onBudgetTypeChange(BudgetType? budgetType) {
    this.budgetType = budgetType!;
    setState(() {});
  }
}
