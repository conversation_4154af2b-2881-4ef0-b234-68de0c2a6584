import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../utils/validations.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../post_job/models/job_post_model.dart';
import '../../post_job/presentation/widgets/post_job_page_frame.dart';
import '../bloc/edit_job_cubit.dart';

class EditJobTimeline extends StatefulWidget {
  const EditJobTimeline({super.key});

  @override
  State<EditJobTimeline> createState() => _EditJobTimelineState();
}

class _EditJobTimelineState extends State<EditJobTimeline> {
  final formGlobalKey = GlobalKey<FormState>();
  late TextEditingController durationHourController;
  late TextEditingController durationMinController;
  late TextEditingController durationSecController;
  late TextEditingController projectDeadlineController;
  late TextEditingController responseDeadlineController;
  bool autovalidation = false;
  DateTime? projectDeadlineDate;
  DateTime? responseDeadlineDate;

  @override
  void initState() {
    super.initState();
    durationHourController = TextEditingController();
    durationMinController = TextEditingController();
    durationSecController = TextEditingController();
    projectDeadlineController = TextEditingController();
    responseDeadlineController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<EditJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      durationHourController.text = jobPostModel.dubbingDurationHours ?? '';
      durationMinController.text = jobPostModel.dubbingDuration ?? '';
      durationSecController.text = jobPostModel.dubbingDurationSeconds ?? '';

      final projectDate = jobPostModel.projectDeadline;
      if (projectDate != null) {
        projectDeadlineDate = projectDate;
        projectDeadlineController.text =
            DateFormat('d MMMM yyyy').format(projectDate);
      }

      final responseDate = jobPostModel.responseDeadline;
      if (responseDate != null) {
        responseDeadlineDate = responseDate;
        responseDeadlineController.text =
            DateFormat('d MMMM yyyy').format(responseDate);
      }

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocListener<EditJobCubit, EditJobState>(
        listener: (context, state) {
          if (state is EditJobLoadingState) {
            Dialogs.showOnlyLoader(context);
          }
          if (state is EditJobSuccessState) {
            context.pop();
            context.pop();
            HiveStorageHelper.deleteKeyInBox(
                boxName: HiveBoxName.user, key: HiveKeys.editJobData);
            CustomToast.show(
                context: context,
                message: AppStrings.jobEditedSuccessfully,
                isSuccess: true);
          }
          if (state is EditJobErrorState) {
            context.pop();
            CustomToast.show(context: context, message: state.errorMessage);
          }
        },
        child: Padding(
          padding: Responsive.isDesktop(context)
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              if (!!Responsive.isDesktop(context)) ...[
                Padding(
                  padding: EdgeInsets.only(top: 40.h, left: 80.w),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: CustomBackButtonArrow(
                      onTap: () => context.pop(),
                    ),
                  ),
                ),
              ],
              Expanded(
                child: PostJobPageFrame(
                  onCancelPressed: () => context.pop(),
                  onBackPressed: () => context.pop(),
                  isEditing: true,
                  formKey: formGlobalKey,
                  nextBtnText: AppStrings.save,
                  onNextPressed: () async {
                    if (formGlobalKey.currentState!.validate()) {
                      final cubit = context.read<EditJobCubit>();
                      JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
                      if (projectDeadlineDate ==
                              jobPostModel?.projectDeadline &&
                          responseDeadlineDate ==
                              jobPostModel?.responseDeadline) {
                        CustomToast.show(
                            context: context,
                            message: AppStrings.noChangesMade);
                        return;
                      }

                      cubit.updateJobDetails(
                        jobModel: JobPostModel(
                          id: jobPostModel?.id,
                          projectDeadline: projectDeadlineDate,
                          responseDeadline: responseDeadlineDate,
                        ),
                      );
                    } else {
                      autovalidation = true;
                      setState(() {});
                    }
                  },
                  children: [
                    !Responsive.isDesktop(context)
                        ? Column(
                            children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text24And20SemiBold(AppStrings.duration)),
                              16.ph,
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationHourController,
                                      hintText: AppStrings.enterHours,
                                      titleText: AppStrings.hours,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                  11.pw,
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationMinController,
                                      hintText: AppStrings.enterMinDuration,
                                      titleText: AppStrings.minutes,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                  11.pw,
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationSecController,
                                      hintText: AppStrings.enterMinDuration,
                                      titleText: AppStrings.seconds,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                ],
                              ),
                              !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                              AppTextFormField(
                                isParentField: true,
                                prefixIconConstraints: BoxConstraints(
                                  minHeight: 20.h,
                                  minWidth: 48.h,
                                ),
                                prefixIcon:
                                    SvgPicture.asset(AppImages.calendarIc),
                                readOnly: true,
                                controller: projectDeadlineController,
                                hintText: AppStrings.projectDeadlineEnter,
                                titleText: AppStrings.projectDeadline,
                                validator: (value) {
                                  return Validator.emptyValidator(
                                      value,
                                      ValidationMsg.plsEntervalid(
                                          "project deadline"));
                                },
                                isAutovalidateModeOn: autovalidation,
                                onTap: () async {
                                  DateTime? pickedDate = await showDatePicker(
                                    context: context,
                                    initialEntryMode:
                                        DatePickerEntryMode.calendarOnly,
                                    initialDate:
                                        projectDeadlineDate ?? DateTime.now(),
                                    firstDate: DateTime.now(),
                                    lastDate: DateTime(2100),
                                  );
                                  if (pickedDate != null) {
                                    if (responseDeadlineDate != null &&
                                        pickedDate
                                            .isBefore(responseDeadlineDate!)) {
                                      if (mounted) {
                                        CustomToast.show(
                                          context: context,
                                          message: ValidationMsg
                                              .projectDeadlineAfterResponse,
                                        );
                                      }
                                      return;
                                    }
                                    projectDeadlineDate = pickedDate;
                                    projectDeadlineController.text =
                                        DateFormat('d MMMM yyyy')
                                            .format(pickedDate);
                                  }
                                },
                              ),
                              !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                              AppTextFormField(
                                isParentField: true,
                                prefixIconConstraints: BoxConstraints(
                                  minHeight: 20.h,
                                  minWidth: 48.h,
                                ),
                                prefixIcon:
                                    SvgPicture.asset(AppImages.calendarIc),
                                readOnly: true,
                                controller: responseDeadlineController,
                                hintText: AppStrings.responseDeadlineEnter,
                                titleText: AppStrings.responseDeadlineAsterisk,
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  return Validator.emptyValidator(
                                      value,
                                      ValidationMsg.plsEntervalid(
                                          "response deadline"));
                                },
                                onTap: () async {
                                  DateTime? pickedDate = await showDatePicker(
                                    context: context,
                                    initialEntryMode:
                                        DatePickerEntryMode.calendarOnly,
                                    initialDate:
                                        responseDeadlineDate ?? DateTime.now(),
                                    firstDate: DateTime.now(),
                                    lastDate: DateTime(2100),
                                  );
                                  if (pickedDate != null) {
                                    if (projectDeadlineDate != null &&
                                        pickedDate
                                            .isAfter(projectDeadlineDate!)) {
                                      if (mounted) {
                                        CustomToast.show(
                                          context: context,
                                          message: ValidationMsg
                                              .responseBeforeProjectDeadline,
                                        );
                                      }
                                      return;
                                    }
                                    responseDeadlineDate = pickedDate;
                                    responseDeadlineController.text =
                                        DateFormat('d MMMM yyyy')
                                            .format(pickedDate);
                                  }
                                },
                                isAutovalidateModeOn: autovalidation,
                              ),
                            ],
                          )
                        : Column(
                            children: [
                            const Align(
                            alignment: Alignment.centerLeft,
                            child: Text24And20SemiBold( AppStrings.duration)),
                            16.ph,
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationHourController,
                                      hintText: AppStrings.enterHours,
                                      titleText: AppStrings.hours,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                  14.pw,
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationMinController,
                                      hintText: AppStrings.enterMinDuration,
                                      titleText: AppStrings.minutes,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                  14.pw,
                                  Expanded(
                                    child: AppTextFormField(
                                      maxLength: 10,
                                      controller: durationSecController,
                                      hintText: AppStrings.enterMinDuration,
                                      titleText: AppStrings.seconds,
                                      keyboardType: TextInputType.number,
                                      // validator: (value) {
                                      //   return Validator.emptyValidator(
                                      //       value,
                                      //       ValidationMsg.plsEntervalid(
                                      //           "tentative dubbing duration"));
                                      // },
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp("[0-9]")),
                                      ],
                                      isAutovalidateModeOn: autovalidation,
                                      readOnly: true,
                                      filled: true,
                                      fillColor:
                                          colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                ],
                              ),
                              24.ph,
                              Row(
                                children: [
                                  Expanded(
                                    child: AppTextFormField(
                                      isParentField: true,
                                      prefixIconConstraints: BoxConstraints(
                                        minHeight: 20.h,
                                        minWidth: 48.h,
                                      ),
                                      prefixIcon: SvgPicture.asset(
                                          AppImages.calendarIc),
                                      readOnly: true,
                                      controller: projectDeadlineController,
                                      hintText: AppStrings.projectDeadlineEnter,
                                      titleText: AppStrings.projectDeadline,
                                      validator: (value) {
                                        return Validator.emptyValidator(
                                            value,
                                            ValidationMsg.plsEntervalid(
                                                "project deadline"));
                                      },
                                      isAutovalidateModeOn: autovalidation,
                                      onTap: () async {
                                        DateTime? pickedDate =
                                            await showDatePicker(
                                          context: context,
                                          initialEntryMode:
                                              DatePickerEntryMode.calendarOnly,
                                          initialDate: projectDeadlineDate ??
                                              DateTime.now(),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime(2100),
                                        );
                                        if (pickedDate != null) {
                                          if (responseDeadlineDate != null &&
                                              pickedDate.isBefore(
                                                  responseDeadlineDate!)) {
                                            if (mounted) {
                                              CustomToast.show(
                                                context: context,
                                                message: ValidationMsg
                                                    .projectDeadlineAfterResponse,
                                              );
                                            }
                                            return;
                                          }
                                          projectDeadlineDate = pickedDate;
                                          projectDeadlineController.text =
                                              DateFormat('d MMMM yyyy')
                                                  .format(pickedDate);
                                        }
                                      },
                                    ),
                                  ),
                                  14.pw,
                                  Expanded(
                                    child: AppTextFormField(
                                      isParentField: true,
                                      prefixIconConstraints: BoxConstraints(
                                        minHeight: 20.h,
                                        minWidth: 48.h,
                                      ),
                                      prefixIcon: SvgPicture.asset(
                                          AppImages.calendarIc),
                                      readOnly: true,
                                      controller: responseDeadlineController,
                                      hintText:
                                          AppStrings.responseDeadlineEnter,
                                      titleText:
                                          AppStrings.responseDeadlineAsterisk,
                                      keyboardType: TextInputType.number,
                                      validator: (value) {
                                        return Validator.emptyValidator(
                                            value,
                                            ValidationMsg.plsEntervalid(
                                                "response deadline"));
                                      },
                                      onTap: () async {
                                        DateTime? pickedDate =
                                            await showDatePicker(
                                          context: context,
                                          initialEntryMode:
                                              DatePickerEntryMode.calendarOnly,
                                          initialDate: responseDeadlineDate ??
                                              DateTime.now(),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime(2100),
                                        );
                                        if (pickedDate != null) {
                                          if (projectDeadlineDate != null &&
                                              pickedDate.isAfter(
                                                  projectDeadlineDate!)) {
                                            if (mounted) {
                                              CustomToast.show(
                                                context: context,
                                                message: ValidationMsg
                                                    .responseBeforeProjectDeadline,
                                              );
                                            }
                                            return;
                                          }
                                          responseDeadlineDate = pickedDate;
                                          responseDeadlineController.text =
                                              DateFormat('d MMMM yyyy')
                                                  .format(pickedDate);
                                        }
                                      },
                                      isAutovalidateModeOn: autovalidation,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
