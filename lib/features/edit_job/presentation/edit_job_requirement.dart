import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:textfield_tags/textfield_tags.dart' as tag_textfield;
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/list_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../utils/validations.dart';
import '../../../widgets/chips.dart';
import '../../../widgets/custom_dropdown.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../post_job/models/job_post_model.dart';
import '../../post_job/presentation/widgets/post_job_page_frame.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_state.dart';
import '../bloc/edit_job_cubit.dart';

class EditJobRequirement extends StatefulWidget {
  const EditJobRequirement({super.key});

  @override
  State<EditJobRequirement> createState() => _EditJobRequirementState();
}

class _EditJobRequirementState extends State<EditJobRequirement> {
  late tag_textfield.TextfieldTagsController tagController;
  late TextEditingController titleController;
  late TextEditingController requirementController;
  DropdownData? selectedCategory;
  List<String> initialTags = [];
  bool autovalidation = false;
  final formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    titleController = TextEditingController();
    requirementController = TextEditingController();
    tagController = tag_textfield.TextfieldTagsController();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<EditJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      titleController.text = jobPostModel.title ?? '';
      requirementController.text = jobPostModel.requirement ?? '';
      final tags = jobPostModel.jobTags;
      if (tags != null && tags.isNotEmpty) {
        for (var element in tags) {
          tagController.addTag = element;
        }
      }

      final state = context.read<StaticDataDropdownBloc>().state;
      if (state is StaticDataDropdownSuccessState) {
        final projectType = state.dropDownResponseModel?.projectType;
        final jobCategoryId = jobPostModel.jobCategory;
        if (jobCategoryId != null) {
          selectedCategory =
              projectType?.firstWhereOrNull((e) => e == jobCategoryId);
        }
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocListener<EditJobCubit, EditJobState>(
        listener: (context, state) {
          if (state is EditJobLoadingState) {
            Dialogs.showOnlyLoader(context);
          }
          if (state is EditJobSuccessState) {
            context.pop();
            context.pop();
            HiveStorageHelper.deleteKeyInBox(
                boxName: HiveBoxName.user, key: HiveKeys.editJobData);
            CustomToast.show(
                context: context,
                message: AppStrings.jobEditedSuccessfully,
                isSuccess: true);
          }
          if (state is EditJobErrorState) {
            context.pop();
            CustomToast.show(context: context, message: state.errorMessage);
          }
        },
        child: Padding(
          padding: Responsive.isDesktop(context)
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              if (!!Responsive.isDesktop(context)) ...[
                Padding(
                  padding: EdgeInsets.only(top: 40.h, left: 80.w),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: CustomBackButtonArrow(
                      onTap: () => context.pop(),
                    ),
                  ),
                ),
              ],
              Expanded(
                child: PostJobPageFrame(
                  onCancelPressed: () => context.pop(),
                  onBackPressed: () => context.pop(),
                  isEditing: true,
                  formKey: formGlobalKey,
                  needContinueBtn: true,
                  nextBtnText: AppStrings.save,
                  // continueBtnText: AppStrings.save,
                  onNextPressed: () async {
                    if (formGlobalKey.currentState!.validate()) {
                      final cubit = context.read<EditJobCubit>();
                      final jobPostModel = cubit.loadJobPostFromHive();
                      if (requirementController.text ==
                          jobPostModel?.requirement) {
                        CustomToast.show(
                            context: context,
                            message: AppStrings.noChangesMade);
                        return;
                      }

                      cubit.updateJobDetails(
                        jobModel: JobPostModel(
                          id: jobPostModel?.id,
                          requirement: requirementController.text.trim(),
                        ),
                      );
                    } else {
                      autovalidation = true;
                      setState(() {});
                    }
                  },
                  children: [
                    !Responsive.isDesktop(context)
                        ? AppTextFormField(
                            isParentField: true,
                            maxLength: 250,
                            controller: titleController,
                            hintText: AppStrings.enterProjectTitle,
                            titleText: AppStrings.title,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp("[a-zA-Z0-9 ]")),
                            ],
                            validator: (String? value) {
                              return Validator.emptyValidator(
                                  value, ValidationMsg.plsEntervalid("title"));
                            },
                            isAutovalidateModeOn: autovalidation,
                            readOnly: true,
                            filled: true,
                            fillColor: colorScheme.lightGreyD9D9D9,
                          )
                        : Row(children: [
                            Expanded(
                              child: AppTextFormField(
                                isParentField: true,
                                maxLength: 250,
                                controller: titleController,
                                hintText: AppStrings.enterProjectTitle,
                                titleText: AppStrings.title,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp("[a-zA-Z0-9 ]")),
                                ],
                                validator: (String? value) {
                                  return Validator.emptyValidator(value,
                                      ValidationMsg.plsEntervalid("title"));
                                },
                                isAutovalidateModeOn: autovalidation,
                                readOnly: true,
                                filled: true,
                                fillColor: colorScheme.lightGreyD9D9D9,
                              ),
                            ),
                            20.pw,
                            Expanded(
                              child: BlocConsumer<StaticDataDropdownBloc,
                                  StaticDataDropdownState>(
                                listener: (context, state) {
                                  if (state is StaticDataDropdownSuccessState) {
                                    final projectType = state
                                        .dropDownResponseModel?.projectType;
                                    final editJobState =
                                        context.read<EditJobCubit>().state;
                                    if (editJobState is EditJobInitialState) {
                                      final jobCategoryId =
                                          editJobState.jobPostModel.jobCategory;
                                      if (jobCategoryId != null) {
                                        setState(() {
                                          selectedCategory =
                                              projectType?.firstWhereOrNull(
                                                  (e) => e == jobCategoryId);
                                        });
                                      }
                                    }
                                  }
                                },
                                builder: (context, state) {
                                  List<DropdownData>? projectType = [];
                                  if (state is StaticDataDropdownSuccessState) {
                                    projectType.addAll(state
                                            .dropDownResponseModel
                                            ?.projectType ??
                                        []);
                                  }
                                  return CustomDropDownWidget<DropdownData>(
                                    isParentField: true,
                                    isLoading:
                                        state is StaticDataDropdownLoadingState,
                                    isError:
                                        state is StaticDataDropdownErrorState,
                                    hintText: AppStrings.selectCategory,
                                    titleText: AppStrings.category,
                                    selectedValue: selectedCategory?.name,
                                    items: projectType
                                        .map((item) => DropdownMenuItem(
                                            value: item,
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                TextTitle18And14(
                                                  item.name ?? "",
                                                ),
                                                if (item.id ==
                                                    selectedCategory?.id)
                                                  const Icon(Icons.check),
                                              ],
                                            )))
                                        .toList(),
                                    // Disable onChanged to make it readOnly
                                    onChanged: (value) {
                                      selectedCategory = value;
                                      setState(() {});
                                    },
                                    value: selectedCategory,
                                    readOnly: true,
                                  );
                                },
                              ),
                            ),
                          ]),
                    !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                    AppTextFormField(
                      isParentField: true,
                      controller: requirementController,
                      titleText: AppStrings.requirementAsterisk,
                      hintText: AppStrings.addRequirement,
                      maxLines: 3,
                      keyboardType: TextInputType.multiline,
                      textInputAction: TextInputAction.newline,
                      validator: (String? value) {
                        return Validator.emptyValidator(
                          value,
                          ValidationMsg.plsEntervalid("requirement"),
                        );
                      },
                      isAutovalidateModeOn: autovalidation,
                    ),
                    if (!Responsive.isDesktop(context)) ...[
                      !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                      BlocConsumer<StaticDataDropdownBloc,
                          StaticDataDropdownState>(
                        listener: (context, state) {
                          if (state is StaticDataDropdownSuccessState) {
                            final projectType =
                                state.dropDownResponseModel?.projectType;
                            final editJobState =
                                context.read<EditJobCubit>().state;
                            if (editJobState is EditJobInitialState) {
                              final jobCategoryId =
                                  editJobState.jobPostModel.jobCategory;
                              if (jobCategoryId != null) {
                                setState(() {
                                  selectedCategory =
                                      projectType?.firstWhereOrNull(
                                          (e) => e == jobCategoryId);
                                });
                              }
                            }
                          }
                        },
                        builder: (context, state) {
                          List<DropdownData>? projectType = [];
                          if (state is StaticDataDropdownSuccessState) {
                            projectType.addAll(
                                state.dropDownResponseModel?.projectType ?? []);
                          }
                          return CustomDropDownWidget<DropdownData>(
                            isParentField: true,
                            isLoading: state is StaticDataDropdownLoadingState,
                            isError: state is StaticDataDropdownErrorState,
                            hintText: AppStrings.selectCategory,
                            titleText: AppStrings.category,
                            selectedValue: selectedCategory?.name,
                            items: projectType
                                .map((item) => DropdownMenuItem(
                                    value: item,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TextTitle18And14(
                                          item.name ?? "",
                                        ),
                                        if (item.id == selectedCategory?.id)
                                          const Icon(Icons.check),
                                      ],
                                    )))
                                .toList(),
                            // Disable onChanged to make it readOnly
                            onChanged: (value) {
                              selectedCategory = value;
                              setState(() {});
                            },
                            value: selectedCategory,
                            readOnly: true,
                          );
                        },
                      ),
                    ],
                    !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                    tag_textfield.TextFieldTags(
                      textfieldTagsController: tagController,
                      textSeparators: const [',', ' '],
                      initialTags: initialTags,
                      letterCase: tag_textfield.LetterCase.small,
                      validator: (String tag) {
                        if (tagController.getTags != null &&
                            tagController.getTags!.isNotEmpty) {
                          if (tagController.getTags!.contains(tag)) {
                            return 'You already entered that';
                          }
                          if (tagController.getTags!.length > 9) {
                            return 'Max 10 tags are allowed';
                          }
                        }
                        return null;
                      },
                      inputfieldBuilder:
                          (context, tec, fn, error, onChanged, onSubmitted) {
                        return ((context, sc, tags, onTagDelete) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text24And20SemiBold(AppStrings.keywords),
                              16.ph,
                              // AppTextFormField(
                              //   readOnly: true,
                              //   isParentField: true,
                              //   controller: tec,
                              //   focusNode: fn,
                              //   isenabled: tags.length < 10,
                              //   hintText: AppStrings.addKeywordsRequirement,
                              //   titleText: AppStrings.addKeywords,
                              //   indicatorText: AppStrings.upTo10,
                              //   maxLength: 50,
                              //   inputFormatters: [
                              //     FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,_]")),
                              //   ],
                              //   onChanged: (value) {
                              //     onChanged!(value);
                              //   },
                              //   onSubmitted: (value) {
                              //     onSubmitted!(value);
                              //     fn.requestFocus();
                              //   },
                              // ),
                              // if (tags.isNotEmpty) 8.ph,
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.w, vertical: 16.h),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: colorScheme.lightGreyD9D9D9,
                                ),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: ChipsWidget(
                                    items: tags,
                                    needRemovel: false,
                                  ),
                                ),
                              ),
                              96.ph,
                            ],
                          );
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
