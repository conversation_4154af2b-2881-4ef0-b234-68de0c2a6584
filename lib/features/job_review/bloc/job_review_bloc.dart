import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';

part 'job_review_state.dart';

class JobReviewBloc extends Cubit<JobReviewState> {
  JobReviewBloc() : super(JobReviewInitialState());

  Future<void> postJobReview({
    required int jobId,
    required int rating,
    required String feedback,
  }) async {
    emit(JobReviewLoadingState());

    try {
      final response = await ApiService.instance.postJobReview(jobId: jobId, rating: rating, feedback: feedback);

      if (response.success) {
        emit(JobReviewSuccessState());
      } else {
        emit(JobReviewErrorState(errorMessage: response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(JobReviewErrorState(errorMessage: AppStrings.genericErrorMsg));
    }
  }
}
