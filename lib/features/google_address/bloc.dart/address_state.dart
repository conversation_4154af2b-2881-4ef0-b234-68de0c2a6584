import 'package:equatable/equatable.dart';
import 'package:the_voice_directory_flutter/features/google_address/model/address_response_model.dart';

abstract class AddressState extends Equatable {}

class AddressInitState extends AddressState {
  @override
  List<Object> get props => [];
}

class AddressLoadingState extends AddressState {
  @override
  List<Object> get props => [];
}

class AddressSubmitSuccessState extends AddressState {
  final List<AddressResponseModel> addressList;
  AddressSubmitSuccessState(this.addressList);
  
  @override
  List<Object> get props => [addressList];
}

class AddressSubmitErrorState extends AddressState {
  final String errorMsg;
  AddressSubmitErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
