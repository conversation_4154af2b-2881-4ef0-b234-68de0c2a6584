import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/google_address/bloc.dart/address_state.dart';
import 'package:the_voice_directory_flutter/features/google_address/model/address_response_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class AddressBloc extends Cubit<AddressState> {
  AddressBloc() : super(AddressInitState());

  void getAddressApi({required String? address}) async {
    emit(AddressLoadingState());
    try {
      final response = await ApiService.instance.googleAddress(address);
      if (response.success) {
        if (response.data != null) {          
          try {
            final addressModel = AddressResponseModel.fromJson(response.data);
            emit(AddressSubmitSuccessState([addressModel]));
          } catch (parseError) {
            emit(AddressSubmitErrorState("Error parsing address data"));
          }
        } else {
          emit(AddressSubmitErrorState("No data returned from API"));
        }
      } else {
        emit(AddressSubmitErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(AddressSubmitErrorState(AppStrings.genericErrorMsg));
    }
  }
}
