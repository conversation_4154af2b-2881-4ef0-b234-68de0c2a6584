class AddressResponseModel {
  List<Results>? results;

  AddressResponseModel({this.results});

  AddressResponseModel.fromJson(Map<String, dynamic> json) {
    if (json['results'] != null) {
      results = <Results>[];
      json['results'].forEach((v) {
        results!.add(Results.fromJson(v));
      });
    }
  }
}

class Results {
  String? formattedAddress;
  double? latitude;
  double? longitude;
  String? placeId;
  List<AddressComponents>? addressComponents;

  Results(
      {this.formattedAddress,
      this.latitude,
      this.longitude,
      this.placeId,
      this.addressComponents});

  Results.fromJson(Map<String, dynamic> json) {
    formattedAddress = json['formatted_address'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    placeId = json['place_id'];
    if (json['address_components'] != null) {
      addressComponents = <AddressComponents>[];
      json['address_components'].forEach((v) {
        addressComponents!.add(AddressComponents.fromJson(v));
      });
    }
  }
}

class AddressComponents {
  String? longName;
  String? shortName;
  List<String>? types;

  AddressComponents({this.longName, this.shortName, this.types});

  AddressComponents.fromJson(Map<String, dynamic> json) {
    longName = json['long_name'];
    shortName = json['short_name'];
    types = json['types'].cast<String>();
  }
}
