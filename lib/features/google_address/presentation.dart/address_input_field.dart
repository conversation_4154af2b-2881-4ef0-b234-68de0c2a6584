import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/google_address/bloc.dart/addess_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_address/bloc.dart/address_state.dart';
import 'package:the_voice_directory_flutter/features/google_address/model/address_response_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';

class AddressInputField extends StatefulWidget {
  final TextEditingController? addressController;
  final TextEditingController? cityController;
  final TextEditingController? stateController;
  final TextEditingController? postalController;
  final TextEditingController? countryController;
  final ValueChanged<Map<String, dynamic>>? onLocationSelected;
  final String? hintText;
  final String? titleText;
  final bool isAutovalidateModeOn;
  final String? Function(String?)? validator;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool showFullAddress;
  final Color? hintTextColor;
  final ValueChanged<String>? onChanged;
  final bool isTitle;
  final int? maxLines;

  const AddressInputField({
    super.key,
    this.addressController,
    this.cityController,
    this.stateController,
    this.postalController,
    this.countryController,
    this.onLocationSelected,
    this.hintText,
    this.titleText,
    this.isAutovalidateModeOn = false,
    this.validator,
    this.prefixIcon,
    this.suffixIcon,
    this.showFullAddress = false,
    this.hintTextColor,
    this.onChanged,
    this.isTitle = true,
    this.maxLines,
  });

  @override
  State<AddressInputField> createState() => _AddressInputFieldState();
}

class _AddressInputFieldState extends State<AddressInputField> {
  final AddressBloc _addressBloc = AddressBloc();
  final GlobalKey _addressKey = GlobalKey();
  static const int _minSearchLength = 3;

  @override
  void dispose() {
    _addressBloc.close();
    super.dispose();
  }

  Future<List<Results>> _getSuggestions(String pattern) async {
    if (pattern.length < _minSearchLength) {
      return [];
    }
    
    final completer = Completer<List<Results>>();
    
    final subscription = _addressBloc.stream.listen((state) {
      if (state is AddressSubmitSuccessState) {
        final suggestions = <Results>[];
        for (var addressModel in state.addressList) {
          if (addressModel.results != null) {
            suggestions.addAll(addressModel.results!);
          }
        }
        completer.complete(suggestions);
      } else if (state is AddressSubmitErrorState) {
        completer.complete([]);
      }
    });
    
    _addressBloc.getAddressApi(address: pattern);
    
    final result = await completer.future;
    subscription.cancel();
    return result;
  }
  
  void _extractAddressComponents(Results result) {
    if (!mounted) return;
      _clearControllers();
    
    if (result.addressComponents == null) {
      return;
    }
    
    String? city, state, country, postalCode;
    
    for (var component in result.addressComponents!) {
      if (component.types == null || component.types!.isEmpty) continue;
      
      if (_isCity(component)) {
        city = component.longName;
      } else if (_isState(component)) {
        state = component.longName;
      } else if (_isCountry(component)) {
        country = component.longName;
      } else if (_isPostalCode(component)) {
        postalCode = component.longName;
      }
    }
   _updateControllers(city, state, postalCode, country);
   _updateLocationCoordinates(result);
  }
  
  bool _isCity(AddressComponents component) {
    return component.types!.contains("locality") || 
           component.types!.contains("administrative_area_level_3");
  }
  
  bool _isState(AddressComponents component) {
    return component.types!.contains("administrative_area_level_1");
  }
  
  bool _isCountry(AddressComponents component) {
    return component.types!.contains("country");
  }
  
  bool _isPostalCode(AddressComponents component) {
    return component.types!.contains("postal_code") || 
           component.types!.contains("zip_code");
  }
  
  void _clearControllers() {
    widget.cityController?.clear();
    widget.stateController?.clear();
    widget.postalController?.clear();
    widget.countryController?.clear();
  }
  
  void _updateControllers(String? city, String? state, String? postalCode, String? country) {
    if (city != null && city.isNotEmpty) {
      widget.cityController?.text = city;
    }
    if (state != null && state.isNotEmpty) {
      widget.stateController?.text = state;
    }
    if (postalCode != null && postalCode.isNotEmpty) {
      widget.postalController?.text = postalCode;
    }
    if (country != null && country.isNotEmpty) {
      widget.countryController?.text = country;
    }
  }
  
  void _updateLocationCoordinates(Results result) {
    if (result.latitude != null && result.longitude != null && widget.onLocationSelected != null) {
      widget.onLocationSelected!({
        'location': {
          'lat': result.latitude,
          'lng': result.longitude
        }
      });
    }
  }

  String _extractStreetAddressOnly(String fullAddress) {
    // Split the address by commas
    List<String> parts = fullAddress.split(',');
    
    if (parts.length <= 1) {
      return fullAddress;
    }
    // Remove the last 2-3 parts which typically contain city, state, and postal code
    int partsToKeep = parts.length > 3 ? parts.length - 3 : 1;

    return parts.sublist(0, partsToKeep).join(',').trim();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return TypeAheadField<Results>(
      key: _addressKey,
      controller: widget.addressController,
      debounceDuration: const Duration(milliseconds: 500),
      decorationBuilder: _buildDecoration(colorScheme),
      suggestionsCallback: _getSuggestions,
      hideOnLoading: false,
      hideOnEmpty: false,
      onSelected: _onAddressSelected,
      builder: _buildTextField,
      loadingBuilder: _buildLoadingIndicator,
      emptyBuilder: _buildEmptyState,
      itemBuilder: _buildSuggestionItem,
    );
  }
  
  Widget Function(BuildContext, Widget) _buildDecoration(ColorScheme colorScheme) {
    return (context, child) => Material(
      color: colorScheme.white,
      elevation: 4.0,
      borderRadius: BorderRadius.circular(12.r),
      shadowColor: Colors.black26,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: child,
      ),
    );
  }
  
  void _onAddressSelected(Results suggestion) {    
    if (widget.showFullAddress) {
      widget.addressController?.text = suggestion.formattedAddress ?? "";
    } else {
      widget.addressController?.text = _extractStreetAddressOnly(suggestion.formattedAddress ?? "");
    }
    _extractAddressComponents(suggestion);
    FocusScope.of(context).unfocus();
  }
  
  Widget _buildTextField(BuildContext context, TextEditingController controller, FocusNode focusNode) {
    return AppTextFormField(
      controller: controller,
      focusNode: focusNode,
      hintText: widget.hintText ?? AppStrings.enterAddress,
      hintTextColor: widget.hintTextColor,
      titleText: widget.isTitle ? (widget.titleText ?? AppStrings.streetAddress) : null,
      validator: widget.validator,
      isAutovalidateModeOn: widget.isAutovalidateModeOn,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      maxLines: widget.maxLines,
      onChanged: (text) {
        if (text.isEmpty) {
          _clearControllers();
        }
        if (widget.onChanged != null) {
          widget.onChanged!(text);
        }
      },
    );
  }
  
  Widget _buildLoadingIndicator(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.white,
      height: 100.h,
      child: const Center(child: Loader()),
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    final bool shouldShowNoResults = (widget.addressController?.text.length ?? 0) >= _minSearchLength;
    
    if (!shouldShowNoResults) {
      return const SizedBox.shrink();
    }
    
    return Container(
      color: Theme.of(context).colorScheme.white,
      height: 50.h,
      child: Center(
        child: TextTitle14(
          "No results found",
          style: TextStyle(fontSize: 14.sp),
        ),
      ),
    );
  }
  
  Widget _buildSuggestionItem(BuildContext context, Results suggestion) {
    return Container(
      color: Theme.of(context).colorScheme.white,
      child: ListTile(
        title: TextTitle14(
          suggestion.formattedAddress ?? "No address",
          style: TextStyle(fontSize: 14.sp),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        dense: true,
        onTap: null,
      ),
    );
  }
}
