import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class CalendarViewDropdown extends StatefulWidget {
  static const _itemPadding = EdgeInsets.symmetric(horizontal: 8, vertical: 4);
  static const _borderRadius = 4.0;
  final CalendarFormat currentFormat;
  final Function(CalendarFormat) onFormatChanged;

  const CalendarViewDropdown({
    super.key,
    required this.currentFormat,
    required this.onFormatChanged,
  });

  @override
  State<CalendarViewDropdown> createState() => _CalendarViewDropdownState();
}

class _CalendarViewDropdownState extends State<CalendarViewDropdown> {
  bool _isOpen = false;

  String _getFormatText(CalendarFormat format) => switch (format) {
        CalendarFormat.month => 'Month',
        CalendarFormat.week => 'Week',
        _ => 'Month', // Default case
      };

  Widget _buildDropdownButton(BuildContext context) => Container(
        padding: EdgeInsets.symmetric(
            horizontal: !Responsive.isDesktop(context) ? 8.w : 20.w,
            vertical: 8.h),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.lightGreyD9D9D9),
          borderRadius: BorderRadius.circular(CalendarViewDropdown._borderRadius.r),
        ),
        child: Row(
          children: [
            TextTitle14(_getFormatText(widget.currentFormat),
            fontWeight: FontWeight.w700),
                9.pw,
            Icon(
              _isOpen
                  ? Icons.keyboard_arrow_up_rounded
                  : Icons.keyboard_arrow_down_rounded,
              color: Theme.of(context).colorScheme.primaryGrey,
            ),
          ],
        ),
      );

  PopupMenuItem<CalendarFormat> _buildPopupMenuItem(
    BuildContext context,
    CalendarFormat format,
  ) =>
      PopupMenuItem<CalendarFormat>(
        value: format,
        child: Container(
          width: double.infinity,
          padding: CalendarViewDropdown._itemPadding.r,
          decoration: BoxDecoration(
            color: widget.currentFormat == format
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(CalendarViewDropdown._borderRadius.r),
          ),
          child: Row(
            children: [
              Icon( Icons.check,
                color: widget.currentFormat == format ? Theme.of(context).colorScheme.hyperlinkBlueColor : Colors.transparent,
              ),
              8.pw,
              TextTitle14(_getFormatText(format), fontWeight: FontWeight.w700),
            ],
          ),
        ),
      );

  List<PopupMenuEntry<CalendarFormat>> _buildDropdownItems(BuildContext context) =>
      [
        _buildPopupMenuItem(context, CalendarFormat.month),
        _buildPopupMenuItem(context, CalendarFormat.week),
      ];

  @override
  Widget build(BuildContext context) => PopupMenuButton<CalendarFormat>(
        color: Theme.of(context).colorScheme.white,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(CalendarViewDropdown._borderRadius.r * 2.r),
          side: BorderSide(color: Colors.grey[200]!),
        ),
        onSelected: (format) {
          widget.onFormatChanged(format);
          setState(() => _isOpen = false);
        },
        onCanceled: () => setState(() => _isOpen = false),
        itemBuilder: _buildDropdownItems,
        child: _buildDropdownButton(context),
        onOpened: () => setState(() => _isOpen = true),
      );
}
