import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/google_calender/data/request_model.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class AllEventsDialog extends StatelessWidget {
  final List<RequestModel> events;

  const AllEventsDialog({
    super.key,
    required this.events,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: Responsive.isDesktop(context) ? 500.w 
            : MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(bottom: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Row(
                children: [
                  const TextTitle18And14(AppStrings.allEvents,
                    style: TextStyle(fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  CommonCircleIcon(
                    iconPath: AppImages.closeIcon,
                    containerSize: 32.h,
                    iconSize: 32.h,
                    onTap: () {
                    context.pop();
                  },
                ),
                ],
              ),
            ),
            8.ph,
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: events.map((event) => Container(
                    margin: EdgeInsets.symmetric(vertical: 4.h),
                    padding: EdgeInsets.all(8.h),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 6.w,
                          height: 6.h,
                          margin: EdgeInsets.only(top: 6.h, right: 8.w),
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextTitle18And14(event.summary,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              8.ph,
                              RichText(
                                text: TextSpan(
                                  children: [
                                     TextSpan(
                                      text: 'Time : ',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context).colorScheme.black,
                                      ),
                                    ),
                                    TextSpan(
                                      text: TimeOfDay.fromDateTime(event.start).format(context),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    const TextSpan(
                                      text: ' - ',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    TextSpan(
                                      text: TimeOfDay.fromDateTime(event.end).format(context),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if(event.eventLocation.isNotEmpty)...[
                              8.ph,
                               RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'Location : ',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context).colorScheme.black,
                                      ),
                                    ),
                                    TextSpan(
                                      text: event.eventLocation,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ],
                              if(event.meetLink.isNotEmpty)...[
                              8.ph,
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Meet link : ',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Theme.of(context).colorScheme.black,
                                    ),
                                  ),
                                  Expanded(
                                    child: ClickableText(
                                      text: event.meetLink,
                                      fontSizeMobile: 16,
                                      fontSizeWeb: 16,
                                      color: Theme.of(context).colorScheme.hyperlinkBlueColor,
                                    ),
                                  ),
                                ],
                              ),
                              ]
                            ],
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
