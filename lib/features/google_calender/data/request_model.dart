class RequestModel {
  final String userId;
  final String summary;
  final DateTime start;
  final DateTime end;
  final String meetLink;
  final List<String> attendees;
  final String eventLocation;

  RequestModel({
    required this.userId,
    required this.summary,
    required this.start,
    required this.end,
    this.meetLink = '',
    this.attendees = const [],
    this.eventLocation = '',
  });

  factory RequestModel.fromJson(Map<String, dynamic> json, String userId) {
    return RequestModel(
      userId: userId,
      summary: json['summary'] ?? 'Busy',
      start: DateTime.parse(json['start']),
      end: DateTime.parse(json['end']),
      meetLink: json['meet_link'] ?? '',
      attendees: json['attendees'] != null 
          ? List<String>.from(json['attendees'])
          : [],
      eventLocation: json['event_location'] ?? '',
    );
  }

  // Add toJson method
  Map<String, dynamic> toJson() {
    return {
      'voice_id': userId,
      'summary': summary,
      'start': start.toIso8601String(),
      'end': end.toIso8601String(),
      'meet_link': meetLink,
      'attendees': attendees,
      'event_location': eventLocation,
    };
  }
}
