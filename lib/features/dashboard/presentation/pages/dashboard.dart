import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/chat/presentation/pages/chats_screen.dart';
import 'package:the_voice_directory_flutter/features/google_calender/presentation/google_calender_screen.dart';
import 'package:the_voice_directory_flutter/features/jobs/bloc/jobs_cubit.dart';
import '../../../../core/api/api_params.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../chat/bloc/chat_bloc.dart';
import '../../../chat/data/convo_model.dart';
import '../../../explore_artists/presentation/explore_artists_screen.dart';
import '../../../jobs/presentation/jobs.dart';
import '../../../notification/data/notification_response_model.dart';
import '../../bloc/dashboard_cubit.dart';
import '../../profile_section/account_screen.dart';
import '../../../common/user_data/data/user_data_model.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
@override
void initState() {
  super.initState();
  OneSignal.Notifications.removeClickListener(_handleNotificationOpened);
  // Notification Click Listener
  OneSignal.Notifications.addClickListener((event) {
    log('Notification clicked: ${event.notification.notificationId}');
    _handleNotificationOpened(event);
  });

  // Foreground Display Listener
  OneSignal.Notifications.addForegroundWillDisplayListener((event) {
    final additionalData = event.notification.additionalData;
    if (additionalData != null) {
      final notificationData = NotificationResponseModel.fromJson(additionalData);
      final groupId = notificationData.extraData?['group_id'];

      if (notificationData.module == AppStrings.chat && context.read<ChatBloc>().state.selectedChat == groupId) {
        event.preventDefault(); // prevent default display
        return;
      }
    }
    event.notification.display(); // display if not prevented
  });
}

Future<void> _handleNotificationOpened(OSNotificationClickEvent event) async {
  await Future.delayed(const Duration(milliseconds: 100));

  log('Notification opened: ${event.notification.notificationId}');

  final isUserLoggedIn = HiveStorageHelper.getData<bool>(
        HiveBoxName.user, HiveKeys.isUserLoggedIn,
      ) ?? false;

  if (!isUserLoggedIn) {
    log('User not logged in, skipping notification handling');
    return;
  }

  final additionalData = event.notification.additionalData;
  if (additionalData != null) {
    try {
      final notificationData = NotificationResponseModel.fromJson(additionalData);
      log('Notification data: $additionalData');
      _navigateToScreen(notificationData);
    } catch (e) {
      log('Error parsing notification data: $e');
    }
  }
}

void _navigateToScreen(NotificationResponseModel notificationData) {
  try {
    final extraData = notificationData.extraData ?? {};

    if (notificationData.module == AppStrings.chat &&
        context.read<ChatBloc>().state.selectedChat != extraData['group_id']) {
      NavigationServiceImpl.getInstance()!.doNavigation(
        context,
        routeName: RouteName.chat,
        pathParameters: {
          Params.jobName: extraData['job_name']?.toString() ?? '',
          Params.groupId: extraData['group_id']?.toString() ?? '',
          Params.jobId: extraData['job_id']?.toString() ?? '',
          Params.idFrom: extraData['id_to']?.toString() ?? '',
          Params.idTo: extraData['id_from']?.toString() ?? '',
          Params.myName: extraData['name']?.toString() ?? '',
          Params.myProfileImg: extraData['profile_img']?.toString() ?? '',
          Params.name: extraData['my_name']?.toString() ?? '',
          Params.profileImg: extraData['my_profile_img']?.toString() ?? '',
          Params.needBackBtn: 'true',
        },
      );
      context.read<ChatBloc>().updateChatState(
          isFromChatList: false,
          isJobChat: extraData['is_job_chat'] == 'true' ? true : false,
          selectedChat: extraData['group_id']?.toString() ?? '');
      return;
    }

    if (notificationData.routeName != null && notificationData.jobId != null) {
      NavigationServiceImpl.getInstance()!.doNavigation(
        context,
        routeName: notificationData.routeName!,
        pathParameters: {
          Params.id: notificationData.jobId.toString(),
          if (notificationData.module == 'Job Applied')
            Params.showApplicantTab: 'true',
        },
      );
    }
  } catch (e) {
    log('Error navigating based on notification: $e');
  }
}

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textStyle = theme.textTheme;
    final userData = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    final bool isClient = userData?.role == UserType.client;
    final bool isAncillary = userData?.role == UserType.ancillaryService;
    final bool isVoice = userData?.role == UserType.voice;
    
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        final dashboardCubit = context.read<DashboardCubit>();
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (_, __) async {
            dashboardCubit.handleWillPop();
          },
          child: Scaffold(
            body: PageView(
              clipBehavior: Clip.none,
              controller: dashboardCubit.pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
               const JobsScreen(),
                if (isVoice) ...[
                  const ExploreArtistsScreen(),
                  GoogleCalenderScreen(
                    events: const [], 
                    accessToken: HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.googleAccessToken) ?? '',
                  ),
                ] else if (isClient || isAncillary) ...[
                  const ExploreArtistsScreen(),
                  const SizedBox()
                ] else ...[
                  GoogleCalenderScreen(
                    events: const [], 
                    accessToken: HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.googleAccessToken) ?? '',
                  ),
                ],
                const ChatsScreen(),
                const AccountScreen(),
              ],
            ),
            bottomNavigationBar: !Responsive.isDesktop(context) ||
                    Responsive.isTab(context)
                ? SafeArea(
                  child: Material(
                      elevation: 8,
                      color: colorScheme.onPrimary,
                      child: Container(
                        height: 52.h,
                        margin: EdgeInsets.symmetric(
                          vertical: 9.h,
                          horizontal: 16.w,
                        ),
                        child: FittedBox(
                          child: GNav(
                            gap: 6.w,
                            padding: EdgeInsets.symmetric(
                              vertical: 9.h,
                              horizontal: 16.w,
                            ),
                            tabBorderRadius: 24.r,
                            tabBackgroundColor: colorScheme.primary,
                            color: colorScheme.primary,
                            activeColor: colorScheme.primary,
                            textStyle: textStyle.bodySmall?.copyWith(
                              overflow: TextOverflow.ellipsis,
                            ),
                            selectedIndex: state.selectedIndex,
                            onTabChange: (index) {
                              if (isVoice) {
                                context.read<JobsCubit>().clearSessionStorage();
                                dashboardCubit.changeTab(index);
                              } else {
                                if (state.hasClientTab && index == 2) {
                                  dashboardCubit.keepCurrentTab(index);
                                }
                                context.read<JobsCubit>().clearSessionStorage();
                                dashboardCubit.changeTab(index);
                              }
                              context.read<ChatBloc>().updateChatState(isFromChatList: false, selectedConvo: Convo(), selectedChat: "");
                              context.read<JobsCubit>().clearSessionStorage();
                              dashboardCubit.changeTab(index);
                            },
                            tabs: [
                              GButton(
                                icon: Icons.adb,
                                leading: SvgPicture.asset(
                                  AppImages.jobsIc,
                                  width: 24,
                                  height: 24,
                                ),
                                text: AppStrings.jobs,
                              ),
                              if (isVoice) ...[
                                GButton(
                                  icon: Icons.adb,
                                  leading: SvgPicture.asset(
                                    AppImages.voicesIc,
                                    width: 24,
                                    height: 24,
                                  ),
                                  text: AppStrings.explore,
                                ),
                                GButton(
                                  icon: Icons.adb,
                                  leading: SvgPicture.asset(
                                    AppImages.calendarIc,
                                    width: 24,
                                    height: 24,
                                  ),
                                  text: AppStrings.calendar,
                                ),
                              ] else ...[
                                GButton(
                                  icon: Icons.adb,
                                  leading: SvgPicture.asset(
                                    state.hasClientTab
                                        ? AppImages.voicesIc
                                        : AppImages.calendarIc,
                                    width: 24,
                                    height: 24,
                                  ),
                                  text: state.hasClientTab
                                      ? AppStrings.explore
                                      : AppStrings.calendar,
                                ),
                              ],
                              if (isClient || isAncillary)
                                GButton(
                                  icon: Icons.adb,
                                  leading: SvgPicture.asset(
                                    AppImages.addCircleIc,
                                    width: 24,
                                    height: 24,
                                  ),
                                  onPressed: () {
                                    HiveStorageHelper.deleteKeyInBox(boxName: HiveBoxName.user, key: HiveKeys.jobPostData);
                                    NavigationServiceImpl.getInstance()!.doNavigation(
                                      context,
                                      routeName: RouteName.postJob,
                                    );
                                  },
                                ),
                              GButton(
                                icon: Icons.adb,
                                leading: SvgPicture.asset(
                                  AppImages.chatIc,
                                  width: 24,
                                  height: 24,
                                ),
                                text: AppStrings.chats,
                              ),
                              GButton(
                                icon: Icons.adb,
                                leading: SvgPicture.asset(
                                  AppImages.profileIc,
                                  width: 24,
                                  height: 24,
                                ),
                                text: AppStrings.account,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                )
                : const SizedBox(),
          ),
        );
      },
    );
  }
}
