part of 'dashboard_cubit.dart';

class DashboardState extends Equatable {
  final int selectedIndex;
  final bool hasClientTab;

  const DashboardState({
    required this.selectedIndex,
    required this.hasClientTab,
  });

  DashboardState copyWith({
    int? selectedIndex,
    bool? hasClientTab,
  }) {
    return DashboardState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
      hasClientTab: hasClientTab ?? this.hasClientTab,
    );
  }

  @override
  List<Object> get props => [
        selectedIndex,
        hasClientTab,
      ];
}
