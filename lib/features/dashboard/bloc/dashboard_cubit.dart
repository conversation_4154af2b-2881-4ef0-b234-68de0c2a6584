import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final PageController pageController = PageController();

  DashboardCubit({required bool hasClientTab})
      : super(DashboardState(selectedIndex: 0, hasClientTab: hasClientTab));

  void changeTab(int newIndex) {
    if (state.selectedIndex != newIndex) {
      pageController.jumpToPage(newIndex);
      emit(state.copyWith(selectedIndex: newIndex));
    }
  }

  void keepCurrentTab(int newIndex) async {
    final oldIndex = state.selectedIndex;
    emit(state.copyWith(selectedIndex: newIndex));
    await Future.delayed(const Duration(milliseconds: 100));
    emit(state.copyWith(selectedIndex: oldIndex));
  }

  void handleWillPop() {
    if (state.selectedIndex != 0) {
      changeTab(0);
    }
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
