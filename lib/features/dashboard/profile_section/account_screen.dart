import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/create_profile/bloc/logout_bloc.dart';
import 'package:the_voice_directory_flutter/features/delete_account/bloc/delete_account_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../utils/custom_toast.dart';
import '../../../utils/environment_config.dart';
import '../../../widgets/common_app_bar.dart';
import '../../../widgets/dialogs.dart';
import '../../create_profile/bloc/logout_state.dart';
import '../../delete_account/bloc/delete_account_bloc.dart';

class AccountScreen extends StatefulWidget {
  const AccountScreen({super.key});

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen> {
  @override
  void initState() {
    context.read<UserBloc>().getUserData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      appBar: !Responsive.isDesktop(context) 
          ? CommonAppBar(
              title: AppStrings.profile,
            )
          : null,
      body: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        if (state is UserLoadingState) {
          return const Center(child: CircularProgressIndicator());
        }
        if (state is UserErrorState) {
          return ErrorScreen(
            onRetry: () {
              context.read<UserBloc>().getUserData();
            },
          );
        }
        if (state is UserSuccessState) {
          bool isClient = (state.userDataModel.role == UserType.client);
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        8.ph,
                        !Responsive.isDesktop(context)
                            ? myProfile(
                                context,
                                theme: theme,
                                colorScheme: colorScheme,
                                userDataModel: state.userDataModel,
                                companyName: isClient
                                    ? state.userDataModel.company ?? ""
                                    : state.userDataModel.role == UserType.ancillaryService
                                        ? state.userDataModel.services!.map((e) => e.name).join(" • ")
                                        : state.userDataModel.email ?? "",
                              )
                            : Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 80.h,),
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: myProfile(
                                    context,
                                    theme: theme,
                                    colorScheme: colorScheme,
                                    userDataModel: state.userDataModel,
                                    companyName: isClient
                                        ? state.userDataModel.company ?? ""
                                        : state.userDataModel.role == UserType.ancillaryService
                                            ? state.userDataModel.services!.map((e) => e.name).join(" • ")
                                            : state.userDataModel.email ?? "",
                                  ),
                                ),
                              ),
                      ]),
                ),
              ),
            ],
          );
        }
        return const SizedBox();
      }
      ),
    );
  }

  Widget myProfile(BuildContext context,
      {required ThemeData theme,
      required ColorScheme colorScheme,
      required UserDataModel userDataModel,
      required String companyName}) {
    return MultiBlocListener(
      listeners: [
        BlocListener<LogoutBloc, LogoutState>(
          listener: (_, state) {
            if (state is LogoutLoadingState) {
              Dialogs.showOnlyLoader(context);
            }
            if (state is LogoutSuccessState) {
              context.pop();
              CustomToast.show(
                  context: context,
                  isSuccess: true,
                  needShellNavigatorContext: true,
                  message: "You have successfully logged out.");
              NavigationServiceImpl.getInstance()!.doLogout();
            }
            if (state is LogoutErrorState) {
              context.pop();
              CustomToast.show(context: context, message: state.errorMsg);
            }
          },
        ),
        BlocListener<DeleteAccountBloc, DeleteAccountState>(
          listener: (_, state) {
            if (state is DeleteAccountLoadingState) {
              Dialogs.showOnlyLoader(context);
            }
            if (state is DeleteAccountSuccessState) {
              context.pop();
              CustomToast.show(
                  context: context,
                  isSuccess: true,
                  needShellNavigatorContext: true,
                  message: "Account deleted successfully");
              NavigationServiceImpl.getInstance()!.doLogout();
            }
            if (state is DeleteAccountErrorState) {
              context.pop();
              CustomToast.show(context: context, message: state.errorMsg);
            }
          },
        ),
      ],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              hoverColor: Colors.transparent,
              onTap: () {
                NavigationServiceImpl.getInstance()!.doNavigation(
                  context,
                  routeName: RouteName.profile,
                  pathParameters: {Params.id: userDataModel.id.toString()},
                );
              },
              child: CommonShadowContainer(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            CircleAvatar(
                              radius: 30.r,
                              backgroundColor: colorScheme.blackE8E8E8,
                              backgroundImage: userDataModel
                                          .profilePic?.isNotEmpty ==
                                      true
                                  ? Uri.parse(userDataModel.profilePic ?? '')
                                          .isAbsolute
                                      ? NetworkImage(userDataModel.profilePic ?? '')
                                      : NetworkImage(
                                          "${EnvironmentConfig.imageBaseUrl}${userDataModel.profilePic}")
                                  : null,
                              child:
                                  userDataModel.profilePic?.isNotEmpty == true
                                      ? null
                                      : SvgPicture.asset(
                                          "assets/images/user_icon.svg"),
                            ),
                            10.pw,
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  TextTitle18And14(
                                    "${userDataModel.firstName.toString()} ${userDataModel.lastName.toString()}",
                                    style: theme.textTheme.titleLarge!
                                        .copyWith(fontWeight: FontWeight.w700),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  TextTitle14(
                                    companyName,
                                    style: theme.textTheme.titleSmall!.copyWith(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          size: 20,
                          color: colorScheme.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Responsive.isDesktop(context) ? 24.ph : 12.ph,
            // CommonShadowContainer(
            //     spreadRadius: 0.1.r,
            //     blurRadius: 5.r,
            //     child: Padding(
            //       padding: const EdgeInsets.all(17.0),
            //       child: Row(
            //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //         children: [
            //           SvgPicture.asset(
            //             AppImages.cardsIcon,
            //             height: 20.h,
            //             width: 20.w,
            //           ),
            //           8.pw,
            //           const TextTitle18And14(
            //             AppStrings.accountDetails,
            //             fontWeight: FontWeight.w600,
            //             fontFamily: 'NotoSans-SemiBold',
            //           ),
            //           const Spacer(),
            //           Icon(
            //             Icons.arrow_forward_ios,
            //             size: 15,
            //             color: colorScheme.black,
            //           ),
            //         ],
            //       ),
            //     )),
            // Responsive.isDesktop(context) ? 24.ph : 12.ph,
            // CommonShadowContainer(
            //     spreadRadius: 0.1.r,
            //     blurRadius: 5.r,
            //     child: Padding(
            //       padding: const EdgeInsets.all(17.0),
            //       child: Row(
            //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //         children: [
            //           SvgPicture.asset(
            //             AppImages.billingIcon,
            //             height: 20.h,
            //             width: 20.w,
            //           ),
            //           8.pw,
            //           const TextTitle18And14(
            //             AppStrings.billingHistory,
            //             fontWeight: FontWeight.w600,
            //             fontFamily: 'NotoSans-SemiBold',
            //           ),
            //           const Spacer(),
            //           Icon(
            //             Icons.arrow_forward_ios,
            //             size: 15,
            //             color: colorScheme.black,
            //           ),
            //         ],
            //       ),
            //     )),
            Responsive.isDesktop(context) ? 24.ph : 12.ph,
            InkWell(
              hoverColor: Colors.transparent,
              onTap: () {
              NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.reviews,
              pathParameters: {Params.id: userDataModel.id.toString()});
               },
              child: CommonShadowContainer(
                  spreadRadius: 0.1.r,
                  blurRadius: 5.r,
                  child: Padding(
                    padding: const EdgeInsets.all(17.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SvgPicture.asset(
                          AppImages.starIcon,
                          height: 20.h,
                          width: 20.w,
                        ),
                        8.pw,
                        const TextTitle18And14(
                          AppStrings.reviews,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'NotoSans-SemiBold',
                        ),
                        const Spacer(),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 15,
                          color: colorScheme.black,
                        ),
                      ],
                    ),
                  )),
            ),
            Responsive.isDesktop(context) ? 24.ph : 12.ph,
            InkWell(
             onTap: () {
               context.pushNamed(RouteName.termsAndConditions);
             },
              child: CommonShadowContainer(
                  spreadRadius: 0.1.r,
                  blurRadius: 5.r,
                  child: Padding(
                    padding: const EdgeInsets.all(17.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SvgPicture.asset(
                          AppImages.termsConditionIcon,
                          height: 20.h,
                          width: 20.w,
                        ),
                        8.pw,
                        const TextTitle18And14(
                          AppStrings.termsAndConditions,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'NotoSans-SemiBold',
                        ),
                        const Spacer(),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 15,
                          color: colorScheme.black,
                        ),
                      ],
                    ),
                  )),
            ),
            Responsive.isDesktop(context) ? 24.ph : 12.ph,
            InkWell(
              // splashColor: Colors.transparent,
              // highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              onTap: () async {
                    const email = '<EMAIL>';
                    try {
                      if (kIsWeb) {
                        await _handleWebEmailLaunch(email, context);
                      } else {
                        final Uri emailLaunchUri = Uri(
                          scheme: 'mailto',
                          path: email,
                        );
                        await launchUrl(emailLaunchUri);
                      }
                    } catch (e) {
                      if (context.mounted) {
                        CustomToast.show(
                          context: context,
                          message: AppStrings.unableToOpenEmail,
                        );
                      }
                    }
                  },
              child: CommonShadowContainer(
                  spreadRadius: 0.1.r,
                  blurRadius: 5.r,
                  child: Padding(
                    padding: const EdgeInsets.all(17.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SvgPicture.asset(
                          AppImages.mailIcon,
                          height: 20.h,
                          width: 20.w,
                        ),
                        8.pw,
                        const TextTitle18And14(
                          AppStrings.contactUs,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'NotoSans-SemiBold',
                        ),
                        const Spacer(),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 15,
                          color: colorScheme.black,
                        ),
                      ],
                    ),
                  )),
            ),
            Responsive.isDesktop(context) ? 32.ph : 15.ph,
            !Responsive.isDesktop(context)
                ? Column(
                    children: [
                      ListTile(
                        onTap: () {
                          showLogoutDialog(context);
                        },
                        title: TextTitle14(
                          AppStrings.logout,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: colorScheme.hyperlinkBlueColor,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        leading: SvgPicture.asset(
                          AppImages.logoutIcon,
                          height: !Responsive.isDesktop(context) ? 20 : 24,
                        ),
                      ),
                    ListTile(
                        onTap: () {
                          showDeleteAccountDialog(context);
                        },
                        title: TextTitle14(
                          AppStrings.deleteAccount,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: colorScheme.red,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        leading: SvgPicture.asset(
                          AppImages.trash,
                          color: colorScheme.red,
                          height: 20,
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          showLogoutDialog(context);
                        },
                        child: Row(
                          children: [
                            8.pw,
                            SvgPicture.asset(
                              AppImages.logoutIcon,
                              height: 20,
                            ),
                            8.pw,
                            TextTitle14(
                              AppStrings.logout,
                              style: theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.hyperlinkBlueColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        height: 60,
                        width: 1,
                        decoration:
                            BoxDecoration(color: colorScheme.lightGreyD9D9D9),
                      ),
                      InkWell(
                        onTap: () {
                          showDeleteAccountDialog(context);
                        },
                        child: Row(
                          children: [
                            8.pw,
                            SvgPicture.asset(
                              AppImages.trash,
                              color: colorScheme.red,
                              height: 20,
                            ),
                            8.pw,
                            TextTitle14(
                              AppStrings.deleteAccount,
                              style: theme.textTheme.titleMedium?.copyWith(
                                  color: colorScheme.red,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  void showLogoutDialog(BuildContext context) {
    Dialogs.showCommonDialog(
      context: context,
      title: "${AppStrings.logout}?",
      message: AppStrings.areYouSureYouWantToLogOut,
      primaryButtonText: AppStrings.yesLogout,
      primaryButtonColor: const Color(0xFF525252),
      onPrimaryButtonTap: () {
        context.read<LogoutBloc>().logout();
        context.pop();
      },
    );
  }

    void showDeleteAccountDialog(BuildContext context) {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.areYouSureYouWantToDeleteAccount,
      message: AppStrings.deletingThisWillPermanentlyDelete,
      primaryButtonBgColor: const Color(0xFFF04438),
      primaryButtonText: AppStrings.delete,
      primaryButtonColor: const Color(0xFFffffff),
      onPrimaryButtonTap: () {
        context.read<DeleteAccountBloc>().deleteAccount();
        context.pop();
      },
    );
  }

  Future<void> _handleWebEmailLaunch(String email, BuildContext context) async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    
    if (await canLaunchUrl(emailLaunchUri)) {
      await launchUrl(emailLaunchUri);
    } else {
      if (context.mounted) {
        CustomToast.show(
          context: context,
          message: AppStrings.unableToOpenEmail,
        );
      }
    }
  }
}
