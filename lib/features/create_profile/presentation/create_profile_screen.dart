import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:textfield_tags/textfield_tags.dart' as tag_textfield;
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/create_profile/bloc/create_profile_state.dart';
import 'package:the_voice_directory_flutter/features/create_profile/bloc/create_profile_bloc.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/welcome_to_tvd_screen.dart';
import 'package:the_voice_directory_flutter/widgets/custom_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';
import 'package:the_voice_directory_flutter/features/google_address/presentation.dart/address_input_field.dart';

import '../../../core/services/hive/hive_keys.dart';

class CreateProfileScreen extends StatefulWidget {
  final String? userType;
  const CreateProfileScreen({super.key, this.userType});

  @override
  State<CreateProfileScreen> createState() => _CreateProfileScreenState();
}

class _CreateProfileScreenState extends State<CreateProfileScreen> {
  late TextEditingController bioController;
  late TextEditingController companyNameController;
  late TextEditingController addressController;
  late TextEditingController cityController;
  late TextEditingController stateController;
  late TextEditingController postalController;
  late TextEditingController countryController;
  late tag_textfield.TextfieldTagsController tagController;
  final formGlobalKey = GlobalKey<FormState>();
  bool _isTermsAccepted = false; // State for terms checkbox
  bool autovalidation = false;
  DropdownData? selectedGender;
  DropdownData? selectedIndustry;
  bool isSuccess = false;
  UserType userType = UserType.unknown;
  String? userName;
  UserDataModel? userDataModel;
  List<String> initialTags = [];
  Map<String, dynamic>? _selectedLocation;
  List<DropdownData> selectedServices = [];

  @override
  void initState() {
    userType = UserDataModel.getUserTypeFromString(widget.userType ?? "");
    bioController = TextEditingController();
    companyNameController = TextEditingController();
    addressController = TextEditingController();
    cityController = TextEditingController();
    stateController = TextEditingController();
    postalController = TextEditingController();
    countryController = TextEditingController();
    countryController.text = "India";
    tagController = tag_textfield.TextfieldTagsController();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      userDataModel = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
      userName = userDataModel?.firstName;
      // bioController.text = userDataModel?.bio ?? "";
      // companyNameController.text = userDataModel?.company ?? "";
      // addressController.text = userDataModel?.streetAddress ?? "";
      // cityController.text = userDataModel?.city ?? "";
      // stateController.text = userDataModel?.state ?? "";
      // postalController.text = userDataModel?.postalCode ?? "";
      // countryController.text = userDataModel?.country ?? "";
      setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    bioController.dispose();
    companyNameController.dispose();
    addressController.dispose();
    cityController.dispose();
    stateController.dispose();
    postalController.dispose();
    countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Row(
        children: [
          if (Responsive.isDesktop(context))
            const Expanded(child: TVDBrandingScreen()),
          Expanded(
            child: Padding(
              padding: !Responsive.isDesktop(context)
                  ? EdgeInsets.zero
                  : EdgeInsets.all(44.0.h),
              child: Card(
                color: Theme.of(context).colorScheme.white,
                elevation: !Responsive.isDesktop(context) ? 0 : 8,
                shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: !Responsive.isDesktop(context) ? 16.h : 25.h,
                      vertical: !Responsive.isDesktop(context) ? 24.h : 30.h),
                  child: BlocBuilder<CreateProfileBloc, CreateProfileState>(
                    builder: (context, state) {
                      if (state is CreateProfileSuccessState &&
                          (userType == UserType.client || userType == UserType.ancillaryService) &&
                          !Responsive.isMobile(context)) {
                        return WelcomeToTVDScreen(
                          title: AppStrings.welcomeToTVD(userName ?? ""),
                          message: AppStrings.welcomeToTVDDes,
                          buttonText: AppStrings.continueTxt,
                          onButtonPressed: () async {
                            NavigationServiceImpl.getInstance()!.doNavigation(
                                context,
                                useGo: true,
                                routeName: RouteName.dashboard);
                          },
                        );
                      }
                      return SingleChildScrollView(
                        child: Form(
                          key: formGlobalKey,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: !Responsive.isDesktop(context)
                                ? CrossAxisAlignment.start
                                : CrossAxisAlignment.center,
                            children: [
                              if (userType == UserType.voice &&
                                  Responsive.isDesktop(context))
                                Align(
                                    alignment: Alignment.centerRight,
                                  child: RichText(
                                      text: TextSpan(
                                          text: '01',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .copyWith(fontSize: 20.sp),
                                          children: [
                                        TextSpan(
                                          text: '/05',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .copyWith(fontSize: 14.sp),
                                        )
                                      ])),
                                ),
                              if (Responsive.isDesktop(context))
                                const TextDisplayLarge36And26(
                                    AppStrings.createProfile),
                              if (!Responsive.isDesktop(context))
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment:
                                      CrossAxisAlignment.center,
                                  children: [
                                    const TextDisplayLarge36And26(
                                        AppStrings.createProfile),
                                    if (userType == UserType.voice) ...[
                                      RichText(
                                          text: TextSpan(
                                              text: '01',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall!
                                                  .copyWith(fontSize: 16.sp),
                                              children: [
                                            TextSpan(
                                              text: '/05',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleSmall!
                                                  .copyWith(
                                                      fontWeight:
                                                          FontWeight.w400),
                                            )
                                          ])),
                                    ]
                                  ],
                                ),
                              15.ph,
                              const TextTitle18And14(
                                  AppStrings.addYourDetails),
                              !Responsive.isDesktop(context) ? 24.ph : 15.ph,
                              AppTextFormField(
                                controller: bioController,
                                titleText: AppStrings.bio,
                                hintText: AppStrings.tellUsALittleAboutYourself,
                                maxLines: 3,
                                validator: (value) {
                                  return Validator.emptyValidator(
                                      value, ValidationMsg.plsEnterBio);
                                },
                                isAutovalidateModeOn: autovalidation,
                                keyboardType: TextInputType.multiline,
                                textInputAction: TextInputAction.newline,
                              ),
                              !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                              BlocBuilder<StaticDataDropdownBloc,
                                  StaticDataDropdownState>(
                                builder: (context, state) {
                                  List<DropdownData>? gender = [];
                                  if (state
                                      is StaticDataDropdownSuccessState) {
                                    gender.addAll(
                                        state.dropDownResponseModel?.gender ??
                                            []);
                                  }
                                  return CustomDropDownWidget<DropdownData>(
                                      isLoading: state
                                          is StaticDataDropdownLoadingState,
                                      isError: state
                                          is StaticDataDropdownErrorState,
                                      hintText: AppStrings.selectGender,
                                      titleText: AppStrings.gender,
                                      selectedValue: selectedGender?.name,
                                      items: gender
                                          .map((item) => DropdownMenuItem(
                                              value: item,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  TextTitle18And14(
                                                    item.name ?? "",
                                                  ),
                                                  if (item.id ==
                                                      selectedGender?.id)
                                                    const Icon(Icons.check),
                                                ],
                                              )))
                                          .toList(),
                                      onChanged: (value) {
                                        selectedGender = value;
                                        setState(() {});
                                      },
                                      value: selectedGender);
                                },
                              ),
                              if (userType == UserType.ancillaryService) ...[
                                Responsive.isMobile(context) ? 16.ph : 24.ph,
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? services = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      services.addAll(state
                                              .dropDownResponseModel
                                              ?.services ??
                                          []);
                                    }
                                    return CustomMultiselectDropdown(
                                      isLoading: state
                                          is StaticDataDropdownLoadingState,
                                      isError:
                                          state is StaticDataDropdownErrorState,
                                      titleText: AppStrings.services,
                                      hintText: AppStrings.selectServices,
                                      items: services,
                                      selectedValues: selectedServices,
                                      onSelectionChanged: (newSelection) {
                                        setState(() {
                                          selectedServices = newSelection;
                                        });
                                      },
                                    );
                                  },
                                ),
                                if (selectedServices.isNotEmpty) 16.ph,
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: ChipsWidget(
                                    items: selectedServices,
                                    onRemove: (item) {
                                      selectedServices.remove(item);
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ],
                              Responsive.isMobile(context) ? 16.ph : 24.ph,
                              if (userType == UserType.client || userType == UserType.ancillaryService)
                                AppTextFormField(
                                  maxLength: 250,
                                  controller: companyNameController,
                                  hintText: AppStrings.addCompanyName,
                                  titleText: userType == UserType.ancillaryService
                                      ? AppStrings.company.substring(
                                          0, AppStrings.company.length - 1)
                                      : AppStrings.company,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp("[a-zA-Z0-9 ]")),
                                  ],
                                  validator: (value) {
                                    if (userType == UserType.client) {
                                      return Validator.companyNameValidator(
                                          companyNameController.text.trim(),
                                          ValidationMsg
                                              .plsEntervalidCompanyName)(value);
                                    }
                                    return null;
                                  },
                                  isAutovalidateModeOn: autovalidation,
                                ),
                              if (userType == UserType.voice || userType == UserType.ancillaryService)...[
                                Responsive.isMobile(context) ? 16.ph : 24.ph,
                                AddressInputField(
                                  addressController: addressController,
                                  cityController: cityController,
                                  stateController: stateController,
                                  postalController: postalController,
                                  countryController: countryController,
                                  hintText: AppStrings.enterAddress,
                                  titleText: AppStrings.streetAddress,
                                  isAutovalidateModeOn: autovalidation,
                                  onLocationSelected: (locationData) {
                                    setState(() {
                                      _selectedLocation = locationData;
                                    });
                                  },
                                  validator: (value) {
                                    return Validator.emptyValidator(value,
                                        ValidationMsg.plsEntervalidAddress);
                                  },
                                )],
                              if (userType == UserType.voice || userType == UserType.ancillaryService)
                                Responsive.isMobile(context) ? 16.ph : 24.ph,
                              if (userType == UserType.voice || userType == UserType.ancillaryService)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: AppTextFormField(
                                        maxLength: 100,
                                        controller: cityController,
                                        hintText: AppStrings.enterCity,
                                        titleText: AppStrings.city,
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp("[a-zA-Z0-9 ]")),
                                        ],
                                        validator: (value) {
                                          return Validator.emptyValidator(
                                              value,
                                              ValidationMsg
                                                  .plsEntervalidCity);
                                        },
                                        isAutovalidateModeOn: autovalidation,
                                      ),
                                    ),
                                    if (!Responsive.isDesktop(context)) 16.pw,
                                    if (!Responsive.isDesktop(context))
                                      Expanded(
                                        child: AppTextFormField(
                                          maxLength: 100,
                                          controller: stateController,
                                          hintText: AppStrings.enterYourState,
                                          maxLines: 1,
                                          titleText: AppStrings.state,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp("[a-zA-Z0-9 ]")),
                                          ],
                                          validator: (value) {
                                            return Validator.emptyValidator(
                                                value,
                                                ValidationMsg
                                                    .plsEntervalidState);
                                          },
                                          isAutovalidateModeOn:
                                              autovalidation,
                                        ),
                                      ),
                                  ],
                                ),
                              if ((userType == UserType.voice || userType == UserType.ancillaryService) &&
                                  Responsive.isDesktop(context))
                                24.ph,
                              if ((userType == UserType.voice || userType == UserType.ancillaryService) &&
                                  !Responsive.isMobile(context))
                                AppTextFormField(
                                  maxLength: 100,
                                  controller: stateController,
                                  hintText: AppStrings.enterYourState,
                                  titleText: AppStrings.state,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp("[a-zA-Z0-9 ]")),
                                  ],
                                  validator: (value) {
                                    return Validator.emptyValidator(value,
                                        ValidationMsg.plsEntervalidState);
                                  },
                                  isAutovalidateModeOn: autovalidation,
                                ),
                              if (userType == UserType.voice || userType == UserType.ancillaryService)
                                Responsive.isMobile(context) ? 16.ph : 24.ph,
                              if (userType == UserType.voice || userType == UserType.ancillaryService)
                                Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: AppTextFormField(
                                        maxLength: 6,
                                        controller: postalController,
                                        hintText: AppStrings.enterPin,
                                        titleText: AppStrings.postalCode,
                                        keyboardType: TextInputType.number,
                                        validator: (value) {
                                          return Validator.postalCode(value!);
                                        },
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp("[0-9]")),
                                        ],
                                        isAutovalidateModeOn: autovalidation,
                                      ),
                                    ),
                                    if (!Responsive.isDesktop(context)) 16.pw,
                                    if (!Responsive.isDesktop(context))
                                      Expanded(
                                        child: AppTextFormField(
                                          maxLength: 100,
                                          controller: countryController,
                                          // filled: true,
                                          // fillColor: Theme.of(context)
                                          //     .colorScheme
                                          //     .lightGreyD9D9D9,
                                          // readOnly: true,
                                          hintText: AppStrings.enterCountry,
                                          titleText: AppStrings.country,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp("[a-zA-Z0-9 ]")),
                                          ],
                                          validator: (value) {
                                            return Validator.emptyValidator(
                                                value,
                                                ValidationMsg
                                                    .plsEntervalidCountry);
                                          },
                                          isAutovalidateModeOn:
                                              autovalidation,
                                        ),
                                      ),
                                  ],
                                ),
                              if ((userType == UserType.voice || userType == UserType.ancillaryService) &&
                                  Responsive.isDesktop(context))
                                24.ph,
                              if ((userType == UserType.voice || userType == UserType.ancillaryService) &&
                                  !Responsive.isMobile(context))
                                AppTextFormField(
                                  maxLength: 100,
                                  controller: countryController,
                                  // filled: true,
                                  // fillColor: Theme.of(context)
                                  //     .colorScheme
                                  //     .lightGreyD9D9D9,
                                  // readOnly: true,
                                  hintText: AppStrings.enterCountry,
                                  titleText: AppStrings.country,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp("[a-zA-Z0-9 ]")),
                                  ],
                                  validator: (value) {
                                    return Validator.emptyValidator(value,
                                        ValidationMsg.plsEntervalidCountry);
                                  },
                                  isAutovalidateModeOn: autovalidation,
                                ),
                              !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                              if (userType == UserType.client)
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? industry = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      industry.addAll(state
                                              .dropDownResponseModel
                                              ?.industry ??
                                          []);
                                    }
                                    return CustomDropDownWidget<DropdownData>(
                                        isLoading: state
                                            is StaticDataDropdownLoadingState,
                                        isError: state
                                            is StaticDataDropdownErrorState,
                                        hintText: AppStrings.selectIndustry,
                                        titleText: AppStrings.industry,
                                        selectedValue: selectedIndustry?.name,
                                        items: industry
                                            .map((item) => DropdownMenuItem(
                                                value: item,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    TextTitle18And14(
                                                      item.name ?? "",
                                                    ),
                                                    if (item.id ==
                                                        selectedIndustry?.id)
                                                      const Icon(Icons.check),
                                                  ],
                                                )))
                                            .toList(),
                                        onChanged: (value) {
                                          selectedIndustry = value;
                                          setState(() {});
                                        },
                                        value: selectedIndustry);
                                  },
                                ),
                              if (userType == UserType.voice)
                                tag_textfield.TextFieldTags(
                                  textfieldTagsController: tagController,
                                  textSeparators: const [',', ' '],
                                  letterCase: tag_textfield.LetterCase.small,
                                  validator: (String tag) {
                                    if (tagController.getTags != null &&
                                        tagController.getTags!.isNotEmpty) {
                                      if (tagController.getTags!
                                          .contains(tag)) {
                                        return 'You already entered that';
                                      }
                                      if (tagController.getTags!.length > 9) {
                                        return 'Max 10 tags are allowed';
                                      }
                                    }
                                    return null;
                                  },
                                  inputfieldBuilder: (context, tec, fn, error,
                                      onChanged, onSubmitted) {
                                    return ((context, sc, tags, onTagDelete) {
                                      return Column(
                                        children: [
                                          AppTextFormField(
                                            controller: tec,
                                            focusNode: fn,
                                            isenabled: tags.length < 10,
                                            hintText: AppStrings.thisWillHelpClientsFindYou,
                                            titleText: AppStrings.tags,
                                            maxLength: 50,
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .allow(RegExp(
                                                      "[a-zA-Z0-9 ,_]")),
                                            ],
                                            onChanged: (value) {
                                              onChanged!(value);
                                            },
                                            onSubmitted: (value) {
                                              onSubmitted!(value);
                                              fn.requestFocus();
                                            },
                                            trailingTitleTextWidget: Tooltip(
                                              message: AppStrings.typeAndPressEnterToAddAKeyword,
                                              textStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                color: Theme.of(context).colorScheme.white,
                                                fontSize: 14.sp,
                                              ),
                                              child: Icon(
                                                Icons.info_outline,
                                                size: 20.h,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ),
                                          if (tags.isNotEmpty) 8.ph,
                                          Align(
                                            alignment: Alignment.centerLeft,
                                            child: ChipsWidget(
                                              items: tags,
                                              onRemove: (item) {
                                                onTagDelete(item);
                                              },
                                            ),
                                          ),
                                        ],
                                      );
                                    });
                                  },
                                ),
                              !Responsive.isDesktop(context) ? 24.ph : 32.ph,
                              Row(
                                children: [
                                  GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _isTermsAccepted =
                                              !_isTermsAccepted;
                                        });
                                      },
                                      child: _isTermsAccepted
                                          ? SvgPicture.asset(
                                              AppImages.selectedCheckboxIc)
                                          : SvgPicture.asset(
                                              AppImages.emptyCheckboxIc)),
                                  15.pw,
                                  Expanded(
                                    child: RichText(
                                      text: TextSpan(
                                        text: AppStrings.bySigningUp,
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleSmall,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text:
                                                AppStrings.termsAndConditions,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall
                                                ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .hyperlinkBlueColor,
                                                  fontWeight: FontWeight.w700,
                                                  decoration: TextDecoration
                                                      .underline,
                                                  decorationColor:
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .hyperlinkBlueColor,
                                                ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                context.pushNamed(RouteName.termsAndConditions);
                                              },
                                          ),
                                          const TextSpan(
                                            text: AppStrings.and,
                                          ),
                                          TextSpan(
                                            text: AppStrings.privacyPolicy,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall
                                                ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .hyperlinkBlueColor,
                                                  fontWeight: FontWeight.w700,
                                                  decoration: TextDecoration
                                                      .underline,
                                                  decorationColor:
                                                      Theme.of(context)
                                                          .colorScheme
                                                          .hyperlinkBlueColor,
                                                ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                context.pushNamed(RouteName.privacyPolicy);
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                              BlocListener<CreateProfileBloc,
                                  CreateProfileState>(
                                listener: (context, state) async {
                                  if (state is CreateProfileLoadingState) {
                                    Dialogs.showOnlyLoader(context);
                                  }
                                  if (state is CreateProfileSuccessState) {
                                    HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                    context.pop();
                                    if ((userType == UserType.client || userType == UserType.ancillaryService) &&
                                        Responsive.isMobile(context)) {
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        isDismissible: false,
                                        backgroundColor: Colors.transparent,
                                        builder: (BuildContext context) {
                                          return Material(
                                            type: MaterialType.transparency,
                                            shadowColor: Colors.transparent,
                                            child: Stack(
                                              alignment: Alignment.bottomCenter,
                                              children: [
                                                Align(
                                                  alignment: Alignment.bottomCenter,
                                                  child: Container(
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      gradient: LinearGradient(
                                                        begin: Alignment.topCenter,
                                                        end: Alignment.bottomCenter,
                                                        colors: [
                                                          Colors.transparent,
                                                          Colors.black.withOpacity(0.15),
                                                        ],
                                                      ),
                                                      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                                    ),
                                                  ),
                                                ),
                                                WelcomeToTVDScreen(
                                                  title:AppStrings.welcomeToTVD(userName ?? ""),
                                                  message: AppStrings.welcomeToTVDDes,
                                                  buttonText: AppStrings.continueTxt,
                                                  onButtonPressed: () {
                                                    context.pop();
                                                  NavigationServiceImpl.getInstance()!.doNavigation(context, useGo: true, routeName: RouteName.dashboard);
                                                  },
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      );
                                      return;
                                    }
                                    if (userType == UserType.voice) {
                                      NavigationServiceImpl.getInstance()!
                                          .doNavigation(context,
                                              routeName: RouteName
                                                  .vocalCharacteristics);
                                      return;
                                    }
                                    return;
                                  }
                                  if (state is CreateProfileErrorState) {
                                    // Show Error Message
                                    context.pop();
                                    CustomToast.show(
                                        context: context,
                                        message: state.errorMsg,
                                        isSuccess: true);
                                  }
                                },
                                child: PrimaryButton(
                                    buttonText: AppStrings.continueTxt,
                                    onPressed: () {
                                      if (selectedGender == null) {
                                        CustomToast.show(
                                          context: context,
                                          message:
                                              ValidationMsg.plsSelectGender,
                                        );
                                        return;
                                      }
                                      if (userType == UserType.ancillaryService && selectedServices.isEmpty) {
                                        CustomToast.show( context: context, message: ValidationMsg.plsSelectServices);
                                        return;
                                      }
                                      if (userType == UserType.client &&
                                          selectedIndustry == null) {
                                        CustomToast.show(
                                          context: context,
                                          message:
                                              ValidationMsg.plsSelectIndustry,
                                        );
                                        return;
                                      }
                                      if (!_isTermsAccepted) {
                                        CustomToast.show(
                                          context: context,
                                          message:
                                              ValidationMsg.plsAgreeToTnC,
                                        );
                                        return;
                                      }
                                      if (formGlobalKey.currentState!
                                          .validate()) {
                                        int? postalCode;
                                        if (postalController
                                            .text.isNotEmpty) {
                                          postalCode = int.parse(
                                              postalController.text);
                                        }
                                        List<String>? tags;
                                        if (userType == UserType.voice) {
                                          tags = tagController.hasTags
                                              ? tagController.getTags
                                              : null;
                                        }
                                        context
                                            .read<CreateProfileBloc>()
                                            .submitCreateProfile(
                                                userInfoRequestModel:
                                                    UserInfoRequestModel(
                                              isProfileCompleted: (userType == UserType.client || userType == UserType.ancillaryService),
                                              bio: bioController.text.trim(),
                                              company:
                                                  companyNameController.text,
                                              streetAddress:
                                                  addressController.text,
                                              city: cityController.text,
                                              state: stateController.text,
                                              country: countryController.text,
                                              postalCode: postalCode,
                                              tag: tags,
                                              gender: selectedGender?.id,
                                              intermediateStep: 1,
                                              industry: selectedIndustry?.id,
                                              lat: _selectedLocation?['location']['lat'],
                                              lng: _selectedLocation?['location']['lng'],
                                              services: selectedServices.map((e) => e.id).toList()));
                                      } else {
                                        setState(() {
                                          autovalidation = true;
                                        });
                                      }
                                    }),
                              )
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      )),
    );
  }
}
