import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/create_profile/bloc/logout_state.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class LogoutBloc extends Cubit<LogoutState> {
  @override
  LogoutBloc() : super(LogoutInitState());

  void logout() async {
    emit(LogoutLoadingState());
    try {
      final response = await ApiService.instance.logout();
      if (response.success) {
        emit(LogoutSuccessState());
      } else {
        emit(LogoutErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(LogoutErrorState(AppStrings.genericErrorMsg));
    }
  }
}
