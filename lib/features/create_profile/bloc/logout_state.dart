import 'package:equatable/equatable.dart';

abstract class LogoutState extends Equatable {}

class LogoutInitState extends LogoutState {
  @override
  List<Object> get props => [];
}

class LogoutLoadingState extends LogoutState {
  @override
  List<Object> get props => [];
}

class LogoutSuccessState extends LogoutState {
  @override
  List<Object> get props => [];
}

class LogoutErrorState extends LogoutState {
  final String errorMsg;
  LogoutErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
