import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';

class NoJobsDataWidget extends StatelessWidget {
  final VoidCallback? onButtonPressed;
  final UserType? userType;
  final String textTitle;

  const NoJobsDataWidget({
    super.key,
    this.onButtonPressed,
    required this.userType,
    required this.textTitle,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Column(
      children: [
        Responsive.isDesktop(context)
            ? 60.ph
            : userType == UserType.client
                ? 185.ph
                : 174.ph,
        SvgPicture.asset(AppImages.noJobsData),
        28.ph,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: userType != UserType.voice ? 0 : 50.w),
          child: TextTitle18And14(
            textTitle,
            fontWeight: Responsive.isDesktop(context) ? FontWeight.w700 : FontWeight.w500,
            fontSize: Responsive.isDesktop(context) ? 18.sp : 16.sp,
            textAlign: TextAlign.center,
          ),
        ),
        if (Responsive.isDesktop(context) && userType != UserType.voice && onButtonPressed != null) ...[
          28.ph,
          PrimaryButton(
            backgroundColor: colorScheme.primary,
            onPressed: () {
              onButtonPressed?.call();
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(40.r),
            ),
            width: 213.w,
            child: const TextTitle18And14(
              AppStrings.postAJob,
            ),
          ),
        ],
      ],
    );
  }
}
