import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_state.dart';
import 'package:the_voice_directory_flutter/features/jobs/bloc/jobs_cubit.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';

import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/get_profile_image.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/common_circle_icon.dart';
import '../../../widgets/texts/app_text.dart';
import '../../chat/bloc/chat_bloc.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../../post_job/data/enums/job_post_type.dart';
import '../enums/application_status.dart';
import '../enums/job_status.dart';
import '../enums/jobs_list_type.dart';
import '../model/jobs_response.dart';

class JobCard extends StatefulWidget {
  final JobsResponseModel job;
  final VoidCallback onTap;
  const JobCard({super.key, required this.job, required this.onTap});

  @override
  State<JobCard> createState() => _JobCardState();
}

class _JobCardState extends State<JobCard> {
  bool _isJobFavorite = false;
  bool defaultFavorite = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _isJobFavorite = defaultFavorite = widget.job.isJobFavorite ?? false;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _handleFavoriteToggle(BuildContext context) {
    _debounceTimer?.cancel();
    setState(() {
      _isJobFavorite = !_isJobFavorite;
    });

    widget.job.copyWith(isJobFavorite: _isJobFavorite);

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (defaultFavorite == _isJobFavorite) return;
      context.read<FavoriteJobBloc>().apiFavoriteJob(widget.job.id);
    });
  }

  Widget _buildFavoriteButton() {
    return BlocConsumer<FavoriteJobBloc, FavoriteJobState>(
      listener: (context, state) {
        if (state is FavoriteJobSuccessState) {
          setState(() {
            defaultFavorite = _isJobFavorite;
          });
        }
        if (state is FavoriteJobErrorState) {
          if (state.jobId == widget.job.id) {
            setState(() {
              _isJobFavorite = !_isJobFavorite;
            });
            widget.job.copyWith(isJobFavorite: _isJobFavorite);
            CustomToast.show(
                context: context, message: state.errorMsg, isSuccess: false);
          }
        }
      },
      builder: (context, state) {
        final bool isLoading =
            state is FavoriteJobLoadingState && state.jobId == widget.job.id;

        return CommonCircleIcon(
          iconPath:
              _isJobFavorite ? AppImages.filledHeartIcon : AppImages.heartIcon,
          onTap: isLoading ? null : () => _handleFavoriteToggle(context),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final jobsCubit = context.read<JobsCubit>().state;
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    final userType = HiveStorageHelper.getData<UserDataModel>(
            HiveBoxName.user, HiveKeys.userData)
        ?.role;
    return InkWell(
      onTap: widget.onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: colorScheme.white,
          border: Border.all(
            color: colorScheme.lightGreyD9D9D9,
            width: 1.2,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextTitle18And14(
              widget.job.title ?? '',
              style: theme.textTheme.titleMedium?.copyWith(
                fontSize: Responsive.isDesktop(context) ? 20.sp : 18.sp,
                fontWeight: FontWeight.w600,
                color: colorScheme.primaryGrey,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 2,
            ),
            12.ph,
            Row(
              children: [
                SvgPicture.asset(
                  widget.job.jobPostType == JobPostType.public
                      ? AppImages.publicJobIc
                      :  AppImages.privateJobIc,
                      color: widget.job.jobPostType == JobPostType.private
                          ? colorScheme.green00843E : colorScheme.primaryGrey,
                ),
                8.pw,
                TextTitle18And14(
                  widget.job.jobPostType?.toString() ?? '',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: widget.job.jobPostType == JobPostType.private
                        ? colorScheme.green00843E : colorScheme.primaryGrey,
                  ),
                ),
              ],
            ),
            12.ph,
            TextTitle18And14(
              'Job #${widget.job.jobUniqueId} • Posted: ${widget.job.createdAt != null ? DateFormat('dd MMM, yyyy').format(widget.job.createdAt!) : 'N/A'}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: colorScheme.primaryGrey,
              ),
            ),
            12.ph,
            TextTitle18And14(
              'Budget: ${widget.job.budgetDisplay ?? "N/A"}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: colorScheme.primaryGrey,
              ),
            ),
            12.ph,
            TextTitle18And14(
              'Response deadline: ${widget.job.responseDeadline != null ? DateFormat('dd MMM, yyyy').format(widget.job.responseDeadline!) : 'N/A'}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: colorScheme.primaryGrey,
              ),
            ),
            12.ph,
            TextTitle18And14(
              'Project deadline: ${widget.job.projectDeadline != null ? DateFormat('dd MMM, yyyy').format(widget.job.projectDeadline!) : 'N/A'}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w400,
                color: colorScheme.primaryGrey,
              ),
            ),
            const Expanded(child: SizedBox()),
            if (jobsCubit.jobStatusType != JobsListType.history)
              if (userType == UserType.client || userType == UserType.ancillaryService) ...[
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: widget.job.jobStatus?.chipColor(colorScheme),
                    borderRadius: BorderRadius.circular(40.r),
                  ),
                  child: TextTitle18And14(
                    widget.job.jobStatus?.toString() ?? '',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: widget.job.jobStatus == JobStatus.open
                          ? colorScheme.primaryGrey
                          : colorScheme.white,
                    ),
                  ),
                ),
              ] else ...[
                if (widget.job.applicationStatus == null) ...[
                  Row(
                    children: [
                      Expanded(
                        child: PrimaryButton(
                          buttonText: AppStrings.apply,
                          onPressed: () {
                            if (widget.job.applicationStatus == null) {
                              NavigationServiceImpl.getInstance()!.doNavigation(
                                context,
                                routeName: RouteName.applyJob,
                                pathParameters: {
                                  Params.id: widget.job.id.toString()
                                },
                              );
                            }
                          },
                          height: 41.h,
                        ),
                      ),
                      12.pw,
                      _buildFavoriteButton(),
                      // Responsive.isDesktop(context) ? 16.pw : 8.pw,
                      // CommonCircleIcon(
                      //   iconPath: AppImages.heartIcon,
                      //   onTap: () {
                      //     // TODO: Handle tap for favorite
                      //   },
                      // ),
                    ],
                  ),
                ] else ...[
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: widget.job.applicationStatus?.chipColor(colorScheme),
                          borderRadius: BorderRadius.circular(40.r),
                        ),
                        child: TextTitle18And14(
                          widget.job.applicationStatus?.toString() ?? '',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: widget.job.applicationStatus == ApplicationStatus.applied
                                ? colorScheme.primaryGrey
                                : colorScheme.white,
                          ),
                        ),
                      ),
                      const Expanded(child: SizedBox()),
                      if (widget.job.applicationStatus == ApplicationStatus.shortlisted || widget.job.applicationStatus == ApplicationStatus.selected)
                      CommonCircleIcon(
                        iconPath: AppImages.messageChatIc,
                        onTap: () {
                          final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
                          final idFrom = userData?.id;
                          final idTo = widget.job.clientId;
                          final jobId = widget.job.id;
                          if(idFrom == null || idTo == null || jobId == null){
                            CustomToast.show(context: context, message: AppStrings.genericErrorMsg);
                            return;
                          }
                          String groupId = '';
                          if (idFrom < idTo) {
                            groupId = "${jobId}_${idFrom}_$idTo";
                          } else {
                            groupId = "${jobId}_${idTo}_$idFrom";
                          }
                          String? myName = '${userData?.firstName ?? ''} ${userData?.lastName ?? ''}';
                          String? myProfileImg = ProfileImage.getProfileImage(userData?.profilePic);
                          String? clientProfileImg = ProfileImage.getProfileImage(widget.job.clientProfileImg);
                          context.read<ChatBloc>().updateChatState(
                            selectedChat: groupId,
                            isFromChatList: false,
                            isJobChat: true,
                          );
                          NavigationServiceImpl.getInstance()!.doNavigation(
                            context,
                            routeName: RouteName.chat,
                            pathParameters: {
                              Params.jobName: widget.job.title ?? '',
                              Params.groupId: groupId,
                              Params.jobId: jobId.toString(),
                              Params.idFrom: idFrom.toString(),
                              Params.idTo: idTo.toString(),
                              Params.myName: myName,
                              Params.myProfileImg: myProfileImg,
                              Params.name: widget.job.clientName ?? '',
                              Params.profileImg: clientProfileImg,
                              Params.needBackBtn: 'true',
                            },
                          );
                        },
                      ),
                      12.pw,
                      if (widget.job.applicationStatus !=
                          ApplicationStatus.selected)
                        _buildFavoriteButton(),
                    ],
                  ),
                ],
              ],
          ],
        ),
      ),
    );
  }
}
