import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';
import '../enums/jobs_sort_order.dart';

class SortOrderBottomSheet extends StatelessWidget {
  final Function(JobsSortOrder jobsSortOrder) onChangeSortOrder;

  const SortOrderBottomSheet({
    super.key,
    required this.onChangeSortOrder,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Container(
        width: ScreenUtil().screenWidth,
        decoration: BoxDecoration(
          color: colorScheme.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
          ),
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical:  12.h, horizontal: 12.w),
                      child: TextTitle14(
                        AppStrings.sortBy,
                        style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                    ...JobsSortOrder.values.map((item) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 14.w),
                          child: InkWell(
                            onTap: () {
                              onChangeSortOrder(item);
                              context.pop();
                            },
                            child: TextTitle14(
                              item.displayName,
                              style: textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        )),
                    10.ph,
                  ],
                ),
              ],
            ),

            // Positioned Cancel Button
            Positioned(
              top: -52.h,
              left: 14.w,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.zero,
                  backgroundColor: colorScheme.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                onPressed: () {
                  context.pop();
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                  child: TextTitle14(
                    AppStrings.cancel,
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.hyperlinkBlueColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
