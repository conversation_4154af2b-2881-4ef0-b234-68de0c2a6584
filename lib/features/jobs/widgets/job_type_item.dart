import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/responsive.dart';
import '../../../widgets/texts/app_text.dart';
import '../enums/jobs_list_type.dart';

class JobTypeItem extends StatelessWidget {
  final JobsListType filter;
  final JobsListType selectedFilter;
  final VoidCallback onTap;

  const JobTypeItem({
    super.key,
    required this.filter,
    required this.selectedFilter,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(32.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32.r),
          color: selectedFilter == filter
              ? colorScheme.secondary
              : colorScheme.white,
          border: Border.all(
            color: selectedFilter == filter
                ? colorScheme.secondary
                : colorScheme.lightGreyD9D9D9,
            width: 1.2,
          ),
        ),
        height: Responsive.isDesktop(context) ? 56.h : 44.h,
        padding: EdgeInsets.symmetric(
          horizontal: Responsive.isDesktop(context) ? 0.0 : 16.w,
          // vertical: Responsive.isDesktop(context) ? 16.h : 9.h,
        ),
        child: Center(
          child: TextTitle18And14(
            filter.toString(),
            style: theme.textTheme.titleMedium?.copyWith(
              fontSize: Responsive.isDesktop(context) ? 20.sp : 16.sp,
              fontWeight: selectedFilter == filter ? FontWeight.w500 : FontWeight.w400,
              color: selectedFilter == filter
                  ? colorScheme.white
                  : colorScheme.primaryGrey,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
        ),
      ),
    );
  }
}
