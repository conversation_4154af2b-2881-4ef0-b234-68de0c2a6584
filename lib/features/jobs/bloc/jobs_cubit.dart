import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:universal_html/html.dart';

import '../../../core/api/api_service.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../enums/jobs_sort_order.dart';
import '../enums/jobs_list_type.dart';
import '../model/jobs_response.dart';

part 'jobs_state.dart';

class JobsCubit extends Cubit<JobsState> {
  JobsCubit() : super(JobsState(jobs: List.empty()));

  final String searchNameKey = 'searchName';
  final String jobStatusTypeKey = 'jobStatusType';
  final String jobsSortOrderKey = 'jobsSortOrder';

  Future<void> fetchJobs({
    String? searchName,
    JobsListType? jobStatusType,
    JobsSortOrder jobsSortOrder = JobsSortOrder.newestFirst,
    int page = 1,
  }) async {
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    final JobsListType jobFilter = jobStatusType ?? ((userData?.role == UserType.client || userData?.role == UserType.ancillaryService)
            ? JobsListType.allJobs
            : JobsListType.hiring);

    emit(state.copyWith(
      isLoading: true,
      jobStatusType: jobFilter,
      jobsSortOrder: jobsSortOrder,
      currentPage: page,
      searchName: searchName,
    ));

    _saveStateToSessionStorage();

    try {
      final response = await ApiService.instance.getAllJobs(
        searchName: searchName?.toString().toLowerCase(),
        filter: jobFilter.toString().toLowerCase(),
        sortOrder: jobsSortOrder.toString().toLowerCase(),
        isClient: userData?.role == UserType.client || userData?.role == UserType.ancillaryService,
      );

      if (response.success && response.data != null && response.data is List) {
        if (response.data != null && response.data is List) {
          final List<JobsResponseModel> fetchedJobs = (response.data as List)
              .map((job) => JobsResponseModel.fromJson(job as Map<String, dynamic>))
              .toList();

          bool disableTaps = false;

          if ((jobFilter == JobsListType.allJobs || userData?.role == UserType.voice) &&
              (searchName?.isEmpty ?? true) &&
              fetchedJobs.isEmpty) {
            disableTaps = true;
          }

          emit(state.copyWith(
            jobs: fetchedJobs,
            jobStatusType: jobStatusType,
            jobsSortOrder: jobsSortOrder,
            currentPage: page,
            isLoading: false,
            disableTaps: disableTaps,
            initialSearch: false,
          ));
        }
      } else {
        emit(state.copyWith(
          errorMsg: response.message ?? AppStrings.genericErrorMsg,
          initialSearch: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        errorMsg: AppStrings.genericErrorMsg,
        isLoading: false,
        initialSearch: false,
      ));
    }
  }

  // void updateDisplayedJobs(int page) {
  //   if (state.isLoading) return;
  //   emit(state.copyWith(currentPage: page));
  // }

  void resetErrorMsg() {
    emit(state.copyWith(errorMsg: null));
  }

  void loadStateFromSessionStorage() {
    final storage = window.sessionStorage;

    final searchName = storage[searchNameKey];
    final jobStatusTypeString = storage[jobStatusTypeKey];
    final jobsSortOrderString = storage[jobsSortOrderKey];

    JobsListType? jobStatusType;
    if (jobStatusTypeString != null) {
      try {
        jobStatusType = JobsListType.values.firstWhere(
          (e) => e.toString() == jobStatusTypeString,
        );
      } catch (e) {
        jobStatusType = JobsListType.allJobs;
      }
    }

    JobsSortOrder? jobsSortOrder;
    if (jobsSortOrderString != null) {
      try {
        jobsSortOrder = JobsSortOrder.values.firstWhere(
          (e) => e.toString() == jobsSortOrderString,
        );
      } catch (e) {
        jobsSortOrder = JobsSortOrder.newestFirst;
      }
    }

    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    final JobsListType filter = userData?.role == UserType.client || userData?.role == UserType.ancillaryService
        ? JobsListType.allJobs
        : JobsListType.hiring;

    fetchJobs(
      jobStatusType: jobStatusType ?? filter,
      jobsSortOrder: jobsSortOrder ?? JobsSortOrder.newestFirst,
      searchName: searchName,
    );
  }

  // Save state to sessionStorage
  void _saveStateToSessionStorage() {
    final storage = window.sessionStorage;

    if (state.searchName != null) {
      storage[searchNameKey] = state.searchName!;
    } else {
      storage.remove(searchNameKey); // Remove if null
    }
    storage[jobStatusTypeKey] = state.jobStatusType.toString();
    storage[jobsSortOrderKey] = state.jobsSortOrder.toString();
  }

  void clearSessionStorage() {
    final storage = window.sessionStorage;
    storage.remove(searchNameKey);
    storage.remove(jobStatusTypeKey);
    storage.remove(jobsSortOrderKey);
  }
}
