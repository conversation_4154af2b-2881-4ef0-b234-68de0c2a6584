import '../../post_job/data/enums/budget_type_enum.dart';
import '../../post_job/data/enums/job_post_type.dart';
import '../enums/application_status.dart';
import '../enums/job_status.dart';

class JobsResponseModel {
  int? id;
  String? title;
  JobPostType? jobPostType;
  int? jobUniqueId;
  DateTime? createdAt;
  BudgetType? budgetType;
  int? fixedBudget;
  int? minBudgetRange;
  int? maxBudgetRange;
  DateTime? projectDeadline;
  DateTime? responseDeadline;
  JobStatus? jobStatus;
  String? budgetDisplay;
  ApplicationStatus? applicationStatus;
  bool? isJobFavorite;
  int? clientId;
  String? clientName;
  String? clientProfileImg;

  JobsResponseModel({
    this.id,
    this.title,
    this.jobPostType,
    this.jobUniqueId,
    this.createdAt,
    this.budgetType,
    this.fixedBudget,
    this.minBudgetRange,
    this.maxBudgetRange,
    this.projectDeadline,
    this.responseDeadline,
    this.jobStatus,
    this.budgetDisplay,
    this.applicationStatus,
    this.isJobFavorite,
    this.clientId,
    this.clientName,
    this.clientProfileImg,
  });

  factory JobsResponseModel.fromJson(Map<String, dynamic> json) {
    JobPostType? jobPostType = json['job_post_type'] != null
        ? JobPostType.getJobPostTypeFromString(json['job_post_type'])
        : null;
    BudgetType? budgetType = json['budget_type'] != null
        ? BudgetType.getBudgetTypeFromString(json['budget_type'])
        : null;

    int? fixedBudget = json['fixed_budget'];
    int? minBudgetRange = json['min_budget_range'];
    int? maxBudgetRange = json['max_budget_range'];

    String budgetDisplay;
    if (fixedBudget != null && budgetType == BudgetType.fixed) {
      budgetDisplay = '₹${fixedBudget.toString()}';
    } else if (budgetType == BudgetType.range &&
        minBudgetRange != null &&
        maxBudgetRange != null) {
      budgetDisplay = '₹$minBudgetRange - ₹$maxBudgetRange';
    } else {
      budgetDisplay = 'N/A';
    }

    return JobsResponseModel(
      id: json['id'] as int?,
      title: json['title'] as String?,
      jobPostType: jobPostType,
      jobUniqueId: json['job_unique_id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      budgetType: budgetType,
      fixedBudget: json['fixed_budget'] as int?,
      minBudgetRange: json['min_budget_range'] as int?,
      maxBudgetRange: json['max_budget_range'] as int?,
      projectDeadline: json['project_deadline'] != null
          ? DateTime.tryParse(json['project_deadline'])
          : null,
      responseDeadline: json['response_deadline'] != null
          ? DateTime.tryParse(json['response_deadline'])
          : null,
      jobStatus:
          JobStatus.getJobStatusFromString(json['job_status'] as String?),
      budgetDisplay: budgetDisplay,
      applicationStatus: ApplicationStatus.getApplicationStatusFromString(
          json['application_status'] as String?),
      isJobFavorite: json['is_job_favorite'] as bool?,
      clientId: json['client_id'] as int?,
      clientName: json['client_name'] as String?,
      clientProfileImg: json['client_picture'] as String?,
    );
  }

  JobsResponseModel copyWith({bool? isJobFavorite}) {
    return JobsResponseModel(
      id: id, isJobFavorite: isJobFavorite ?? this.isJobFavorite,
    );
  }
}
