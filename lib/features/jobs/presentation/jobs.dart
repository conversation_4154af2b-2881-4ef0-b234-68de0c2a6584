import 'dart:async';

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/loading_dialog.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/jobs_cubit.dart';
import '../enums/jobs_list_type.dart';
import '../enums/jobs_sort_order.dart';
import '../widgets/job_card.dart';
import '../widgets/job_type_item.dart';
import '../../../widgets/common_app_bar.dart';
import '../widgets/no_jobs_data_widget.dart';
import '../widgets/sort_order_bottom_sheet.dart';

class JobsScreen extends StatefulWidget {
  const JobsScreen({super.key});

  @override
  State<JobsScreen> createState() => _JobsScreenState();
}

class _JobsScreenState extends State<JobsScreen> {
  bool isOpen = false;
  late final TextEditingController searchController;
  Timer? _debounceTimer;
  bool _hasSearched = false;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();

    context.read<JobsCubit>().loadStateFromSessionStorage();

    final initialSearchName = context.read<JobsCubit>().state.searchName;
    if (initialSearchName != null) {
      searchController.text = initialSearchName;
    }
  }

  void onSearchChanges(BuildContext context, {bool forceSearch = false}) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    if (searchController.text.length < 2 && !forceSearch) {
      if (searchController.text.isEmpty && !_hasSearched) return;
      if (searchController.text.isNotEmpty) return;
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final cubit = context.read<JobsCubit>();
      final state = cubit.state;
      if (forceSearch) FocusManager.instance.primaryFocus?.unfocus();
      cubit.fetchJobs(
        searchName: searchController.text,
        jobStatusType: state.jobStatusType,
        jobsSortOrder: state.jobsSortOrder,
        page: 1,
      );

      _hasSearched = searchController.text.length >= 2 || forceSearch;
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    final List<JobsListType> filters = (userData?.role == UserType.client || userData?.role == UserType.ancillaryService)
        ? JobsListType.getByUserType(UserType.client)
        : JobsListType.getByUserType(UserType.voice);
    return Scaffold(
      appBar: Responsive.isDesktop(context)
          ? null
          : const CommonAppBar(title: AppStrings.jobs, showNotificationIcon: true),
      body: BlocConsumer<JobsCubit, JobsState>(
        listener: (context, state) {
          if (state.errorMsg != null && state.errorMsg!.isNotEmpty) {
            CustomToast.show(context: context, message: state.errorMsg!);
            context.read<JobsCubit>().resetErrorMsg();
          }
        },
        builder: (context, state) {
          if (state.isLoading && state.initialSearch) {
            return const Center(
              child: Loader(),
            );
          }
          return ConstrainedBox(
            constraints: const BoxConstraints.expand(),
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                left: Responsive.isDesktop(context) ? 80.w: 16.w,
                right: Responsive.isDesktop(context) ? 80.w : 16.w,
                top: Responsive.isDesktop(context) ? 40.h : 4.h,
                bottom: Responsive.isDesktop(context) ? 32.h : 0,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (Responsive.isDesktop(context))
                    Row(
                      children: [
                        Expanded(
                          flex: 8,
                          child: TextDisplayLarge36And26(
                            AppStrings.jobs,
                            color: colorScheme.primaryGrey,
                          ),
                        ),
                        Expanded(
                          flex: 8,
                          child: AppTextFormField(
                            maxLength: 100,
                            maxLines: 1,
                            controller: searchController,
                            hintText: AppStrings.searchHintText,
                            hintTextFontWeight: FontWeight.w500,
                            hintTextColor: colorScheme.darkGrey525252,
                            prefixIcon: Padding(
                              padding: EdgeInsets.all(14.h),
                              child: SvgPicture.asset(AppImages.searchIc),
                            ),
                            onChanged: (_) {
                              setState(() {});
                              onSearchChanges(context);
                            },
                            readOnly: state.disableTaps,
                            suffixIcon: searchController.text.isNotEmpty
                                ? GestureDetector(
                                    onTap: () {
                                      searchController.clear();
                                      setState(() {});
                                      onSearchChanges(context, forceSearch: true);
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.all(8.0.h),
                                      child: SvgPicture.asset(AppImages.closeSquareIc),
                                    ),
                                  )
                                : null,
                          ),
                        ),
                        12.pw,
                        Expanded(
                          flex: 5,
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton2<JobsSortOrder>(
                              customButton: Container(
                                padding: !Responsive.isDesktop(context)
                                    ? EdgeInsets.fromLTRB(12.w, 8.h, 12.w, 8.h)
                                    : EdgeInsets.all(12.h),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.r),
                                  border: Border.all(
                                    color: colorScheme.lightGreyD9D9D9,
                                    width: 1.2,
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    SvgPicture.asset(AppImages.swapVertIc),
                                    4.pw,
                                    Expanded(
                                      child: Center(
                                        child: TextTitle18And14(
                                          "SORT BY: ${state.jobsSortOrder.displayName}",
                                          color: colorScheme.primaryGrey,
                                          style: (!Responsive.isDesktop(context)
                                              ? textTheme.titleSmall!.copyWith(
                                                  overflow: TextOverflow.ellipsis,
                                                )
                                              : textTheme.titleLarge!.copyWith(
                                                  overflow: TextOverflow.ellipsis,
                                                  fontWeight: FontWeight.w700,
                                                )),
                                          maxLines: 1,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              value: state.jobsSortOrder,
                              onChanged: state.disableTaps
                                  ? null
                                  : (val) {
                                      if (val == null) return;
                                      context.read<JobsCubit>().fetchJobs(
                                            jobsSortOrder: val,
                                            jobStatusType: state.jobStatusType,
                                            searchName: state.searchName,
                                          );
                                    },
                              buttonStyleData: const ButtonStyleData(
                                overlayColor: WidgetStatePropertyAll(Colors.transparent),
                              ),
                              dropdownStyleData: DropdownStyleData(
                                offset: const Offset(0, -8),
                                elevation: 0,
                                padding: EdgeInsets.zero,
                                decoration: BoxDecoration(
                                  color: colorScheme.white,
                                  border: Border.all(
                                    color: colorScheme.lightGreyD9D9D9,
                                    strokeAlign: BorderSide.strokeAlignOutside,
                                  ),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                              ),
                              onMenuStateChange: (isOpen) {
                                this.isOpen = isOpen;
                                setState(() {});
                              },
                              menuItemStyleData: MenuItemStyleData(
                                height: 56.h,
                                overlayColor: WidgetStatePropertyAll(colorScheme.lightGreenFDFFDA),
                              ),
                              items: JobsSortOrder.values
                                  .map((item) => DropdownMenuItem(
                                        value: item,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            TextTitle18And14(
                                              item.displayName,
                                              color: state.jobsSortOrder == item
                                                  ? colorScheme.primaryGrey
                                                  : colorScheme.darkGrey525252,
                                              fontWeight: state.jobsSortOrder == item
                                                      ? FontWeight.w700
                                                      : null,
                                            ),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  Responsive.isDesktop(context) ? 40.ph : 20.ph,
                  if (state.disableTaps && userData?.role != UserType.voice) ...[
                    Center(
                      child: NoJobsDataWidget(
                        userType: userData?.role,
                        textTitle: AppStrings.noJobsPostedYetClient,
                        onButtonPressed: () {
                          HiveStorageHelper.deleteKeyInBox(boxName: HiveBoxName.user, key: HiveKeys.jobPostData);
                          NavigationServiceImpl.getInstance()!.doNavigation(
                            context,
                            routeName: RouteName.postJob,
                          );
                        },
                      ),
                    )
                  ] else ...[
                    Builder(
                      builder: (context) {
                        if (Responsive.isDesktop(context)) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: filters.map((filter) => Expanded(
                                      child: JobTypeItem(
                                        filter: filter,
                                        selectedFilter: state.jobStatusType,
                                        onTap: () {
                                          if (state.jobStatusType != filter) {
                                            context.read<JobsCubit>().fetchJobs(
                                                  jobStatusType: filter,
                                                  jobsSortOrder: state.jobsSortOrder,
                                                  searchName: state.searchName,
                                                );
                                          }
                                        },
                                      ),
                                    ))
                                .expand((widget) => [widget, 16.pw])
                                .toList()
                              ..removeLast(), // Remove the last SizedBox to avoid extra spacing
                          );
                        }
                        return SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: filters.map((filter) => JobTypeItem(
                                      filter: filter,
                                      selectedFilter: state.jobStatusType,
                                      onTap: () {
                                        if (state.jobStatusType != filter) {
                                          context.read<JobsCubit>().fetchJobs(
                                                jobStatusType: filter,
                                                jobsSortOrder: state.jobsSortOrder,
                                                searchName: state.searchName,
                                              );
                                        }
                                      },
                                    ))
                                .expand((widget) => [widget, 16.pw])
                                .toList()
                              ..removeLast(), // Remove the last SizedBox to avoid extra spacing
                          ),
                        );
                      },
                    ),
                    Responsive.isDesktop(context) ? 28.ph : 20.ph,
                    if (!Responsive.isDesktop(context) && !state.disableTaps) ...[
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: AppTextFormField(
                              maxLength: 100,
                              maxLines: 1,
                              controller: searchController,
                              hintText: AppStrings.searchHintText,
                              prefixIcon: Padding(
                                padding: EdgeInsets.all(14.h),
                                child: SvgPicture.asset(AppImages.searchIc),
                              ),
                              onChanged: (_) {
                                setState(() {});
                                onSearchChanges(context);
                              },
                              suffixIcon: searchController.text.isNotEmpty
                                  ? GestureDetector(
                                      onTap: () {
                                        searchController.clear();
                                        setState(() {});
                                        onSearchChanges(context, forceSearch: true);
                                      },
                                      child: Padding(
                                        padding: EdgeInsets.all(8.0.h),
                                        child: SvgPicture.asset(AppImages.closeSquareIc),
                                      ),
                                    )
                                  : null,
                            ),
                          ),
                          12.pw,
                          InkWell(
                            onTap: () async {
                              await showModalBottomSheet(
                                context: context,
                                backgroundColor: Colors.transparent,
                                builder: (_) {
                                  return SortOrderBottomSheet(
                                    onChangeSortOrder: (sortOrder) {
                                      if (state.jobsSortOrder != sortOrder) {
                                        context.read<JobsCubit>().fetchJobs(
                                              jobStatusType: state.jobStatusType,
                                              jobsSortOrder: sortOrder,
                                              searchName: state.searchName,
                                            );
                                      }
                                    },
                                  );
                                },
                              );
                            },
                            borderRadius: BorderRadius.circular(24.h),
                            child: Container(
                              width: 44.h,
                              height: 44.h,
                              decoration: BoxDecoration(
                                color: colorScheme.white,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: colorScheme.lightGreyD9D9D9,
                                ),
                              ),
                              child: Center(
                                child: SvgPicture.asset(
                                  AppImages.swapVertIc,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      20.ph,
                    ],
                    Builder(
                      builder: (context) {
                        if (state.isLoading && !state.initialSearch) {
                          return Column(
                            children: [
                              Responsive.isDesktop(context)
                                  ? 150.ph
                                  : userData?.role == UserType.client
                                      ? 260.ph
                                      : 250.ph,
                              const Center(child: Loader()),
                            ],
                          );
                        }
                    
                        if (state.jobs.isEmpty) {
                          if (userData?.role == UserType.voice) {
                            return Center(
                              child: NoJobsDataWidget(
                                userType: userData?.role,
                                textTitle: userData?.role != UserType.voice
                                    ? AppStrings.noJobsPostedYetClient
                                    : (state.jobStatusType == JobsListType.favourite) ? AppStrings.youHaveNotAddedFavorite : AppStrings.noJobsPostedYetVoice,
                              ),
                            );
                          }
                          return Center(
                            child: NoJobsDataWidget(
                              userType: userData?.role,
                              textTitle: AppStrings.noJobsFound,
                            ),
                          );
                        }
                    
                        // Remove stepper
                        // List<JobsResponseModel> displayedJobs;
                        // if (Responsive.isDesktop(context)) {
                        //   const int itemsPerPage = 4;
                        //   final startIndex = (state.currentPage - 1) * itemsPerPage;
                        //   final endIndex = startIndex + itemsPerPage;
                        //   displayedJobs = state.jobs.sublist(startIndex, endIndex.clamp(0, state.jobs.length));
                        // } else {
                        //   displayedJobs = state.jobs;
                        // }
                    
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                mainAxisExtent: state.jobStatusType != JobsListType.history
                                    ? 330.h
                                    : 280.h,
                                crossAxisCount: Responsive.isDesktop(context) ? 3 : 1,
                                mainAxisSpacing: Responsive.isDesktop(context) ? 24 : 16,
                                crossAxisSpacing: Responsive.isDesktop(context) ? 24 : 16,
                                // childAspectRatio: 2,
                              ),
                              itemCount: state.jobs.length,
                              itemBuilder: (context, index) {
                                return JobCard(
                                  job: state.jobs[index],
                                  onTap: () {
                                    context.read<JobsCubit>().clearSessionStorage();
                                    NavigationServiceImpl.getInstance()!.doNavigation(
                                      context,
                                      routeName: RouteName.jobDetail, 
                                      pathParameters: {Params.id: state.jobs[index].id.toString(),
                                      Params.showApplicantTab: 'false'}
                                    );
                                  },
                                );
                              },
                            ),
                            // if (Responsive.isDesktop(context)) ...[
                            //   32.ph,
                            //   PaginationFooter(
                            //     currentPage: state.currentPage,
                            //     totalPages: state.jobs.length ~/ 4,
                            //     onPreviousPage: () {
                            //       context.read<JobsCubit>().updateDisplayedJobs(state.currentPage - 1);
                            //     },
                            //     onNextPage: () {
                            //       context.read<JobsCubit>().updateDisplayedJobs(state.currentPage + 1);
                            //     },
                            //     onPageSelected: (page) {
                            //       context.read<JobsCubit>().updateDisplayedJobs(page);
                            //     },
                            //   ),
                            // ],
                          ],
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }
}
