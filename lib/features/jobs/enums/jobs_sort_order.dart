import '../../../utils/string_constants/app_strings.dart';

enum JobsSortOrder {
  newestFirst(AppStrings.newest, AppStrings.newest),
  oldestFirst(AppStrings.oldest, AppStrings.oldest),
  highToLow(AppStrings.highToLow, AppStrings.highToLowName),
  lowToHigh(AppStrings.lowToHigh, AppStrings.lowToHighName),
  responseDeadline(AppStrings.responseDeadline, AppStrings.responseDeadline);

  final String _name;
  final String displayName;

  const JobsSortOrder(this._name, this.displayName);

  @override
  String toString() => _name;
}
