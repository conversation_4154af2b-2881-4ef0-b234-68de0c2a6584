import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';

enum JobsListType {
  allJobs(AppStrings.allJobs, UserType.client),
  openJobs(AppStrings.open, UserType.client),
  decidingJobs(AppStrings.deciding, UserType.client),
  inProgress(AppStrings.inProgress, UserType.client),
  closedJobs(AppStrings.closed, UserType.client),
  hiring(AppStrings.hiring, UserType.voice),
  applied(AppStrings.applied, UserType.voice),
  shortlisted(AppStrings.shortlisted, UserType.voice),
  selected(AppStrings.selected, UserType.voice),
  history(AppStrings.history, UserType.voice),
  favourite(AppStrings.favourites, UserType.voice),
  ancillary(AppStrings.ancillary, UserType.ancillaryService);

  final String _name;
  final UserType _userType;

  const JobsListType(this._name, this._userType);

  @override
  String toString() => _name;

  static JobsListType? getJobsListTypeFromString(String type) {
    switch (type) {
      case AppStrings.allJobs:
        return JobsListType.allJobs;
      case AppStrings.open:
        return JobsListType.openJobs;
      case AppStrings.deciding:
        return JobsListType.decidingJobs;
      case AppStrings.inProgress:
        return JobsListType.inProgress;
      case AppStrings.closed:
        return JobsListType.closedJobs;
      case AppStrings.hiring:
        return JobsListType.hiring;
      case AppStrings.applied:
        return JobsListType.applied;
      case AppStrings.shortlisted:
        return JobsListType.shortlisted;
      case AppStrings.selected:
        return JobsListType.selected;
      case AppStrings.history:
        return JobsListType.history;
      case AppStrings.favourites:
        return JobsListType.favourite;
      case AppStrings.ancillary:
        return JobsListType.ancillary;
      default:
        return null;
    }
  }

  static List<JobsListType> getByUserType(UserType userType) {
    return JobsListType.values
        .where((jobType) => jobType._userType == userType)
        .toList();
  }
}
