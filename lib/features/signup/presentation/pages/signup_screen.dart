import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/signup/bloc/signup_bloc.dart';
import 'package:the_voice_directory_flutter/features/signup/bloc/signup_state.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/apple_signin_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/google_signin_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/email_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../../../../core/services/hive/hive_keys.dart';
import '../../../../core/services/hive/hive_storage_helper.dart';
import '../../../common/social_sign_in/bloc/social_login_bloc.dart';
import '../../../common/user_data/data/user_data_model.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _LoginSignUpScreenState();
}

class _LoginSignUpScreenState extends State<SignUpScreen> {
  late TextEditingController _emailController;
  final formKey = GlobalKey<FormState>();
  bool isCheckboxSelected = true;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController();
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Row(
        children: [
          if (Responsive.isDesktop(context))
            const Expanded(child: TVDBrandingScreen()),
          Expanded(
            child: Padding(
              padding: !Responsive.isDesktop(context)
                  ? EdgeInsets.zero
                  : EdgeInsets.all(44.0.h),
              child: Card(
                color: Theme.of(context).colorScheme.white,
                elevation: !Responsive.isDesktop(context) ? 0 : 8,
                shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: !Responsive.isDesktop(context) ? 16.h : 52.h,
                      vertical: !Responsive.isDesktop(context) ? 24.h : 48.h),
                  child: Form(
                    key: formKey,
                    child: Column(
                      crossAxisAlignment: !Responsive.isDesktop(context)
                          ? CrossAxisAlignment.start
                          : CrossAxisAlignment.center,
                      children: [
                        if (!Responsive.isDesktop(context))
                          Align(
                              alignment: Alignment.centerLeft,
                              child: CustomBackButton(
                                onTap: () {
                                  context.pop();
                                },
                              )),
                        if (!Responsive.isDesktop(context)) 16.ph,
                        const TextDisplayLarge36And26(AppStrings.signUp),
                        16.ph,
                        const TextTitle14(AppStrings.createAccountToContinue),
                        !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                        EmailTextField(
                          controller: _emailController,
                        ),
                        !Responsive.isDesktop(context) ? 24.ph : 36.ph,
                        BlocListener<SignUpBloc, SignUpState>(
                            listener: (context, state) {
                              if (state is SignUpLoadingState) {
                                Dialogs.showOnlyLoader(context);
                              }
                              if (state is SignUpSuccessState) {
                                // Show Success Message
                                CustomToast.show(
                                    context: context,
                                    message: AppStrings.otpSentOnEmail,
                                    isSuccess: true);
                                // Navigate to OTP Screen
                                context.pop();
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.verifyOtp,
                                    pathParameters: {
                                      Params.email: _emailController.text
                                          .trim()
                                          .toLowerCase(),
                                      Params.phoneNumber: "ND",
                                    });
                              }
                              if (state is SignUpErrorState) {
                                // Show Error Message
                                context.pop();
                                CustomToast.show(
                                    context: context,
                                    message: state.errorMsg,
                                    isSuccess: true);
                              }
                            },
                            child: PrimaryButton(
                                onPressed: () {
                                  if (formKey.currentState!.validate()) {
                                    context.read<SignUpBloc>().signUp(
                                        email: _emailController.text.trim().toLowerCase());
                                  }
                                },
                                buttonText: AppStrings.continueTxt)),
                        !Responsive.isDesktop(context) ? 24.ph : 32.ph,
                        BlocConsumer<SocialLoginBloc, SocialLoginState>(
                          listener: (context, state) {
                            if (state is SocialLoginLoading) {
                              Dialogs.showOnlyLoader(context);
                            }
                            if (state is SocialLoginSuccess) {
                              context.pop();
                              if (!(state.userDataModel.isPhoneVerified ?? true)) {
                                NavigationServiceImpl.getInstance()!.doNavigation(
                                  context,
                                  routeName: RouteName.enterInformation,
                                  pathParameters: {
                                    Params.email: state.userDataModel.email ?? '',
                                  },
                                  useGo: true,
                                );
                                return;
                              }
                              bool isClient = state.userDataModel.role == UserType.client;
                              bool isVoice = state.userDataModel.role == UserType.voice;
                              bool isAncillary = state.userDataModel.role == UserType.ancillaryService;
                              if (state.userDataModel.intermediateStep == 0) {
                                NavigationServiceImpl.getInstance()!.doNavigation(
                                  context,
                                  routeName: RouteName.createProfile,
                                  pathParameters: {
                                    Params.type: isClient
                                        ? UserType.client.getString()
                                        : isAncillary ? UserType.ancillaryService.getString() : UserType.voice.getString(),
                                  },
                                  useGo: true,
                                );
                                return;
                              }
                              if (isClient || isAncillary && state.userDataModel.intermediateStep == 1) {
                                HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 1) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.vocalCharacteristics, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 2) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.uploadSamples, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 3) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.projectType, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 4) {
                                HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
                                return;
                              }
                            }
                            if (state is SocialLoginError) {
                              context.pop();
                              CustomToast.show(
                                context: context,
                                message: state.errorMessage,
                                isSuccess: false,
                              );
                            }
                          },
                          builder: (context, state) {
                            return GoogleSignInButton(
                              buttonText: AppStrings.continueWithGoogle,
                              onPressed: () {
                                context.read<SocialLoginBloc>().signInWithGoogle();
                              },
                            );
                          },
                        ),
                        if (!kIsWeb && Platform.isIOS)...[
                          24.ph,
                          AppleSignInButton(
                            buttonText: AppStrings.signUpWithApple,
                            onPressed: () {
                              context.read<SocialLoginBloc>().signInWithApple();
                            },
                          ),
                        ],
                        24.ph,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const TextTitle18And14(
                                AppStrings.alreadyHaveAnAccount),
                            8.pw,
                            InkWell(
                              onTap: () {
                                if(kIsWeb) {
                                  context.go(RoutePath.login);
                                } else {
                                  context.pop();
                                }
                              },
                              child: TextTitle18And14(
                                AppStrings.login,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                        if (Responsive.isDesktop(context)) const Spacer(),
                        if (!Responsive.isDesktop(context)) 20.ph,
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () {
                                setState(() {
                                  isCheckboxSelected = !isCheckboxSelected;
                                });
                              },
                              child: SvgPicture.asset(
                                isCheckboxSelected
                                    ? AppImages.selectedCheckboxIc
                                    : AppImages.emptyCheckboxIc,
                                height: 24.h,
                                width: 24.h,
                              ),
                            ),
                            12.pw,
                             Expanded(
                                child: TextTitle14(
                                    AppStrings.signUpMarketing, style: 
                                    Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 12, fontWeight: FontWeight.w600))),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      )),
    );
  }
}
