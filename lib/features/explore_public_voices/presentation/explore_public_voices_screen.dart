import 'dart:async';
import 'dart:ui';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/common_app_bar.dart';
import '../../../widgets/loading_dialog.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../explore_artists/data/artist_filter_category_model.dart';
import '../../explore_artists/data/artist_filter_type.dart';
import '../../explore_artists/widgets/empty_voice_result.dart';
import '../../landing_page/bloc/explore_public_voices_cubit.dart';
import '../../landing_page/data/enums/landing_page_item_type.dart';
import '../../landing_page/widgets/audio_card.dart';
import '../../landing_page/widgets/landing_page_app_bar.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'widgets/public_filter_widget.dart';

class ExplorePublicVoicesScreen extends StatefulWidget {
  const ExplorePublicVoicesScreen({super.key});

  @override
  State<ExplorePublicVoicesScreen> createState() => _ExplorePublicVoicesScreenState();
}

class _ExplorePublicVoicesScreenState extends State<ExplorePublicVoicesScreen> {
  late final TextEditingController searchController;
  Timer? _debounceTimer;
  bool _hasSearched = false;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    context.read<ExplorePublicVoicesCubit>().initFetchArtists();
  }

  void onSearchChanges(BuildContext context, {bool forceSearch = false}) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    if (searchController.text.length < 2 && !forceSearch) {
      if (searchController.text.isEmpty && !_hasSearched) return;
      if (searchController.text.isNotEmpty) return;
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (forceSearch) FocusManager.instance.primaryFocus?.unfocus();
      context.read<ExplorePublicVoicesCubit>().updateSearchQuery(searchController.text);
      _hasSearched = searchController.text.length >= 2 || forceSearch;
    });
  }

  Set<int> _getInitialSelectedIds(ArtistFilterCategoryModel category) {
    final cubit = context.read<ExplorePublicVoicesCubit>();
    switch (category.filterType) {
      case ArtistFilterType.gender:
        return cubit.filters.genderIds?.toSet() ?? {};
      case ArtistFilterType.projectType:
        return cubit.filters.projectTypeIds?.toSet() ?? {};
      case ArtistFilterType.ageRange:
        return cubit.filters.ageRangeIds?.toSet() ?? {};
      case ArtistFilterType.language:
        return cubit.filters.languageIds?.toSet() ?? {};
      case ArtistFilterType.accent:
        return cubit.filters.accentIds?.toSet() ?? {};
      case ArtistFilterType.experience:
        return cubit.filters.experienceIds?.toSet() ?? {};
      case ArtistFilterType.services:
        return cubit.filters.servicesIds?.toSet() ?? {};
      default:
        return {};
    }
  }

  void _handleCheckboxChanged(BuildContext context, int itemId, ArtistFilterCategoryModel category) {
    final cubit = context.read<ExplorePublicVoicesCubit>();
    cubit.updateFilter(category.filterType, itemId);
  }

  void onSectionTap(LandingPageItemType type) {
    switch (type) {
      case LandingPageItemType.about:
        NavigationServiceImpl.getInstance()?.doNavigation(
          context,
          routeName: RouteName.landingPage,
          useGo: true,
          extra: LandingPageItemType.about.toString(),
        );
        break;
      case LandingPageItemType.exploreVoices:
        break;
      case LandingPageItemType.howItWorks:
        NavigationServiceImpl.getInstance()?.doNavigation(
          context,
          routeName: RouteName.landingPage,
          useGo: true,
          extra: LandingPageItemType.howItWorks.toString(),
        );
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return BlocProvider(
      create: (_) => StaticDataDropdownBloc(isFlexible: false),
      child: Scaffold(
        appBar: Responsive.isDesktop(context)
            ? null
            : CommonAppBar(
                title: AppStrings.exploreVoices.capitalizeEachWord(),
                showLoginButton: !(HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn) ?? false),
              ),
        body: BlocConsumer<StaticDataDropdownBloc, StaticDataDropdownState>(
          listener: (context, state) {
            if (state is StaticDataDropdownErrorState) {
              if (state is ExplorePublicVoicesErrorState) {
                CustomToast.show(context: context, message: state.errorMsg);
              }
            }
          },
          builder: (context, dropDownState) {
            if (dropDownState is StaticDataDropdownLoadingState) {
              return Column(
                children: [
                  if (Responsive.isDesktop(context)) ...[
                    LandingPageAppBar(
                      onSectionTap: onSectionTap,
                    ),
                  ],
                  Expanded(
                    child: const Center(
                      child: Loader(),
                    ),
                  ),
                ],
              );
            }
            return BlocConsumer<ExplorePublicVoicesCubit, ExplorePublicVoicesState>(
              listener: (context, state) {
                if (state is ExplorePublicVoicesErrorState) {
                  CustomToast.show(context: context, message: state.errorMsg);
                }
              },
              builder: (context, state) {
                if (state is ExplorePublicVoicesInitialState) {
                  return Column(
                    children: [
                      if (Responsive.isDesktop(context)) ...[
                        LandingPageAppBar(
                          onSectionTap: onSectionTap,
                        ),
                      ],
                      Expanded(
                        child: const Center(
                          child: Loader(),
                        ),
                      ),
                    ],
                  );
                }
                return Theme(
                  data: theme.copyWith(
                    scrollbarTheme: ScrollbarThemeData(
                      thickness: WidgetStatePropertyAll(10.h),
                      thumbVisibility: WidgetStatePropertyAll(
                        Responsive.isDesktop(context),
                      ),
                      mainAxisMargin: 20,
                      thumbColor: WidgetStatePropertyAll(
                        Responsive.isDesktop(context)
                            ? colorScheme.lightGreyB2B2B2
                            : Colors.transparent,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      if (Responsive.isDesktop(context)) ...[
                        LandingPageAppBar(
                          onSectionTap: onSectionTap,
                        ),
                      ],
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(
                            left: Responsive.isDesktop(context) ? 80.w : 16.w,
                            right: Responsive.isDesktop(context) ? 80.w : 16.w,
                            top: Responsive.isDesktop(context) ? 40.h : 4.h,
                            // bottom: Responsive.isDesktop(context) ? 32.h : 0,
                          ),
                          child: Column(
                            children: [
                              if (Responsive.isDesktop(context)) ...[
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 10,
                                      child: TextDisplayLarge36And26(
                                        AppStrings.exploreVoices.capitalizeEachWord(),
                                        color: colorScheme.primaryGrey,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 7,
                                      child: AppTextFormField(
                                        maxLength: 100,
                                        maxLines: 1,
                                        controller: searchController,
                                        hintText: AppStrings.searchArtistsHintText,
                                        hintTextFontWeight: FontWeight.w500,
                                        hintTextColor: colorScheme.darkGrey525252,
                                        prefixIcon: Padding(
                                          padding: EdgeInsets.all(14.h),
                                          child: SvgPicture.asset(AppImages.searchIc),
                                        ),
                                        onChanged: (_) {
                                          setState(() {});
                                          onSearchChanges(context);
                                        },
                                        suffixIcon: searchController.text.isNotEmpty
                                            ? GestureDetector(
                                                onTap: () {
                                                  searchController.clear();
                                                  setState(() {});
                                                  onSearchChanges(context, forceSearch: true);
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.all(8.0.h),
                                                  child: SvgPicture.asset(AppImages.closeSquareIc),
                                                ),
                                              )
                                            : null,
                                      ),
                                    ),
                                  ],
                                )
                              ],
                              Responsive.isDesktop(context) ? 40.ph : 20.ph,
                              if (!Responsive.isDesktop(context)) ...[
                                Row(
                                  children: [
                                    Expanded(
                                      child: AppTextFormField(
                                        maxLength: 100,
                                        maxLines: 1,
                                        controller: searchController,
                                        hintText: AppStrings.searchArtistsHintText,
                                        hintTextFontWeight: FontWeight.w500,
                                        hintTextColor: colorScheme.darkGrey525252,
                                        prefixIcon: Padding(
                                          padding: EdgeInsets.all(14.h),
                                          child: SvgPicture.asset(AppImages.searchIc),
                                        ),
                                        onChanged: (_) {
                                          setState(() {});
                                          onSearchChanges(context);
                                        },
                                        suffixIcon: searchController.text.isNotEmpty
                                            ? GestureDetector(
                                                onTap: () {
                                                  searchController.clear();
                                                  setState(() {});
                                                  onSearchChanges(context, forceSearch: true);
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.all(8.0.h),
                                                  child: SvgPicture.asset(AppImages.closeSquareIc),
                                                ),
                                              )
                                            : null,
                                      ),
                                    ),
                                    12.pw,
                                    InkWell(
                                      onTap: () async {
                                        if (dropDownState is StaticDataDropdownSuccessState &&
                                            dropDownState.dropDownResponseModel != null) {
                                          await showModalBottomSheet(
                                            context: context,
                                            backgroundColor: Colors.transparent,
                                            isScrollControlled: true,
                                            elevation: 0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.vertical(
                                                top: Radius.circular(20.r),
                                              ),
                                            ),
                                            builder: (_) {
                                              return ClipRRect(
                                                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                                                child: BackdropFilter(
                                                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                                  child: DraggableScrollableSheet(
                                                    initialChildSize: 0.5,
                                                    minChildSize: 0.4,
                                                    maxChildSize: 0.75,
                                                    expand: false,
                                                    builder: (_, scrollController) {
                                                      return BlocProvider.value(
                                                        value: BlocProvider.of<ExplorePublicVoicesCubit>(context),
                                                        child: BlocBuilder<ExplorePublicVoicesCubit, ExplorePublicVoicesState>(
                                                          builder: (context, state) {
                                                            return PublicFilterWidget(
                                                              dropDownResponseModel: dropDownState.dropDownResponseModel!,
                                                              onResetFilters: () {
                                                                context.read<ExplorePublicVoicesCubit>().resetFilters();
                                                              },
                                                              currentlySelectedIds: (category) => _getInitialSelectedIds(category),
                                                              onCheckboxChanged: (id, category) {
                                                                return _handleCheckboxChanged(context, id, category);
                                                              },
                                                            );
                                                          }
                                                        ),
                                                      );
                                                    }
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        }
                                      },
                                      borderRadius: BorderRadius.circular(24.h),
                                      child: Stack(
                                        children: [
                                          Container(
                                            width: 44.h,
                                            height: 44.h,
                                            decoration: BoxDecoration(
                                              color: colorScheme.white,
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: colorScheme.lightGreyD9D9D9,
                                              ),
                                            ),
                                            child: Center(
                                              child: SvgPicture.asset(
                                                AppImages.filtersIc,
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 3,
                                            right: 2,
                                            child: Builder(builder: (context) {
                                              final cubit = context.watch<ExplorePublicVoicesCubit>();
                                              final isFilterApplied = cubit.filters.isAnyFilterApplied;

                                              if (!isFilterApplied) return const SizedBox.shrink();
                                              return Container(
                                                width: 10,
                                                height: 10,
                                                decoration: BoxDecoration(
                                                  color: Colors.red,
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color: Colors.white,
                                                    width: 1.5,
                                                  ),
                                                ),
                                              );
                                            }),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                              if (!Responsive.isDesktop(context)) 20.ph,
                              Builder(builder: (context) {
                                return Expanded(
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Left filters panel - always visible
                                      if (Responsive.isDesktop(context) &&
                                          dropDownState is StaticDataDropdownSuccessState &&
                                          dropDownState.dropDownResponseModel != null)
                                        PublicFilterWidget(
                                          dropDownResponseModel: dropDownState.dropDownResponseModel!,
                                          onResetFilters: () {
                                            context.read<ExplorePublicVoicesCubit>().resetFilters();
                                          },
                                          currentlySelectedIds: (category) {
                                            return _getInitialSelectedIds(category);
                                          },
                                          onCheckboxChanged: (id, category) {
                                            return _handleCheckboxChanged(context, id, category);
                                          },
                                        ),
                                      
                                      // Main content area
                                      Expanded(
                                        child: Builder(
                                          builder: (context) {
                                            if (state is ExplorePublicVoicesLoadingState) {
                                              return Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Column(
                                                    children: [
                                                      Responsive.isDesktop(context) ? 220.ph : 120.ph,
                                                      const Loader(),
                                                    ],
                                                  ),
                                                ],
                                              );
                                            }
                                            if (state is ExplorePublicVoicesSuccessState) {
                                              if (state.artistsList.isEmpty) {
                                                return EmptyVoiceResult(isSearchLocationBased: false);
                                              }
                                              if (Responsive.isDesktop(context)) {
                                                return GridView.builder(
                                                  padding: Responsive.isDesktop(context)
                                                      ? EdgeInsets.symmetric(horizontal: 24.w, vertical: 3.h)
                                                      : EdgeInsets.only(bottom: 20.h),
                                                  physics: const BouncingScrollPhysics(),
                                                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                                    mainAxisExtent: Responsive.isDesktop(context) ? 160.h : null,
                                                    crossAxisCount: Responsive.isDesktop(context) ? 2 : 1,
                                                    mainAxisSpacing: Responsive.isDesktop(context) ? 24.h : 16.h,
                                                    crossAxisSpacing: Responsive.isDesktop(context) ? 25.w : 0,
                                                  ),
                                                  itemCount: state.artistsList.length,
                                                  itemBuilder: (context, index) {
                                                    return AudioCard(
                                                      user: state.artistsList[index],
                                                    );
                                                  },
                                                );
                                              }
                                              return ListView.builder(
                                                physics: const BouncingScrollPhysics(),
                                                itemCount: state.artistsList.length,
                                                itemBuilder: (context, index) {
                                                  return Padding(
                                                    padding: EdgeInsets.only(bottom: 16.h),
                                                    child: AudioCard(
                                                      user: state.artistsList[index],
                                                    ),
                                                  );
                                                },
                                              );
                                            }
                                            return const SizedBox.shrink();
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
        bottomNavigationBar: Builder(
          builder: (context) {
            final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn) ?? false;
            if (!Responsive.isDesktop(context) && !isUserLoggedIn) {
              return Container(
                decoration: BoxDecoration(
                  color: colorScheme.white,
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.black.withOpacity(0.25),
                      blurRadius: 4.r,
                      offset: const Offset(1, 0),
                    ),
                  ],
                ),
                padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 12.h),
                child: Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    children: [
                      TextSpan(
                        text: AppStrings.login,
                        style: textTheme.displayLarge?.copyWith(
                          color: colorScheme.hyperlinkBlueColor,
                          fontSize: 16.sp,
                        ),
                        recognizer: TapGestureRecognizer()..onTap = () {
                          NavigationServiceImpl.getInstance()!.doNavigation(
                            context,
                            routeName: RouteName.login,
                          );
                        },
                      ),
                      TextSpan(
                        text: ' or ',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.darkGrey525252,
                        ),
                      ),
                      TextSpan(
                        text: AppStrings.signUp,
                        style: textTheme.displayLarge?.copyWith(
                          color: colorScheme.hyperlinkBlueColor,
                          fontSize: 16.sp,
                        ),
                        recognizer: TapGestureRecognizer()..onTap = () {
                          NavigationServiceImpl.getInstance()!.doNavigation(
                            context,
                            routeName: RouteName.signUp,
                          );
                        },
                      ),
                      TextSpan(
                        text: AppStrings.toPersonalizeYourExperience,
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.darkGrey525252,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
