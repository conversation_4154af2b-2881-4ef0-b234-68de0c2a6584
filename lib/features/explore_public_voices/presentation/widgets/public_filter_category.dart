import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../explore_artists/data/artist_filter_category_model.dart';

class PublicFilterCategory extends StatelessWidget {
  final ArtistFilterCategoryModel category;
  final Set<int> currentlySelectedIds;
  final Function(int, ArtistFilterCategoryModel) onCheckboxChanged;

  const PublicFilterCategory({
    super.key,
    required this.category,
    required this.currentlySelectedIds,
    required this.onCheckboxChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (category.items.isEmpty) return SizedBox.shrink();
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          child: TextTitle18And14(
            category.filterType.toString(),
            fontSize: Responsive.isDesktop(context) ? 18.sp : 16.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        Column(
          children: category.items.asMap().entries.map((entry) {
            final item = entry.value;

            bool isSelected = currentlySelectedIds.contains(item.id);

            return Theme(
              data: Theme.of(context).copyWith(
                checkboxTheme: CheckboxThemeData(
                  fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colorScheme.checkBoxLemonGreen;
                    }
                    return states.contains(WidgetState.hovered)
                        ? colorScheme.lightGreyB2B2B2.withOpacity(0.1)
                        : Colors.transparent;
                  }),
                  checkColor: WidgetStateProperty.all<Color>(colorScheme.white),
                  side: WidgetStateBorderSide.resolveWith((states) => BorderSide(
                    width: 1.2,
                    color: states.contains(WidgetState.selected)
                        ? colorScheme.checkBoxLemonGreen
                        : colorScheme.lightGreyB2B2B2,
                  )),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 11.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 24.h,
                      height: 24.h,
                      child: Checkbox(
                        value: isSelected,
                        onChanged: (bool? newValue) {
                          onCheckboxChanged.call(item.id, category);
                        },
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    12.pw,
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          onCheckboxChanged.call(item.id, category);
                        },
                        child: TextTitle14(item.name ?? ''),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
 