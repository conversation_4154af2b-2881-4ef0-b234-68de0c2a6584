import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../explore_artists/data/artist_filter_category_model.dart';
import '../../../explore_artists/data/artist_filter_type.dart';
import 'public_filter_category.dart';

class PublicFilterWidget extends StatelessWidget {
  final DropDownResponseModel dropDownResponseModel;
  final ScrollController? scrollController;
  final VoidCallback onResetFilters;
  final Set<int> Function(ArtistFilterCategoryModel category) currentlySelectedIds;
  final Function(int, ArtistFilterCategoryModel) onCheckboxChanged;

  const PublicFilterWidget({
    super.key,
    required this.dropDownResponseModel,
    this.scrollController,
    required this.onResetFilters,
    required this.currentlySelectedIds,
    required this.onCheckboxChanged,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    final List<ArtistFilterCategoryModel> filterCategories = [
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.gender,
        items: dropDownResponseModel.gender ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.projectType,
        items: dropDownResponseModel.projectType ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.ageRange,
        items: dropDownResponseModel.ageRange ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.language,
        items: dropDownResponseModel.voiceLanguage ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.accent,
        items: dropDownResponseModel.accent ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.experience,
        items: dropDownResponseModel.experience ?? [],
      ),
    ];
    return SingleChildScrollView(
      controller: scrollController,
      child: Container(
        width: Responsive.isDesktop(context) ? 239.w : null,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            28.ph,
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 28.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text24And20SemiBold(AppStrings.filter.toUpperCase()),
                  InkWell(
                    onTap: () {
                      onResetFilters.call();
                      if (!Responsive.isDesktop(context)) context.pop(); // Close bottom sheet
                    },
                    child: TextTitle18And14(
                      AppStrings.reset,
                      color: colorScheme.hyperlinkBlueColor,
                    ),
                  ),
                ],
              ),
            ),
            ...filterCategories.map(
              (category) => PublicFilterCategory(
                category: category,
                currentlySelectedIds: currentlySelectedIds(category),
                onCheckboxChanged: onCheckboxChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
