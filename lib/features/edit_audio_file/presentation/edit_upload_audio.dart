import 'dart:typed_data';

import 'package:audioplayers/audioplayers.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_state.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_samples_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_samples_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../upload_audio/bloc/upload_audio_video_cubit.dart';

class EditUploadAudioScreen extends StatefulWidget {
  const EditUploadAudioScreen({super.key});

  @override
  State<EditUploadAudioScreen> createState() => _EditUploadAudioScreenState();
}

class _EditUploadAudioScreenState extends State<EditUploadAudioScreen> {
  bool _isAudioListLoading = false;

  @override
  void initState() {
    super.initState();
    context.read<UserBloc>().getUserData();
    _isAudioListLoading = true;
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocConsumer<UploadAudioVideoCubit, UploadAudioVideoState>(
      listener: (context, state) {
        if (state.errorMessage != null && state.errorMessage!.isNotEmpty) {
          CustomToast.show(context: context, message: state.errorMessage!);
          context.read<UploadAudioVideoCubit>().resetError();
        }
          if (state.audioFiles.isNotEmpty) {
          setState(() {
            _isAudioListLoading = false;
          });
        }
      },
      builder: (context, state) {
        final cubit = context.read<UploadAudioVideoCubit>();
        return BlocListener<UserBloc, UserState>(
          listener: (context, userState) {
            if (userState is UserLoadingState) {
              const Center(child: Loader());
            }
            if (userState is UserSuccessState) {
              cubit.initWithUserUploadedAudios(userState);
            }
            if (userState is UserErrorState) {
              ErrorScreen(
                  onRetry: () {
                    context.read<UserBloc>().getUserData();
                  },
                  errorMessage: userState.errorMsg,
                  imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                      height: 200, width: 100));
            }
          },
          child: Scaffold(
            body: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: !Responsive.isDesktop(context)
                          ? EdgeInsets.zero
                          : EdgeInsets.all(44.0.h),
                      child: Card(
                        color: Theme.of(context).colorScheme.white,
                        elevation: !Responsive.isDesktop(context) ? 0 : 8,
                        shadowColor:
                            Theme.of(context).colorScheme.lightGreyD9D9D9,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal:
                                !Responsive.isDesktop(context) ? 16.h : 60.h,
                            vertical:
                                !Responsive.isDesktop(context) ? 24.h : 40.h,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (!Responsive.isDesktop(context)) ...[
                                Row(
                                  children: [
                                    CustomBackButton(
                                      onTap: () {
                                        context.pop();
                                      },
                                    ),
                                    8.pw,
                                    const Expanded(
                                      child: Center(
                                          child: Text24And20SemiBold(
                                              AppStrings
                                                  .editUploadAudio)),
                                    ),
                                    SizedBox(width: 32.w)
                                  ],
                                ),
                              ] else
                                Row(
                                  children: [
                                    const TextDisplayLarge36And26(
                                        AppStrings.editUploadAudio),
                                    const Spacer(),
                                    InkWell(
                                      onTap: () {
                                        context.pop();
                                      },
                                      child: TextTitle18And14(
                                        AppStrings.cancel,
                                        color: colorScheme.hyperlinkBlueColor,
                                      ),
                                    ),
                                  ],
                                ),
                              !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                              Align(
                                alignment: Alignment.center,
                                child: DottedBorder(
                                  borderType: BorderType.RRect,
                                  radius: Radius.circular(12.r),
                                  padding: EdgeInsets.all(20.h),
                                  color: Colors.blue,
                                  strokeWidth: 2,
                                  dashPattern: const [10, 10],
                                  child: GestureDetector(
                                    onTap: () async => cubit.browseFiles(context, maxFiles: 10),
                                    child: Container(
                                      width: 441,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(8.0.r),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(AppImages.export),
                                          12.ph,
                                          TextTitle14(
                                            AppStrings.browseFileToUploadFileType(),
                                            textAlign: TextAlign.center,
                                          ),
                                          12.ph,
                                          PrimaryButton(
                                            width: 164.w,
                                            height: 44.h,
                                            backgroundColor:
                                                colorScheme.lightGreenFDFFDA,
                                            onPressed: () async {
                                              if (state.audioFiles.length < 10) {
                                                cubit.browseFiles(context, maxFiles: 10);
                                              } else {
                                                CustomToast.show(
                                                  context: context,
                                                  message: AppStrings.youCanUploadMaximum10Files,
                                                );
                                              }
                                            },
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(40.r),
                                              side: BorderSide(
                                                color: colorScheme.primary,
                                                width: 1.2.h,
                                              ),
                                            ),
                                            child: TextTitle14(
                                              AppStrings.browseFile,
                                              style: theme.textTheme.titleMedium
                                                  ?.copyWith(fontSize: 16.sp),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                              Expanded(
                                child: _isAudioListLoading || (state.audioFiles.isNotEmpty && state.durations.any((duration) => duration == Duration.zero))
                                    ? const Center(child: Loader())
                                    : ListView.builder(
                                  itemCount: state.audioFiles.length,
                                  itemBuilder: (context, index) {
                                    final isPlaying =
                                        state.audioPlayers[index].state ==
                                            PlayerState.playing;
                                    final duration = state.durations[index];
                                    final position = state.positions[index];
                                    return Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 8.h),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Expanded(
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 9.h),
                                              decoration: BoxDecoration(
                                                color: colorScheme.white,
                                                borderRadius:
                                                    BorderRadius.circular(12.r),
                                                border: Border.all(
                                                    color: colorScheme
                                                        .lightGreyD9D9D9),
                                              ),
                                              child: Row(
                                                children: [
                                                  IconButton(
                                                    icon: SvgPicture.asset(
                                                      isPlaying
                                                          ? AppImages.pauseIcon
                                                          : AppImages.play,
                                                    ),
                                                    onPressed: () =>
                                                        cubit.playAudio(index),
                                                  ),
                                                  Expanded(
                                                    child: Row(
                                                      children: [
                                                        Expanded(
                                                          child: SliderTheme(
                                                            data:
                                                                SliderThemeData(
                                                              thumbShape:
                                                                  RoundSliderThumbShape(
                                                                      enabledThumbRadius:
                                                                          6.r),
                                                            ),
                                                            child: Slider(
                                                              thumbColor:
                                                                  colorScheme
                                                                      .white,
                                                              activeColor:
                                                                  colorScheme
                                                                      .primary,
                                                              inactiveColor:
                                                                  colorScheme
                                                                      .lightGreyD9D9D9,
                                                              value: position
                                                                  .inSeconds
                                                                  .toDouble(),
                                                              max: duration
                                                                  .inSeconds
                                                                  .toDouble(),
                                                              onChanged:
                                                                  (value) async {
                                                                final newPosition =
                                                                    Duration(
                                                                        seconds:
                                                                            value.toInt());
                                                                await state
                                                                    .audioPlayers[
                                                                        index]
                                                                    .seek(
                                                                        newPosition);
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                        TextTitle14(
                                                          isPlaying
                                                              ? "${(duration - position).inMinutes}:${((duration - position).inSeconds % 60).toString().padLeft(2, '0')}"
                                                              : "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          IconButton(
                                            icon: SvgPicture.asset(
                                                AppImages.trash),
                                            onPressed: () =>
                                                cubit.deleteAudio(index),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                              MultiBlocListener(
                                listeners: [
                                  BlocListener<UploadMediaBloc,
                                      UploadMediaState>(
                                    listener: (context, uploadState) {
                                      if (uploadState
                                          is UploadMediaLoadingState) {
                                        Dialogs.showOnlyLoader(context);
                                      }
                                      if (uploadState
                                          is UploadMediaSuccessState) {
                                        context.pop();
                                        context
                                            .read<UploadSamplesBloc>()
                                            .submitUploadSamples(
                                              userInfoRequestModel:
                                                  UserInfoRequestModel(
                                                audioPath: uploadState
                                                    .getPreSignedUrlModel
                                                    ?.presignedUrls
                                                    ?.voiceAudio
                                                    ?.map((audio) => audio.path)
                                                    .toList(),
                                                audioPathToDelete: state
                                                    .deletedInitialAudioIds,
                                              ),
                                            );
                                      }
                                      if (uploadState
                                          is UploadMediaErrorState) {
                                        context.pop();
                                        CustomToast.show(
                                          context: context,
                                          message: uploadState.errorMsg,
                                          isSuccess: true,
                                        );
                                      }
                                    },
                                  ),
                                  BlocListener<UploadSamplesBloc,
                                      UploadSamplesState>(
                                    listener: (context, state) {
                                      if (state is UploadSamplesLoadingState) {
                                        Dialogs.showOnlyLoader(context);
                                      }
                                      if (state is UploadSamplesSuccessState) {
                                        CustomToast.show(
                                          context: context,
                                          message:
                                              AppStrings.audioSampleSuccess,
                                          isSuccess: true,
                                        );
                                        context.read<UserBloc>().getUserData();
                                        context.pop();
                                        context.pop();
                                      }
                                      if (state is UploadSamplesErrorState) {
                                        context.pop();
                                        CustomToast.show(
                                          context: context,
                                          message: state.errorMsg,
                                          isSuccess: true,
                                        );
                                      }
                                    },
                                  ),
                                ],
                                child: Column(
                                  children: [
                                    if (!!Responsive.isDesktop(context)) ...[
                                      40.ph,
                                      Divider(color: colorScheme.lightGreyD9D9D9,thickness: 1),
                                    ],
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 20),
                                      child: Align(
                                        alignment: Alignment.center,
                                        child: PrimaryButton(
                                          width: !Responsive.isDesktop(context)
                                              ? null
                                              : MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  .6,
                                          onPressed: () async {
                                            for (var player in state.audioPlayers) {
                                              if (player.state == PlayerState.playing) {
                                                await player.stop();
                                              }
                                            }
                                            final newAudioFiles = state
                                                .audioFiles
                                                .where(
                                                    (item) => item.id == null)
                                                .toList();

                                            if (state.audioFiles.isEmpty) {
                                              cubit.showError(AppStrings
                                                  .plsSelectAudioFile);
                                              return;
                                            }

                                            if (state.deletedInitialAudioIds
                                                    .isEmpty &&
                                                newAudioFiles.isEmpty) {
                                              cubit.showError(
                                                  AppStrings.noChangesMade);
                                              return;
                                            }
                                            context
                                                .read<UploadMediaBloc>()
                                                .getPreSignedUrl(
                                                  contentType: "audio/mp3",
                                                  preSignedUrlReqModel:
                                                      PreSignedUrlReqModel(
                                                    voiceAudio: newAudioFiles
                                                        .map(
                                                          (item) =>
                                                              MediaDetails(
                                                            extn: item.ext,
                                                            fileName: item.path
                                                                ?.split('/')
                                                                .last,
                                                            fileType: "audio",
                                                          ),
                                                        )
                                                        .toList(),
                                                  ),
                                                  voiceAudio: newAudioFiles
                                                      .map((item) => item.bytes)
                                                      .whereType<Uint8List>()
                                                      .toList(),
                                                );
                                          },
                                          buttonText: AppStrings.save,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
