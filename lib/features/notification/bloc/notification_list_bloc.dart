import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_list_state.dart';
import 'package:the_voice_directory_flutter/features/notification/data/notification_list_model.dart';

import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class NotificationListBloc extends Cubit<NotificationListState> {
  NotificationListBloc() : super(NotificationInitState());

  Future<void> getNotificationList(bool? isPrivate) async {
    emit(NotificationListLoadingState());
    try {
      final response =
          await ApiService.instance.getNotificationList(isPrivate);
      if (response.success) {
        final List<dynamic> notificationList = response.data as List<dynamic>;
        final notification = NotificationListModel.fromJson({'data': notificationList});
        emit(NotificationListSuccessState([notification]));
      } else {
        emit(NotificationListErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(NotificationListErrorState(AppStrings.genericErrorMsg));
    }
  }
}
