import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/read_notification_state.dart';

import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class ReadNotificationBloc extends Cubit<ReadNotificationState> {
  ReadNotificationBloc() : super(ReadNotificationInitState());

  Future<void> readNotification({int? id, required String? isReadType}) async {
    emit(ReadNotificationLoadingState());
    try {
      final response =
          await ApiService.instance.readNotification(id, isReadType);
      if (response.success) {
        emit(ReadNotificationSuccessState());
      } else {
        emit(ReadNotificationErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ReadNotificationErrorState(AppStrings.genericErrorMsg));
    }
  }
}
