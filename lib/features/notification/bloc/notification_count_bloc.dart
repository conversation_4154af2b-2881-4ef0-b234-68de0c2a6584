import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_count_state.dart';
import 'package:the_voice_directory_flutter/features/notification/data/notification_count_model.dart';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:flutter/foundation.dart';

import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class NotificationCountBloc extends Cubit<NotificationCountState> {
  NotificationCountBloc() : super(NotificationInitState());

  Future<void> getNotificationCount() async {
    emit(NotificationCountLoadingState());
    try {
      final response = await ApiService.instance.getNotificationCount();
      if (response.success) {
        final notification = NotificationCountModel.fromJson(response.data);
        if (!kIsWeb) {
          AppBadgePlus.updateBadge(notification.totalUnreadCount ?? 0);
        }
        emit(NotificationCountSuccessState(notification));
      } else {
        emit(NotificationCountErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(NotificationCountErrorState(AppStrings.genericErrorMsg));
    }
  }
}
