import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_count_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_list_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_list_state.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/read_notification_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/read_notification_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/notification_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class NotificationListScreen extends StatefulWidget {
  final bool isDialog;
  final bool isPrivate;
  

  const NotificationListScreen({
    super.key,
    this.isDialog = false,
    this.isPrivate = false,
  });

  static Future<void> showAsDialog(BuildContext context, {bool isPrivate = false}) async {
    await showDialog(
      context: context,
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return NotificationDialog(isPrivate: isPrivate);
      },
    );
  }

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {

  @override
  void initState() {
    super.initState();
    context.read<NotificationListBloc>().getNotificationList(widget.isPrivate);
  }
  
  String _formatDate(String dateString) {
    final DateTime date = DateTime.parse(dateString);
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(date);
    if (difference.inMinutes < 1) {
      return 'Now';
    }
    if (difference.inHours < 1) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'min' : 'mins'} ago';
    }
    if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    }
    if (difference.inHours < 48) {
      return 'Yesterday';
    }
    return DateFormat('dd/MM/yyyy').format(date);
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;

    return Scaffold(
      body: SafeArea(
        child: BlocListener<ReadNotificationBloc, ReadNotificationState>(
          listener: (context, state) {
            if (state is ReadNotificationLoadingState) {
            } else if (state is ReadNotificationSuccessState) {
              context.read<NotificationCountBloc>().getNotificationCount();
            } else if (state is ReadNotificationErrorState) {}
          },
          child: BlocBuilder<NotificationListBloc, NotificationListState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!widget.isDialog)
                    Padding(
                      padding: EdgeInsets.only(
                          left: 16.w, right: 16.w, bottom: 44.h, top: 16.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextDisplayLarge24And16(
                            widget.isPrivate ? AppStrings.privateInvite : AppStrings.notifications,
                          ),
                          CommonCircleIcon(
                            iconPath: AppImages.closeIcon,
                            containerSize: 32.h,
                            iconSize: 32.h,
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  Expanded(
                    child: _buildNotificationContent(
                        state, colorScheme, textTheme, widget.isPrivate),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationContent(
    NotificationListState state,
    ColorScheme colorScheme,
    TextTheme textTheme,
    bool isPrivate,
  ) {
    if (state is NotificationListLoadingState) {
      return const Loader();
    } else if (state is NotificationListErrorState) {
      return ErrorScreen(
        onRetry: () {
          context.read<NotificationListBloc>().getNotificationList(widget.isPrivate);
        },
      );
    } else if (state is NotificationListSuccessState) {
      var notifications = state.notificationList.first.data ?? [];
      context.read<ReadNotificationBloc>().readNotification(isReadType: widget.isPrivate ? 'private' : 'others');
      if (notifications.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(AppImages.notificationEmptyIcon),
              16.ph,
              TextTitle18And20(
                AppStrings.noNotificationsYet,
                style: textTheme.titleLarge?.copyWith(
                  color: colorScheme.textfieldTitleColor,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return Column(
            children: [
              InkWell(
                onTap: () {
                  // if (notification.isRead == false) {
                  //   _readNotificationBloc.readNotification(notification.id, true);
                  // }
                  if (widget.isDialog) {
                    Navigator.pop(context);
                  }
                  NavigationServiceImpl.getInstance()!.doNavigation(context,
                      routeName: notification.routeName ?? '',
                      pathParameters: {
                        Params.id:
                            notification.extraData?.jobId.toString() ?? '',
                        Params.showApplicantTab: notification.module == 'Job Applied' ? 'true' : 'false',
                      },
                  );
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: widget.isDialog ? 10.h : 7.h,
                    horizontal: widget.isDialog ? 10.w : 16.w,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Stack(
                        children: [
                          Container(
                            width: 32.w,
                            height:  32.h,
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: SizedBox(
                                width: widget.isDialog ? 16.w : 20.w,
                                height: widget.isDialog ? 16.h : 20.h,
                                child: SvgPicture.asset(
                                  AppImages.notificationIc,
                                  color: colorScheme.primaryGrey,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ),
                          if (notification.isRead == false)
                            Positioned(
                              right: widget.isDialog ? 2.h : 2.h,
                              top: widget.isDialog ? 1.h : 0.h,
                              child: Container(
                                width: 8.w,
                                height: 8.h,
                                decoration: BoxDecoration(
                                  color: colorScheme.black,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                        ],
                      ),
                      12.pw,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextTitle18And14(
                              notification.description ?? '',
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                            12.ph,
                            TextBodySmall12(
                              _formatDate(notification.createdAt ?? ''),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (index < notifications.length - 1)
                Divider(
                  color: colorScheme.primary,
                ),
            ],
          );
        },
      );
    }
    return const SizedBox();
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(0, size.height)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);

    // Draw shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, shadowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
