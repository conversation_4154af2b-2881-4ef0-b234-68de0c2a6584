class NotificationCountModel {
  int? privateUnreadCount;
  int? othersUnreadCount;
  int? totalUnreadCount;

  NotificationCountModel({
    this.privateUnreadCount,
    this.othersUnreadCount,
    this.totalUnreadCount,
  });

  NotificationCountModel.fromJson(Map<String, dynamic> json) {
    privateUnreadCount = json['private_unread_count'] ?? 0;
    othersUnreadCount = json['others_unread_count'] ?? 0;
    totalUnreadCount = json['total_unread_count'] ?? 0;
  }
}

