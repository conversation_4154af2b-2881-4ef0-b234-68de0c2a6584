import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class StaticDataDropdownBloc extends Cubit<StaticDataDropdownState> {
  final bool? isFlexible;
  
  StaticDataDropdownBloc({this.isFlexible}) : super(StaticDataDropdownInitState()){
    dropDownListApi();
  }

  void dropDownListApi() async {
    emit(StaticDataDropdownLoadingState());
    try {
      final response = await ApiService.instance.dropDowns(isFlexible: isFlexible);
      if (response.success) {
        emit(StaticDataDropdownSuccessState(dropDownResponseModel: DropDownResponseModel.fromJson(response.data)));
      } else {
        emit(StaticDataDropdownErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(StaticDataDropdownErrorState(AppStrings.genericErrorMsg));
    }
  }
}
