import 'package:flutter/material.dart';

import '../../../utils/responsive.dart';
import 'widgets/post_job_mobile_responsive.dart';
import 'widgets/post_job_web_responsive.dart';

class PostAJobScreen extends StatefulWidget {
  final bool isEditing;

  const PostAJobScreen({
    super.key,
    this.isEditing = false,
  });

  @override
  State<PostAJobScreen> createState() => _PostAJobScreenState();
}

class _PostAJobScreenState extends State<PostAJobScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: !Responsive.isDesktop(context)
            ? const PostJobMobileResponsive()
            : const PostJobWebResponsive(),
      ),
    );
  }
}
