import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../widgets/buttons/back_button.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';

class CardPageFrame extends StatelessWidget {
  final List<Widget> children;
  final Key? formKey;
  final String? titleText;
  final String? buttonText;
  final void Function()? onPressed;
  final EdgeInsetsGeometry? margin;

  const CardPageFrame({
    super.key,
    this.children = const <Widget>[],
    this.formKey,
    this.titleText,
    this.buttonText,
    this.onPressed,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    // Get keyboard height
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return Responsive.isDesktop(context)
        ? SafeArea(
            child: Form(
              key: formKey,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Container(
                    margin: margin,
                    constraints: const BoxConstraints.expand(),
                    decoration: BoxDecoration(
                      color: colorScheme.white,
                      borderRadius: BorderRadius.circular(16.w),
                      boxShadow: Responsive.isDesktop(context)
                          ? [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 2,
                                blurRadius: 5,
                                offset: const Offset(0, 3),
                              ),
                            ]
                          : null,
                    ),
                    child: Theme(
                      data: theme.copyWith(
                        scrollbarTheme: ScrollbarThemeData(
                          thickness: WidgetStatePropertyAll(10.h),
                          thumbVisibility: WidgetStatePropertyAll(
                            Responsive.isDesktop(context),
                          ),
                          mainAxisMargin: 20,
                          thumbColor: WidgetStatePropertyAll(
                            Responsive.isDesktop(context)
                                ? colorScheme.lightGreyB2B2B2
                                : Colors.transparent,
                          ),
                        ),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
                        controller: scrollController,
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            40.ph,
                            if (titleText != null)
                              TextDisplayLarge36And26(titleText!),
                            !Responsive.isDesktop(context) ? 24.ph : 40.ph,
                            ...children,
                            // Add extra padding when keyboard is visible to prevent button overlap
                            isKeyboardVisible && !Responsive.isDesktop(context)
                                ? SizedBox(height: keyboardHeight + 40.h)
                                : SizedBox(
                                    height: !Responsive.isDesktop(context)
                                        ? 24.h
                                        : 40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: margin,
                    decoration: BoxDecoration(
                      color: colorScheme.white,
                      borderRadius: !Responsive.isDesktop(context)
                          ? null
                          : BorderRadius.only(
                              bottomLeft: Radius.circular(16.r),
                              bottomRight: Radius.circular(16.r),
                            ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (Responsive.isDesktop(context))
                          Divider(
                            thickness: 0.5.h,
                            height: 0.5.h,
                            color: colorScheme.lightGreyD9D9D9,
                          ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 206.w,
                            vertical: 20.h,
                          ),
                          child: PrimaryButton(
                            onPressed: onPressed ?? () {},
                            buttonText: buttonText,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        : SafeArea(
            child: Form(
              key: formKey,
              child: Column(
                // Main Column
                children: [
                  Expanded(
                    // Scrollable Content
                    child: Container(
                      margin: margin,
                      decoration: BoxDecoration(
                        color: colorScheme.white,
                        borderRadius: BorderRadius.circular(16.w),
                        boxShadow: Responsive.isDesktop(context)
                            ? [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.5),
                                  spreadRadius: 2,
                                  blurRadius: 5,
                                  offset: const Offset(0, 3),
                                ),
                              ]
                            : null,
                      ),
                      child: Theme(
                        data: theme.copyWith(
                          scrollbarTheme: ScrollbarThemeData(
                            thickness: WidgetStatePropertyAll(10.h),
                            thumbVisibility: WidgetStatePropertyAll(
                              Responsive.isDesktop(context),
                            ),
                            mainAxisMargin: 20,
                            thumbColor: WidgetStatePropertyAll(
                              Responsive.isDesktop(context)
                                  ? colorScheme.lightGreyB2B2B2
                                  : Colors.transparent,
                            ),
                          ),
                        ),
                        child: SingleChildScrollView(
                          controller: scrollController,
                          physics: const BouncingScrollPhysics(),
                          padding: EdgeInsets.only(
                            bottom: isKeyboardVisible &&
                                    !Responsive.isDesktop(context)
                                ? keyboardHeight + 20.h // Add some extra space
                                : !Responsive.isDesktop(context)
                                    ? 24.h
                                    : 40.h,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              40.ph,
                              if (titleText != null)
                                Row(
                                  children: [
                                    const CustomBackButton(),
                                    Expanded(
                                      child: Center(
                                        child: TextDisplayLarge36And26(titleText!),
                                      ),
                                    ),
                                    const SizedBox(width: 48),
                                  ],
                                ),
                              !Responsive.isDesktop(context) ? 24.ph : 40.ph,
                              ...children,
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Fixed Button Bar
                  Container(
                    margin: margin,
                    decoration: BoxDecoration(
                      color: colorScheme.white,
                      borderRadius: !Responsive.isDesktop(context)
                          ? null
                          : BorderRadius.only(
                              bottomLeft: Radius.circular(16.r),
                              bottomRight: Radius.circular(16.r),
                            ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (Responsive.isDesktop(context))
                          Divider(
                            thickness: 0.5.h,
                            height: 0.5.h,
                            color: colorScheme.lightGreyD9D9D9,
                          ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 20.h,
                          ),
                          child: PrimaryButton(
                            onPressed: onPressed ?? () {},
                            buttonText: buttonText,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
  }
}
