import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/environment_config.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../data/model/users_list.dart';

class UserInviteeTile extends StatelessWidget {
  final User user;
  final bool checkBoxValue;
  final ValueChanged<bool?>? onChanged;
  final bool enabled;

  const UserInviteeTile({
    super.key,
    required this.user,
    required this.checkBoxValue,
    this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 1),
      child: CommonShadowContainer(
        blurRadius: 3,
        margin:
            EdgeInsets.only(bottom: !Responsive.isDesktop(context) ? 16 : 24),
        child: ListTile(
          shape: RoundedRectangleBorder(
            side: BorderSide(color: colorScheme.lightGreyF2F2F2),
            borderRadius: BorderRadius.circular(16.r),
          ),
          leading: Container(
            width: 44.h,
            height: 44.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: colorScheme.lightGreyD9D9D9,
                width: 1.5,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(22),
              child: user.profilePic != null && user.profilePic!.isNotEmpty
                  ? Image.network(
                      Uri.parse(user.profilePic!).isAbsolute
                          ? user.profilePic!
                          : '${EnvironmentConfig.imageBaseUrl}${user.profilePic!}',
                      fit: BoxFit.cover,
                      width: 44,
                      height: 44,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: colorScheme.lightGreyD9D9D9,
                          child: Center(
                            child: Icon(
                              Icons.person,
                              color: colorScheme.primaryGrey,
                            ),
                          ),
                        );
                      },
                    )
                  : Container(
                      color: colorScheme.lightGreyD9D9D9,
                      child: Center(
                        child: TextTitle14(
                          (user.firstName ?? '').isNotEmpty
                              ? user.firstName![0]
                              : '',
                          color: colorScheme.primaryGrey,
                        ),
                      ),
                    ),
            ),
          ),
          title: TextTitle18And14(
            user.firstName ?? '',
            color: colorScheme.primaryGrey,
            fontWeight: FontWeight.w700,
          ),
          subtitle: TextTitle14(
            user.email ?? '',
            color: colorScheme.darkGrey525252,
            fontSize: !Responsive.isDesktop(context) ? 12.sp : null,
          ),
          trailing: SizedBox(
            width: 24.h,
            height: 24.h,
            child: Checkbox(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              side: BorderSide(
                color: colorScheme.lightGreyD9D9D9,
                width: 1.5,
              ),
              value: checkBoxValue,
              onChanged: enabled ? onChanged : null,
              fillColor: WidgetStateProperty.resolveWith<Color>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return enabled 
                        ? colorScheme.checkBoxLemonGreen
                        : colorScheme.lightGreyD9D9D9;
                  }
                  return colorScheme.white;
                },
              ),
              checkColor: colorScheme.white,
            ),
          ),
        ),
      ),
    );
  }
}
