import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/extensions/list_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/back_button.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../data/model/users_list.dart';
import 'user_invitee_tile.dart';

class PrivateJobInvitees extends StatefulWidget {
  const PrivateJobInvitees({super.key});

  @override
  State<PrivateJobInvitees> createState() => _PrivateJobInviteesState();
}

class _PrivateJobInviteesState extends State<PrivateJobInvitees> {
  late TextEditingController searchController = TextEditingController();
  Timer? _debounce;
  List<User> _selectedUsers = [];

  @override
  void initState() {
    super.initState();
    context.read<PostAJobCubit>().clearUserList();
    _selectedUsers = List.from(context.read<PostAJobCubit>().state.selectedUsers?.users ?? []);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    searchController.dispose();
    super.dispose();
  }

  _onSearchChanged(BuildContext context) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (searchController.text.isNotEmpty) {
        context
            .read<PostAJobCubit>()
            .searchInvitees(searchName: searchController.text);
      } else {
        context.read<PostAJobCubit>().clearUserList();
      }
    });
  }

  void _toggleUserSelection(int userId) {
    if (userId == -1) return;

    final isSelected = _selectedUsers.any((user) => user.id == userId);
    if (isSelected) {
      setState(() {
        _selectedUsers.removeWhere((user) => user.id == userId);
      });
    } else {
      final userToAdd = context.read<PostAJobCubit>().state.usersList?.users?.firstWhereOrNull((user) => user.id == userId);
      if (userToAdd != null) {
        setState(() {
          _selectedUsers.add(userToAdd);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      body: Container(
        color: colorScheme.white,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            if (!Responsive.isDesktop(context)) ...[
              20.ph,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const CustomBackButton(),
                      12.pw,
                      const Text24And20SemiBold(
                        AppStrings.inviteVoices,
                      ),
                    ],
                  ),
                  BlocBuilder<PostAJobCubit, PostAJobState>(
                    builder: (context, state) {
                      return TextButton(
                        onPressed: () {
                          context.read<PostAJobCubit>().setSelectedUsers(_selectedUsers);
                          context.pop();
                        },
                        child: TextTitle18And14(
                          AppStrings.done,
                          color: _selectedUsers.isNotEmpty
                              ? colorScheme.secondary
                              : colorScheme.lightGreyB2B2B2,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
            if (Responsive.isDesktop(context)) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  BlocBuilder<PostAJobCubit, PostAJobState>(
                    builder: (context, state) {
                      return TextButton(
                        onPressed: () {
                          context.read<PostAJobCubit>().setSelectedUsers(_selectedUsers);
                          context.pop();
                        },
                        child: TextTitle18And14(
                          AppStrings.done,
                          color:  _selectedUsers.isNotEmpty
                              ? colorScheme.secondary
                              : colorScheme.lightGreyB2B2B2,
                        ),
                      );
                    },
                  ),
                  const CustomBackButton(
                    icon: Icons.close,
                  )
                ],
              ),
              20.ph,
              const Align(
                alignment: Alignment.centerLeft,
                child: Text24And20SemiBold(
                  AppStrings.inviteVoices,
                ),
              ),
            ],
            !Responsive.isDesktop(context) ? 16.ph : 36.ph,
            AppTextFormField(
              controller: searchController,
              hintText: AppStrings.search,
              prefixIcon: Icon(
                Icons.search,
                color: colorScheme.lightGreyD9D9D9,
              ),
              onChanged: (_) {
                setState(() {});
                _onSearchChanged(context);
              },
              suffixIcon: searchController.text.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        searchController.clear();
                        context.read<PostAJobCubit>().clearUserList();
                        _selectedUsers.clear();
                        setState(() {});
                      },
                      child: Padding(
                        padding: EdgeInsets.all(8.0.h),
                        child: SvgPicture.asset(AppImages.closeSquareIc),
                      ),
                    )
                  : null,
            ),
        
            Flexible(
              child: BlocBuilder<PostAJobCubit, PostAJobState>(
                builder: (context, state) {
                  if (state.isSearchLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state.errorMsg != null) {
                    return Center(child: Text('Error: ${state.errorMsg}'));
                  } else if (state.usersList != null &&
                      state.usersList!.users != null &&
                      state.usersList!.users!.isNotEmpty) {
                    final users = state.usersList!.users!;
                    return ListView.builder(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      itemCount: users.length,
                      itemBuilder: (context, index) {
                        final user = users[index];
                        return UserInviteeTile(
                          user: user,
                          checkBoxValue: _selectedUsers.any((u) => u.id == user.id),
                          onChanged: (value) {
                            if (value != null) {
                              _toggleUserSelection(user.id ?? -1);
                            }
                          },
                        );
                      },
                    );
                  } else {
                    return Center(
                      child: TextTitle14(
                        AppStrings.searchAnArtist,
                        textAlign: TextAlign.center,
                        fontWeight: FontWeight.w700,
                        fontSize:
                            !Responsive.isDesktop(context) ? 14.sp : 18.sp,
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
