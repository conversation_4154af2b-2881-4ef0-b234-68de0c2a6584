import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../bloc/post_a_job_cubit.dart';

class StepperWidget extends StatelessWidget {
  const StepperWidget({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textStyle = theme.textTheme;
    final ScrollController scrollController = ScrollController();

    void scrollToSelectedStep(int selectedIndex, int totalSteps) {
      // Calculate scroll position
      double scrollPosition = (selectedIndex > 3 ? selectedIndex * 40.0 : 0).w; // Adjust width per step
      double maxScroll = scrollController.position.maxScrollExtent;

      if (scrollPosition > maxScroll) {
        scrollPosition = maxScroll; // Prevent scrolling beyond max width
      }

      scrollController.animateTo(
        scrollPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    return BlocBuilder<PostAJobCubit, PostAJobState>(
      builder: (context, state) {
        final cubit = context.read<PostAJobCubit>();        
        // Scroll to selected step
        WidgetsBinding.instance.addPostFrameCallback((_) {
          scrollToSelectedStep(state.selectedIndex, cubit.pages.length);
        });
        return SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (int i = 0; i < cubit.pages.length; i++)
                Row(
                  children: [
                    Container(
                      width: !Responsive.isDesktop(context) ? 28.h : 36.h,
                      height: !Responsive.isDesktop(context) ? 28.h : 36.h,
                      decoration: BoxDecoration(
                        color: i <= state.selectedIndex
                            ? colorScheme.primary
                            : colorScheme.lightGreyB2B2B2,
                        shape: BoxShape.circle,
                        border: i < state.selectedIndex
                            ? Border.all(
                                color: colorScheme.black,
                                width:
                                    !Responsive.isDesktop(context) ? 1.h : 3.h,
                              )
                            : null,
                      ),
                      child: Center(
                        child: i < state.selectedIndex
                            ? !Responsive.isDesktop(context)
                                ? Icon(Icons.check, color: colorScheme.primaryGrey, size: 16.h,)
                                : SvgPicture.asset(AppImages.checkIcon)
                            : Text(
                                (i + 1).toString(),
                                style: textStyle.bodyMedium?.copyWith(
                                  color: colorScheme.black,
                                  fontSize: !Responsive.isDesktop(context)
                                      ? 12.sp
                                      : 14.sp,
                                ),
                              ),
                      ),
                    ),
                    if (i < (cubit.pages.length - 1))
                      Container(
                        width: 56.w,
                        height: 2.h,
                        decoration: BoxDecoration(
                          gradient: i < state.selectedIndex
                              ? LinearGradient(
                                  colors: [
                                    colorScheme.primary,
                                    colorScheme.primary
                                  ],
                                )
                              : i == state.selectedIndex
                                  ? LinearGradient(
                                      colors: [
                                        colorScheme.primary,
                                        colorScheme.lightGreyA9ADB3,
                                      ],
                                    )
                                  : LinearGradient(
                                      colors: [
                                        colorScheme.lightGreyA9ADB3,
                                        colorScheme.lightGreyA9ADB3
                                      ],
                                    ),
                        ),
                      ),
                  ],
                ),
              16.pw,
            ],
          ),
        );
      },
    );
  }
}
