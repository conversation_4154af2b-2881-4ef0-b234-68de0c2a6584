import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_title.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class PostJobPageFrame extends StatelessWidget {
  final List<Widget> children;
  final Key? formKey;
  final String? nextBtnText;
  final String? continueBtnText;
  final void Function()? onContinuePressed;
  final void Function()? onNextPressed;
   final bool needContinueBtn;
   final void Function()? onBackPressed;
  final void Function()? onCancelPressed;
  final bool isEditing;
  final String? title;
  final String? backButtonText;

  const PostJobPageFrame({
    super.key,
    this.children = const <Widget>[],
    this.formKey,
    this.onContinuePressed,
    this.onNextPressed,
    this.onBackPressed,
    this.nextBtnText,
    this.needContinueBtn = false,
    this.continueBtnText,
    this.isEditing = false,
    this.title,
    this.backButtonText, this.onCancelPressed,
  });

  void _showPublicJobConfirmationDialog(BuildContext context) {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.areYouSureYouWantCancelJob,
      message: AppStrings.areYouSureYouWantCancelJobDes,
      onPrimaryButtonTap: () async {
        HiveStorageHelper.deleteKeyInBox(
            boxName: HiveBoxName.user, key: HiveKeys.jobPostData);
        Navigator.pop(context);
        context.pop();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    
    // Get keyboard height
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;
    
    return Responsive.isDesktop(context)
        ? SafeArea(
      child: Form(
        key: formKey,
              child: Stack(
                alignment: Alignment.bottomCenter,
          children: [
                  Container(
                    constraints: const BoxConstraints.expand(),
                    child: Theme(
                      data: theme.copyWith(
                        scrollbarTheme: ScrollbarThemeData(
                            thickness: WidgetStatePropertyAll(10.h),
                            thumbVisibility: WidgetStatePropertyAll(
                                Responsive.isDesktop(context)),
                            mainAxisMargin: 20,
                            thumbColor: WidgetStatePropertyAll(
                                Responsive.isDesktop(context)
                                    ? colorScheme.lightGreyB2B2B2
                                    : Colors.transparent)),
                      ),
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(horizontal: 80.w),
                        controller: scrollController,
                        physics:
                            const BouncingScrollPhysics(), // Smooth scrolling
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            18.ph,
                            PostJobTitle(title: title, isEditing: isEditing),
                            18.ph,
                            ...children,
                            // Add extra padding when keyboard is visible to prevent button overlap
                            isKeyboardVisible && !Responsive.isDesktop(context)
                                ? SizedBox(height: keyboardHeight + 40.h)
                                : SizedBox(
                                    height: !Responsive.isDesktop(context)
                                        ? 24.h
                                        : 40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                        color: colorScheme.white,
                        borderRadius: !Responsive.isDesktop(context)
                            ? null
                            : BorderRadius.only(
                                bottomLeft: Radius.circular(16.r),
                                bottomRight: Radius.circular(16.r))),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (Responsive.isDesktop(context))
                          Divider(
                            thickness: 0.5.h,
                            height: 0.5.h,
                            color: colorScheme.lightGreyD9D9D9,
                          ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 80.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    PrimaryButton(
                                      width: 172.w,
                                      backgroundColor:
                                          colorScheme.lightGreenFDFFDA,
                                      onPressed: 
                                           onCancelPressed ?? () {
                                            _showPublicJobConfirmationDialog(context);
                                          },
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(40.r),
                                        side: BorderSide(
                                          color: colorScheme.primary,
                                          width: 1.2.h,
                                        ),
                                      ),
                                      child: TextTitle18And14(
                                        AppStrings.cancel,
                                      ),
                                    ),
                                    28.pw,
                                    PrimaryButton(
                                      width: 172.w,
                                      onPressed: onNextPressed ?? () {},
                                      buttonText: nextBtnText ?? AppStrings.next,
                                    )
                                  ],
                           ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        : SafeArea(
            child: Form(
              key: formKey,
              child: Column(
                // Main Column
                children: [
                  Expanded(
                    // Scrollable Content
                    child: Container(
                      decoration: BoxDecoration(
                        color: colorScheme.white,
                        borderRadius: BorderRadius.circular(16.w),
                        boxShadow: Responsive.isDesktop(context)
                            ? [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.5),
                                  spreadRadius: 2,
                                  blurRadius: 5,
                                  offset: const Offset(0, 3),
                                ),
                              ]
                            : null,
                      ),
                      child: Theme(
                        data: theme.copyWith(
                          scrollbarTheme: ScrollbarThemeData(
                              thickness: WidgetStatePropertyAll(10.h),
                              thumbVisibility: WidgetStatePropertyAll(
                                  Responsive.isDesktop(context)),
                              mainAxisMargin: 20,
                              thumbColor: WidgetStatePropertyAll(
                                  Responsive.isDesktop(context)
                                      ? colorScheme.lightGreyB2B2B2
                                      : Colors.transparent)),
                        ),
                        child: SingleChildScrollView(
                          controller: scrollController,
                          physics: const BouncingScrollPhysics(),
                          padding: EdgeInsets.only(
                              bottom: isKeyboardVisible &&
                                      !Responsive.isDesktop(context)
                                  ? keyboardHeight +
                                      20.h // Add some extra space
                                  : !Responsive.isDesktop(context)
                                      ? 24.h
                                      : 40.h),
                          child: Padding(
                            padding: Responsive.isDesktop(context)
                                ? EdgeInsets.symmetric(horizontal: 206.w)
                                : EdgeInsets.zero,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                              //  18.ph,
                                PostJobTitle(title: title, isEditing: isEditing),
                                18.ph,
                                ...children,
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Fixed Button Bar
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.white,
                      borderRadius: !Responsive.isDesktop(context)
                          ? null
                          : BorderRadius.only(
                              bottomLeft: Radius.circular(16.r),
                              bottomRight: Radius.circular(16.r)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (Responsive.isDesktop(context))
                          Divider(
                            thickness: 0.5.h,
                            height: 0.5.h,
                            color: colorScheme.lightGreyD9D9D9,
                          ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal:
                                  !Responsive.isDesktop(context) ? 0 : 206.w,
                              vertical: 20.h),
                          child: Row(
                                  children: [
                                    Expanded(
                                      child: PrimaryButton(
                                        backgroundColor:
                                            colorScheme.lightGreenFDFFDA,
                                        onPressed: onBackPressed ??
                                            () {
                                              _showPublicJobConfirmationDialog(context);
                                            },
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(40.r),
                                          side: BorderSide(
                                            color: colorScheme.primary,
                                            width: 1.2.h,
                                          ),
                                        ),
                                        child: TextTitle18And14(
                                          backButtonText ?? AppStrings.cancel,
                                        ),
                                      ),
                                    ),
                                    16.pw,
                                    Expanded(
                                      child: PrimaryButton(
                                        onPressed: onNextPressed ?? () {},
                                        buttonText:
                                            nextBtnText ?? AppStrings.next,
                                      ),
                                    )
                                  ],
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
