import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import '../../../upload_audio/bloc/upload_audio_video_cubit.dart';
import 'pageview_widget.dart';
import 'stepper_widget.dart';

class PostJobWebResponsive extends StatelessWidget {
  const PostJobWebResponsive({super.key});

  @override
  Widget build(BuildContext context) {
  final cubit = context.read<PostAJobCubit>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        40.ph,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 80.w),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomBackButtonArrow(
                onTap: () async {
                  cubit.state.selectedIndex == 0 ? context.pop() : cubit.goToPreviousPage();
                  if (cubit.state.selectedIndex == 3) {
                    final cubit = context.read<UploadAudioVideoCubit>();
                    for (var player in cubit.state.audioPlayers) {
                      if (player.state == PlayerState.playing) {
                        await player.pause();
                      }
                    }
                  }
                },
              ),
              18.ph,
              StepperWidget(),
              5.ph,
            ],
          ),
        ),
        Expanded(child: PageviewWidget()),
      ],
    );
  }
}