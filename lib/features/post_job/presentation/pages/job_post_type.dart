import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/radio_container.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../data/enums/job_post_type.dart';
import '../../models/job_post_model.dart';
import '../widgets/private_job_invitees.dart';
import '../widgets/user_invitee_tile.dart';

class JobPostTypeScreen extends StatefulWidget {
  const JobPostTypeScreen({super.key});

  @override
  State<JobPostTypeScreen> createState() => _JobPostTypeScreenState();
}

class _JobPostTypeScreenState extends State<JobPostTypeScreen> {
  JobPostType? jobPostType;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;
      jobPostType = jobPostModel.jobPostType;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return PostJobPageFrame(
      onNextPressed: () async {
        if (jobPostType == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("job type"));
          return;
        }
        if (jobPostType == JobPostType.public) {
          _showPublicJobConfirmationDialog(colorScheme: colorScheme);
        } else {
          final cubit = context.read<PostAJobCubit>();
          if (cubit.state.selectedUsers == null ||
              cubit.state.selectedUsers!.users == null ||
              cubit.state.selectedUsers!.users!.isEmpty) {
            CustomToast.show(
                context: context, message: ValidationMsg.plsSelectUser);
            return;
          } else {
            JobPostModel? jobPostModel;
            jobPostModel = cubit.loadJobPostFromHive();
            jobPostModel?.jobPostType = jobPostType!;
            jobPostModel?.selectedUsers = cubit.state.selectedUsers;
            jobPostModel?.associatedUsers = cubit.state.selectedUsers?.users
                    ?.map((e) => e.id)
                    .whereType<int>()
                    .toList() ??
                [];
            cubit.updateJobDetails(jobPostModel: jobPostModel!);
            cubit.goToNextPage();
          }
        }
      },
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text24And20SemiBold(
            AppStrings.selectInvitees,
            fontWeight: FontWeight.w700,
            color: colorScheme.primaryGrey,
          ),
        ),
        15.ph,
        !Responsive.isDesktop(context)
            ?
        Row(
          children: [
            Expanded(
              child: RadioContainer(
                groupValue: jobPostType,
                onChanged: onInviteTypeChange,
                value: JobPostType.public,
                title: AppStrings.public,
              ),
            ),
            11.pw,
            Expanded(
              child: RadioContainer(
                groupValue: jobPostType,
                onChanged: onInviteTypeChange,
                value: JobPostType.private,
                title: AppStrings.private,
              ),
            ),
          ],
        ):
         Row(
          children: [
            RadioContainer(
              width: 308.w,
              groupValue: jobPostType,
              onChanged: onInviteTypeChange,
              value: JobPostType.public,
              title: AppStrings.public,
            ),
            14.pw,
            RadioContainer(
              width: 308.w,
              groupValue: jobPostType,
              onChanged: onInviteTypeChange,
              value: JobPostType.private,
              title: AppStrings.private,
            ),
          ],
        ),
        if (jobPostType == JobPostType.private) ...[
          !Responsive.isDesktop(context) ? 26.ph : 52.ph,

          BlocBuilder<PostAJobCubit, PostAJobState>(
            builder: (context, state) {
              final selectedUsers = state.selectedUsers?.users ?? [];

              if (selectedUsers.isEmpty) {
                return const SizedBox.shrink();
              }
              return SizedBox(
                width: !Responsive.isDesktop(context) ? null : 628.w,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: selectedUsers.length,
                  itemBuilder: (context, index) {
                    final user = selectedUsers[index];
                    return UserInviteeTile(
                      user: user,
                      checkBoxValue: state.selectedUsers?.users
                              ?.any((u) => u.id == user.id) ??
                          false,
                      onChanged: (value) {
                        if (value != null) {
                          context
                              .read<PostAJobCubit>()
                              .toggleUserSelection(user.id ?? -1);
                        }
                      },
                    );
                  },
                ),
              );
            },
          ),
          InkWell(
            onTap: () {
              !Responsive.isDesktop(context)
                  ? Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => BlocProvider.value(
                          value: BlocProvider.of<PostAJobCubit>(context),
                          child: const PrivateJobInvitees(),
                        ),
                      ),
                    )
                  : showDialog(
                      context: context,
                      builder: (_) {
                        return BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: Dialog(
                            backgroundColor: Colors.transparent,
                            insetPadding: EdgeInsets.symmetric(
                              horizontal: 286.w,
                              vertical: 86.h,
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: BlocProvider.value(
                                value: BlocProvider.of<PostAJobCubit>(context),
                                child: const PrivateJobInvitees(),
                              ),
                            ),
                          ),
                        );
                      },
                    );
            },
            child: Container(
              width: !Responsive.isDesktop(context) ? double.infinity : 628.w,
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.lightGreyD9D9D9,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: EdgeInsets.all(16.h),
                child: Row(
                  children: [
                    Container(
                      height: 36.h,
                      width: 36.h,
                      decoration: BoxDecoration(
                        color: colorScheme.primary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        Icons.add,
                        color: colorScheme.darkGrey525252,
                      ),
                    ),
                    14.pw,
                    const TextTitle18And20(
                      AppStrings.inviteVoices,
                     // fontWeight: FontWeight.w500,
                    )
                  ],
                ),
              ),
            ),
          ),
          96.ph
        ]
      ],
    );
  }

  void _showPublicJobConfirmationDialog({required ColorScheme colorScheme}) {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.areYouSureYouWantToPostPublicly,
      message: AppStrings.theJobWillBeVisibleToEveryVoice,
      primaryButtonText: AppStrings.yes,
      primaryButtonColor: colorScheme.primaryGrey,
      cancelButtonText: AppStrings.cancel,
      onPrimaryButtonTap: () async {
        JobPostModel? jobPostModel;
        final cubit = context.read<PostAJobCubit>();
        jobPostModel = cubit.loadJobPostFromHive();
        jobPostModel?.jobPostType = jobPostType!;
        // In case user selects public after selecting private
        jobPostModel?.selectedUsers = null;
        jobPostModel?.associatedUsers = null;
        cubit.updateJobDetails(
          jobPostModel: jobPostModel!,
        );
        context.pop();
        cubit.goToNextPage();
      },
    );
  }

  void onInviteTypeChange(JobPostType? jobPostType) {
    if (jobPostType != null) {
      setState(() {
        this.jobPostType = jobPostType;
      });
    }
  }
}
