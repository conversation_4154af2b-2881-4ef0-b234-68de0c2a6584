import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../utils/textformatter/non_leading_zero_formatter.dart';
import '../../../../utils/validations.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../models/job_post_model.dart';
import '../widgets/post_job_page_frame.dart';

class ProjectDeadline extends StatefulWidget {
  const ProjectDeadline({super.key});

  @override
  State<ProjectDeadline> createState() => _ProjectDeadlineState();
}

class _ProjectDeadlineState extends State<ProjectDeadline> {
  final formGlobalKey = GlobalKey<FormState>();
  late TextEditingController durationHourController;
  late TextEditingController durationMinController;
  late TextEditingController durationSecController;
  late TextEditingController projectDeadlineController;
  late TextEditingController responseDeadlineController;
  bool autovalidation = false;
  DateTime? projectDeadlineDate;
  DateTime? responseDeadlineDate;

  String? _validateTimeField({
    required String? value,
    required String fieldName,
    required String otherFieldValue1,
    required String otherFieldValue2,
    required int maxValue,
  }) {
    // If all three fields are empty, show error
    if ((value == null || value.isEmpty) && (otherFieldValue1.isEmpty) && (otherFieldValue2.isEmpty)) {
      return "Please enter $fieldName";
    }

    if (value == null || value.isEmpty) {
      return null;
    }

    final number = int.tryParse(value);
    if (number == null) {
      return ValidationMsg.plsEntervalid(fieldName);
    }

    if (number == 0) {
      final other1 = int.tryParse(otherFieldValue1) ?? 0;
      final other2 = int.tryParse(otherFieldValue2) ?? 0;
      if (other1 == 0 && other2 == 0) {
        return "Duration cannot be 0";
      }
    }
    // if (number > maxValue) {
    //   return "${fieldName[0].toUpperCase()}${fieldName.substring(1)} must be less than ${maxValue + 1}";
    // }
    return null;
  }

  // Helper method to create seconds formatter
  TextInputFormatter _createSecondsFormatter() {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final newText = newValue.text;
      if (newText.isEmpty) return newValue;

      final number = int.tryParse(newText);
      if (number == null) return oldValue;

      if (newText.length == 1) return newValue;

      if (number > 59) return oldValue;
      return newValue;
    });
  }

  // Helper method to create minutes formatter
  TextInputFormatter _createMinutesFormatter() {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final newText = newValue.text;
      if (newText.isEmpty) return newValue;

      final number = int.tryParse(newText);
      if (number == null) return oldValue;

      if (newText.length == 1) return newValue;

      if (number > 59) return oldValue;
      return newValue;
    });
  }

  void _triggerValidationIfNeeded() {
    if (autovalidation) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    durationHourController = TextEditingController();
    durationMinController = TextEditingController();
    durationSecController = TextEditingController();
    projectDeadlineController = TextEditingController();
    responseDeadlineController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      durationHourController.text = jobPostModel.dubbingDurationHours ?? '';
      durationMinController.text = jobPostModel.dubbingDuration ?? '';
      durationSecController.text = jobPostModel.dubbingDurationSeconds ?? '';

      final projectDate = jobPostModel.projectDeadline;
      if (projectDate != null) {
        projectDeadlineDate = projectDate;
        projectDeadlineController.text =
            DateFormat('d MMMM yyyy').format(projectDate);
      }

      final responseDate = jobPostModel.responseDeadline;
      if (responseDate != null) {
        responseDeadlineDate = responseDate;
        responseDeadlineController.text =
            DateFormat('d MMMM yyyy').format(responseDate);
      }

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return PostJobPageFrame(
        formKey: formGlobalKey,
        onNextPressed: () async {
          FocusManager.instance.primaryFocus?.unfocus();
          if (formGlobalKey.currentState!.validate()) {
            final cubit = context.read<PostAJobCubit>();
            JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
            jobPostModel?.dubbingDurationHours = durationHourController.text.trim();
            jobPostModel?.dubbingDuration = durationMinController.text.trim();
            jobPostModel?.dubbingDurationSeconds =
                durationSecController.text.trim();
            jobPostModel?.projectDeadline = projectDeadlineDate;
            jobPostModel?.responseDeadline = responseDeadlineDate;
            cubit.updateJobDetails(jobPostModel: jobPostModel!);
            cubit.goToNextPage();
          } else {
            autovalidation = true;
            setState(() {});
          }
        },
        children: [
          !Responsive.isDesktop(context)
              ? Column(children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text24And20SemiBold(AppStrings.duration)),
                  16.ph,
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: AppTextFormField(
                            maxLength: 10,
                            controller: durationHourController,
                            hintText: AppStrings.enterHours,
                            titleText: AppStrings.hours,
                            keyboardType: TextInputType.number,
                            validator: (value) => _validateTimeField(
                              value: value,
                              fieldName: "hours",
                              otherFieldValue1: durationMinController.text,
                              otherFieldValue2: durationSecController.text,
                              maxValue: 23,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              NoLeadingZeroFormatter(),
                            ],
                            isAutovalidateModeOn: autovalidation,
                            onChanged: (_) => _triggerValidationIfNeeded(),
                          ),
                      ),
                      10.pw,
                      Expanded(
                        child: AppTextFormField(
                          maxLength: 2,
                          controller: durationMinController,
                          hintText: AppStrings.enterMinDuration,
                          titleText: AppStrings.minutes,
                          keyboardType: TextInputType.number,
                          validator: (value) => _validateTimeField(
                            value: value,
                            fieldName: "minutes",
                            otherFieldValue1: durationHourController.text,
                            otherFieldValue2: durationSecController.text,
                            maxValue: 59,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            NoLeadingZeroFormatter(),
                            _createMinutesFormatter(),
                          ],
                          isAutovalidateModeOn: autovalidation,
                          onChanged: (_) => _triggerValidationIfNeeded(),
                        ),
                      ),
                      10.pw,
                      Expanded(
                        child: AppTextFormField(
                         maxLength: 2,
                         controller: durationSecController,
                         hintText: AppStrings.enterSecDuration,
                         titleText: AppStrings.seconds,
                         keyboardType: TextInputType.number,
                         validator: (value) => _validateTimeField(
                           value: value,
                           fieldName: "seconds",
                           otherFieldValue1: durationMinController.text,
                           otherFieldValue2: durationHourController.text,
                           maxValue: 59,
                         ),
                         inputFormatters: [
                           FilteringTextInputFormatter.digitsOnly,
                           NoLeadingZeroFormatter(),
                           LengthLimitingTextInputFormatter(2),
                           _createSecondsFormatter(),
                         ],
                         isAutovalidateModeOn: autovalidation,
                         onChanged: (_) => _triggerValidationIfNeeded(),
                                                      ),
                      ),
                    ],
                  ),
                  !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                  AppTextFormField(
                    isParentField: true,
                    prefixIconConstraints: BoxConstraints(
                      minHeight: 20.h,
                      minWidth: 48.h,
                    ),
                    prefixIcon: SvgPicture.asset(AppImages.calendarIc),
                    readOnly: true,
                    controller: projectDeadlineController,
                    hintText: AppStrings.projectDeadlineEnter,
                    titleText: AppStrings.projectDeadline,
                    validator: (value) {
                      return Validator.emptyValidator(value,
                          ValidationMsg.plsEntervalid("project deadline"));
                    },
                    isAutovalidateModeOn: autovalidation,
                    onTap: () async {
                      DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialEntryMode: DatePickerEntryMode.calendarOnly,
                        initialDate: projectDeadlineDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2100),
                      );
                      if (pickedDate != null) {
                        if (responseDeadlineDate != null &&
                            pickedDate.isBefore(responseDeadlineDate!)) {
                          if (mounted) {
                            CustomToast.show(
                                context: context,
                                message:
                                    ValidationMsg.projectDeadlineAfterResponse);
                          }
                          return;
                        }
                        projectDeadlineDate = pickedDate;
                        projectDeadlineController.text =
                            DateFormat('d MMMM yyyy').format(pickedDate);
                      }
                    },
                  ),
                  !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                  AppTextFormField(
                    isParentField: true,
                    prefixIconConstraints: BoxConstraints(
                      minHeight: 20.h,
                      minWidth: 48.h,
                    ),
                    prefixIcon: SvgPicture.asset(AppImages.calendarIc),
                    readOnly: true,
                    controller: responseDeadlineController,
                    hintText: AppStrings.responseDeadlineEnter,
                    titleText: AppStrings.responseDeadlineAsterisk,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      return Validator.emptyValidator(value,
                          ValidationMsg.plsEntervalid("response deadline"));
                    },
                    onTap: () async {
                      DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialEntryMode: DatePickerEntryMode.calendarOnly,
                        initialDate: responseDeadlineDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2100),
                      );
                      if (pickedDate != null) {
                        if (projectDeadlineDate != null &&
                            pickedDate.isAfter(projectDeadlineDate!)) {
                          if (mounted) {
                            CustomToast.show(
                                context: context,
                                message: ValidationMsg
                                    .responseBeforeProjectDeadline);
                          }
                          return;
                        }
                        responseDeadlineDate = pickedDate;
                        responseDeadlineController.text =
                            DateFormat('d MMMM yyyy').format(pickedDate);
                      }
                    },
                    isAutovalidateModeOn: autovalidation,
                  ),
                  99.ph,
                ])
              : Column(
                  children: [
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text24And20SemiBold(AppStrings.duration)),
                    16.ph,
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: AppTextFormField(
                            maxLength: 10,
                            controller: durationHourController,
                            hintText: AppStrings.enterHours,
                            titleText: AppStrings.hours,
                            keyboardType: TextInputType.number,
                            validator: (value) => _validateTimeField(
                              value: value,
                              fieldName: "hours",
                              otherFieldValue1: durationMinController.text,
                              otherFieldValue2: durationSecController.text,
                              maxValue: 23,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              NoLeadingZeroFormatter(),
                            ],
                            isAutovalidateModeOn: autovalidation,
                            onChanged: (_) => _triggerValidationIfNeeded(),
                          ),
                        ),
                        10.pw,
                        Expanded(
                          child: AppTextFormField(
                            maxLength: 2,
                            controller: durationMinController,
                            hintText: AppStrings.enterMinDuration,
                            titleText: AppStrings.minutes,
                            keyboardType: TextInputType.number,
                            validator: (value) => _validateTimeField(
                              value: value,
                              fieldName: "minutes",
                              otherFieldValue1: durationHourController.text,
                              otherFieldValue2: durationSecController.text,
                              maxValue: 59,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              NoLeadingZeroFormatter(),
                              _createMinutesFormatter(),
                            ],
                            isAutovalidateModeOn: autovalidation,
                            onChanged: (_) => _triggerValidationIfNeeded(),
                          ),
                        ),
                        10.pw,
                        Expanded(
                          child: AppTextFormField(
                            maxLength: 2,
                            controller: durationSecController,
                            hintText: AppStrings.enterSecDuration,
                            titleText: AppStrings.seconds,
                            keyboardType: TextInputType.number,
                            validator: (value) => _validateTimeField(
                              value: value,
                              fieldName: "seconds",
                              otherFieldValue1: durationMinController.text,
                              otherFieldValue2: durationHourController.text,
                              maxValue: 59,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              NoLeadingZeroFormatter(),
                              LengthLimitingTextInputFormatter(2),
                              _createSecondsFormatter(),
                            ],
                            isAutovalidateModeOn: autovalidation,
                            onChanged: (_) => _triggerValidationIfNeeded(),
                          ),
                        ),
                      ],
                    ),
                    24.ph,
                    Row(children: [
                      Expanded(
                        child: AppTextFormField(
                          isParentField: true,
                          prefixIconConstraints: BoxConstraints(
                            minHeight: 20.h,
                            minWidth: 48.h,
                          ),
                          prefixIcon: SvgPicture.asset(AppImages.calendarIc),
                          readOnly: true,
                          controller: projectDeadlineController,
                          hintText: AppStrings.projectDeadlineEnter,
                          titleText: AppStrings.projectDeadline,
                          validator: (value) {
                            return Validator.emptyValidator(
                                value,
                                ValidationMsg.plsEntervalid(
                                    "project deadline"));
                          },
                          isAutovalidateModeOn: autovalidation,
                          onTap: () async {
                            DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialEntryMode:
                                  DatePickerEntryMode.calendarOnly,
                              initialDate:
                                  projectDeadlineDate ?? DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate: DateTime(2100),
                            );
                            if (pickedDate != null) {
                              if (responseDeadlineDate != null &&
                                  pickedDate.isBefore(responseDeadlineDate!)) {
                                if (mounted) {
                                  CustomToast.show(
                                      context: context,
                                      message: ValidationMsg
                                          .projectDeadlineAfterResponse);
                                }
                                return;
                              }
                              projectDeadlineDate = pickedDate;
                              projectDeadlineController.text =
                                  DateFormat('d MMMM yyyy').format(pickedDate);
                            }
                          },
                        ),
                      ),
                      10.pw,
                      Expanded(
                          child: AppTextFormField(
                        isParentField: true,
                        prefixIconConstraints: BoxConstraints(
                          minHeight: 20.h,
                          minWidth: 48.h,
                        ),
                        prefixIcon: SvgPicture.asset(AppImages.calendarIc),
                        readOnly: true,
                        controller: responseDeadlineController,
                        hintText: AppStrings.responseDeadlineEnter,
                        titleText: AppStrings.responseDeadlineAsterisk,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          return Validator.emptyValidator(value,
                              ValidationMsg.plsEntervalid("response deadline"));
                        },
                        onTap: () async {
                          DateTime? pickedDate = await showDatePicker(
                            context: context,
                            initialEntryMode: DatePickerEntryMode.calendarOnly,
                            initialDate: responseDeadlineDate ?? DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate: DateTime(2100),
                          );
                          if (pickedDate != null) {
                            if (projectDeadlineDate != null &&
                                pickedDate.isAfter(projectDeadlineDate!)) {
                              if (mounted) {
                                CustomToast.show(
                                    context: context,
                                    message: ValidationMsg
                                        .responseBeforeProjectDeadline);
                              }
                              return;
                            }
                            responseDeadlineDate = pickedDate;
                            responseDeadlineController.text =
                                DateFormat('d MMMM yyyy').format(pickedDate);
                          }
                        },
                        isAutovalidateModeOn: autovalidation,
                      ))
                    ]),
                  ],
                )
        ]);
  }
}
