import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/enums/budget_type_enum.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/textformatter/non_leading_zero_formatter.dart';
import 'package:the_voice_directory_flutter/widgets/radio_container.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../../utils/custom_toast.dart';
import '../../../../utils/validations.dart';
import '../../models/job_post_model.dart';

class BudgetTypeScreen extends StatefulWidget {
  const BudgetTypeScreen({super.key});

  @override
  State<BudgetTypeScreen> createState() => _BudgetTypeScreenState();
}

class _BudgetTypeScreenState extends State<BudgetTypeScreen> {
  BudgetType? budgetType;
  bool autovalidation = false;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController fixedBudgetController;
  late TextEditingController minRangeController;
  late TextEditingController maxRangeController;

  @override
  void initState() {
    super.initState();
    fixedBudgetController = TextEditingController();
    minRangeController = TextEditingController();
    maxRangeController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      if (jobPostModel.budgetType == BudgetType.fixed) {
        budgetType = BudgetType.fixed;
        fixedBudgetController.text = jobPostModel.fixedBudget ?? '';
      } else if (jobPostModel.budgetType == BudgetType.range) {
        budgetType = BudgetType.range;
        minRangeController.text = jobPostModel.minBudgetRange ?? '';
        maxRangeController.text = jobPostModel.maxBudgetRange ?? '';
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return PostJobPageFrame(
      formKey: _formKey,
      onNextPressed: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        if (budgetType == null) {
          CustomToast.show(
              context: context,
              message: ValidationMsg.plsSelect("budget type"));
          return;
        }
        if (_formKey.currentState!.validate()) {
          final cubit = context.read<PostAJobCubit>();
          JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
          jobPostModel?.budgetType = budgetType;
          if (budgetType == BudgetType.fixed) {
            jobPostModel?.fixedBudget = fixedBudgetController.text.trim();
            jobPostModel?.minBudgetRange = null;
            jobPostModel?.maxBudgetRange = null;
          } else if (budgetType == BudgetType.range) {
            jobPostModel?.fixedBudget = null;
            jobPostModel?.minBudgetRange = minRangeController.text.trim();
            jobPostModel?.maxBudgetRange = maxRangeController.text.trim();
          }
          cubit.updateJobDetails(jobPostModel: jobPostModel!);
          cubit.goToNextPage();
        } else {
          autovalidation = true;
          setState(() {});
        }
      },
      children: [
        const Align(
          alignment: Alignment.centerLeft,
          child: Text24And20SemiBold(
            AppStrings.budgetType,
          ),
        ),
        16.ph,
        !Responsive.isDesktop(context) ?
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: RadioContainer(
                  groupValue: budgetType,
                  onChanged: onBudgetTypeChange,
                  value: BudgetType.fixed,
                  title: AppStrings.fixed),
            ),
            !Responsive.isDesktop(context) ? 11.pw : 12.pw,
            Expanded(
              child: RadioContainer(
                  groupValue: budgetType,
                  onChanged: onBudgetTypeChange,
                  value: BudgetType.range,
                  title: AppStrings.range),
            ),
          ],
        ):
         Row(
          children: [
            RadioContainer(
                width: 308.w,
                groupValue: budgetType,
                onChanged: onBudgetTypeChange,
                value: BudgetType.fixed,
                title: AppStrings.fixed),
            !Responsive.isDesktop(context) ? 11.pw : 12.pw,
            RadioContainer(
                width: 308.w,
                groupValue: budgetType,
                onChanged: onBudgetTypeChange,
                value: BudgetType.range,
                title: AppStrings.range),
          ],
        ),
        24.ph,
        if (budgetType == BudgetType.fixed) ...[
          Align(
              alignment: Alignment.centerLeft,
            child: RichText(
              text: TextSpan(
                text: AppStrings.budgetPrice,
                style: (!Responsive.isDesktop(context)
                    ? textTheme.bodyMedium
                    : textTheme.displaySmall),
                children: [
                  TextSpan(
                    text: AppStrings.priceInINR,
                    style: (!Responsive.isDesktop(context)
                        ? textTheme.titleSmall!.copyWith(
                            fontFamily: 'NotoSans-SemiBold',
                            fontWeight: FontWeight.w600,
                          )
                        : textTheme.titleLarge!.copyWith(
                            fontFamily: 'NotoSans-SemiBold',
                            fontWeight: FontWeight.w600,
                          )),
                  )
                ],
              ),
            ),
          ),
          16.ph,
          AppTextFormField(
            width: !Responsive.isDesktop(context) ? double.infinity : 308.w,
            controller: fixedBudgetController,
            maxLength: 10,
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                1.ph,
                const TextTitle18And14("₹"),
                Container(
                  width: 1.h,
                  height: 20.h,
                  color: colorScheme.lightGreyD9D9D9,
                )
              ],
            ),
            validator: (value) {
              return Validator.emptyValidator(
                  value, ValidationMsg.plsEntervalid("amount"),
                  isNumber: true);
            },
            isAutovalidateModeOn: autovalidation,
            keyboardType: TextInputType.number,
            hintText: AppStrings.enterAmount,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              NoLeadingZeroFormatter(),
            ],
          ),
        ] else if (budgetType == BudgetType.range) ...[
          Align(
              alignment: Alignment.centerLeft,
            child: RichText(
              text: TextSpan(
                text: AppStrings.rangePrice,
                style: (!Responsive.isDesktop(context)
                    ? textTheme.bodyMedium
                    : textTheme.displaySmall),
                children: [
                  TextSpan(
                    text: AppStrings.priceInINR,
                    style: (!Responsive.isDesktop(context)
                        ? textTheme.titleSmall!.copyWith(
                            fontFamily: 'NotoSans-SemiBold',
                            fontWeight: FontWeight.w600,
                          )
                        : textTheme.titleLarge!.copyWith(
                            fontFamily: 'NotoSans-SemiBold',
                            fontWeight: FontWeight.w600,
                          )),
                  )
                ],
              ),
            ),
          ),
          16.ph,
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: AppTextFormField(
                  width: !Responsive.isDesktop(context) ? double.infinity : 308.w,
                  controller: minRangeController,
                  maxLength: 10,
                  prefixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      1.ph,
                      const TextTitle18And14("₹"),
                      Container(
                        width: 1.h,
                        height: 20.h,
                        color: colorScheme.lightGreyD9D9D9,
                      )
                    ],
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please enter a minimum price.";
                    }
                    final min = int.tryParse(value);
                    final max = int.tryParse(maxRangeController.text);

                    if (min == null || min <= 0) {
                      return "Minimum price must be greater than ₹0.";
                    }
                    if (max != null && min > max) {
                      return "Minimum price cannot be greater than maximum price.";
                    }
                    return null;
                  },
                  isAutovalidateModeOn: autovalidation,
                  keyboardType: TextInputType.number,
                  hintText: AppStrings.min,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    NoLeadingZeroFormatter(),
                  ],
                ),
              ),
              !Responsive.isDesktop(context) ? 11.pw : 12.pw,
              Flexible(
                child: AppTextFormField(
                  width: !Responsive.isDesktop(context) ? double.infinity : 308.w,
                  controller: maxRangeController,
                  maxLength: 10,
                  prefixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      1.ph,
                      const TextTitle18And14("₹"),
                      Container(
                        width: 1.h,
                        height: 20.h,
                        color: colorScheme.lightGreyD9D9D9,
                      )
                    ],
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please enter a maximum price.";
                    }
                    final max = int.tryParse(value);
                    final min = int.tryParse(minRangeController.text);

                    if (max == null || max <= 0) {
                      return "Maximum price must be greater than ₹0.";
                    }
                    if (min != null && max <= min) {
                      return "Maximum price must be greater than minimum price.";
                    }
                    if (max > 10000000) {
                      return "Maximum price cannot exceed ₹10,00,000.";
                    }
                    return null;
                  },
                  isAutovalidateModeOn: autovalidation,
                  keyboardType: TextInputType.number,
                  hintText: AppStrings.max,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    NoLeadingZeroFormatter(),
                  ],
                ),
              ),
            ],
          ),
        ],
        // if (budgetType != null) ...[
        //   12.ph,
        //   Align(
        //     alignment: Alignment.centerLeft,
        //     child: TextBodySmall12(
        //       AppStrings.gstText,
        //       color: colorScheme.hyperlinkBlueColor,
        //       fontSize: 14.sp,
        //     ),
        //   )
        // ]
      ],
    );
  }

  void onBudgetTypeChange(BudgetType? budgetType) {
    this.budgetType = budgetType!;
    setState(() {});
  }
}
