import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:textfield_tags/textfield_tags.dart' as tag_textfield;

import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/list_extn.dart';
import '../../models/job_post_model.dart';

class BasicJobDetails extends StatefulWidget {
  const BasicJobDetails({super.key});

  @override
  State<BasicJobDetails> createState() => _BasicJobDetailsState();
}

class _BasicJobDetailsState extends State<BasicJobDetails> {
  late tag_textfield.TextfieldTagsController tagController;
  late TextEditingController titleController;
  late TextEditingController requirementController;
  DropdownData? selectedCategory;
  List<String> initialTags = [];
  bool autovalidation = false;
  final formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    titleController = TextEditingController();
    requirementController = TextEditingController();
    tagController = tag_textfield.TextfieldTagsController();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      titleController.text = jobPostModel.title ?? '';
      requirementController.text = jobPostModel.requirement ?? '';
      final tags = jobPostModel.jobTags;
      if (tags != null && tags.isNotEmpty) {
        for (var element in tags) {
          tagController.addTag = element;
        }
      }

      final state = context.read<StaticDataDropdownBloc>().state;
      if (state is StaticDataDropdownSuccessState) {
        final projectType = state.dropDownResponseModel?.projectType;
        final jobCategoryId = jobPostModel.jobCategory;
        if (jobCategoryId != null) {
          selectedCategory = projectType?.firstWhereOrNull((e) => e == jobCategoryId);
        }
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return PostJobPageFrame(
      formKey: formGlobalKey,
      onNextPressed: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        if (selectedCategory == null) {
          CustomToast.show(context: context, message: ValidationMsg.plsSelect("category"));
          return;
        }
        if (formGlobalKey.currentState!.validate()) {
          final cubit = context.read<PostAJobCubit>();
          JobPostModel? jobPostModel;
          jobPostModel = cubit.loadJobPostFromHive();
          if (jobPostModel != null) {
            jobPostModel.title = titleController.text.trim();
            jobPostModel.requirement = requirementController.text.trim();
            jobPostModel.jobCategory = selectedCategory;
            jobPostModel.jobTags = tagController.getTags;
          } else {
            jobPostModel = JobPostModel(
              title: titleController.text.trim(),
              requirement: requirementController.text.trim(),
              jobCategory: selectedCategory,
              jobTags: tagController.getTags,
            );
          }
          cubit.updateJobDetails(jobPostModel: jobPostModel);
          cubit.goToNextPage();
        } else {
          autovalidation = true;
          setState(() {});
        }
      },
      children: [
        !Responsive.isDesktop(context) ?
        AppTextFormField(
          isParentField: true,
          maxLength: 250,
          controller: titleController,
          hintText: AppStrings.enterProjectTitle,
          titleText: AppStrings.title,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
          ],
          validator: (String? value) {
            return Validator.emptyValidator(
                value, ValidationMsg.plsEntervalid("title"), minCharLimit: 2);
          },
          isAutovalidateModeOn: autovalidation,
        ) : 
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              flex: 1,
              child: AppTextFormField(
                isParentField: true,
                maxLength: 250,
                controller: titleController,
                hintText: AppStrings.enterProjectTitle,
                titleText: AppStrings.title,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
                ],
                validator: (String? value) {
                  return Validator.emptyValidator(
                      value, ValidationMsg.plsEntervalid("title"), minCharLimit: 2);
                },
                isAutovalidateModeOn: autovalidation,
              ),
            ),
            20.pw,
            Expanded(
              flex: 1,
              child: BlocConsumer<StaticDataDropdownBloc, StaticDataDropdownState>(
                listener: (context, state) {
                  if (state is StaticDataDropdownSuccessState) {
                    final projectType = state.dropDownResponseModel?.projectType;
                    final jobCategoryId = context.read<PostAJobCubit>().state.jobPostModel.jobCategory;
                    if (jobCategoryId != null) {
                      setState(() {
                        selectedCategory = projectType?.firstWhereOrNull((e) => e == jobCategoryId);
                      });
                    }
                  }
                },
                builder: (context, state) {
                  List<DropdownData>? projectType = [];
                  if (state is StaticDataDropdownSuccessState) {
                    projectType.addAll(state.dropDownResponseModel?.projectType ?? []);
                  }
                  return CustomDropDownWidget<DropdownData>(
                      isParentField: true,
                      isLoading: state is StaticDataDropdownLoadingState,
                      isError: state is StaticDataDropdownErrorState,
                      hintText: AppStrings.selectCategory,
        
                      titleText: AppStrings.category,
                      selectedValue: selectedCategory?.name,
                      items: projectType
                          .map((item) => DropdownMenuItem(
                              value: item,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  TextTitle18And14(
                                    item.name ?? "",
                                  ),
                                  if (item.id == selectedCategory?.id)
                                    const Icon(Icons.check),
                                ],
                              )))
                          .toList(),
                      onChanged: (value) {
                        selectedCategory = value;
                        setState(() {});
                      },
                      value: selectedCategory);
                },
              ),
            ),
          ],
        ),
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        AppTextFormField(
          isParentField: true,
          controller: requirementController,
          titleText: AppStrings.requirementAsterisk,
          hintText: AppStrings.addRequirement,
          maxLines: 3,
          keyboardType: TextInputType.multiline,
          textInputAction: TextInputAction.newline,
          validator: (String? value) {
            return Validator.emptyValidator(
                value, ValidationMsg.plsEntervalid("requirement"), minCharLimit: 5);
          },
          isAutovalidateModeOn: autovalidation,
        ),
        if(!Responsive.isDesktop(context))...[
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        BlocConsumer<StaticDataDropdownBloc, StaticDataDropdownState>(
          listener: (context, state) {
            if (state is StaticDataDropdownSuccessState) {
              final projectType = state.dropDownResponseModel?.projectType;
              final jobCategoryId = context.read<PostAJobCubit>().state.jobPostModel.jobCategory;
              if (jobCategoryId != null) {
                setState(() {
                  selectedCategory = projectType?.firstWhereOrNull((e) => e == jobCategoryId);
                });
              }
            }
          },
          builder: (context, state) {
            List<DropdownData>? projectType = [];
            if (state is StaticDataDropdownSuccessState) {
              projectType.addAll(state.dropDownResponseModel?.projectType ?? []);
            }
            return CustomDropDownWidget<DropdownData>(
                isParentField: true,
                isLoading: state is StaticDataDropdownLoadingState,
                isError: state is StaticDataDropdownErrorState,
                hintText: AppStrings.selectCategory,
                titleText: AppStrings.category,
                selectedValue: selectedCategory?.name,
                items: projectType
                    .map((item) => DropdownMenuItem(
                        value: item,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextTitle18And14(
                              item.name ?? "",
                            ),
                            if (item.id == selectedCategory?.id)
                              const Icon(Icons.check),
                          ],
                        )))
                    .toList(),
                onChanged: (value) {
                  selectedCategory = value;
                  setState(() {});
                },
                value: selectedCategory);
          },
        ),
        ],
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        tag_textfield.TextFieldTags(
          textfieldTagsController: tagController,
          textSeparators: const [',', ' '],
          initialTags: initialTags,
          letterCase: tag_textfield.LetterCase.small,
          validator: (String tag) {
            if (tagController.getTags != null &&
                tagController.getTags!.isNotEmpty) {
              if (tagController.getTags!.contains(tag)) {
                return 'You already entered that';
              }
              if (tagController.getTags!.length > 9) {
                return 'Max 10 tags are allowed';
              }
            }
            return null;
          },
          inputfieldBuilder: (context, tec, fn, error, onChanged, onSubmitted) {
            return ((context, sc, tags, onTagDelete) {
              return Column(
                children: [
                  AppTextFormField(
                    isParentField: true,
                    controller: tec,
                    focusNode: fn,
                    isenabled: tags.length < 10,
                    hintText: AppStrings.addKeywordsRequirement,
                    titleText: AppStrings.addKeywords,
                    indicatorText: AppStrings.upTo10,
                    maxLength: 50,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z0-9 ,_]"))
                    ],
                    onChanged: (value) {
                      onChanged!(value);
                    },
                    onSubmitted: (value) {
                      onSubmitted!(value);
                      fn.requestFocus();
                    },
                    trailingTitleTextWidget: Tooltip(
                      message: AppStrings.typeAndPressEnterToAddAKeyword,
                      textStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.white,
                        fontSize: 14.sp,
                      ),
                      child: Icon(
                        Icons.info_outline,
                        size: Responsive.isDesktop(context) ? 20.h : 16.h,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  if (tags.isNotEmpty) 8.ph,
                  Align(
                    alignment: Alignment.centerLeft,
                    child: ChipsWidget(
                      items: tags,
                      onRemove: (item) {
                        onTagDelete(item);
                      },
                    ),
                  ),
                  96.ph,
                ],
              );
            });
          },
        ),
      ],
    );
  }
}
