import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';

import '../../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../../../utils/custom_toast.dart';
import '../../../../utils/extensions/list_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../utils/validations.dart';
import '../../../../widgets/custom_dropdown.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';
import '../../../prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import '../../../prefered_project_type/bloc/static_data_dropdown_state.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../models/job_post_model.dart';

class JobVocalCharacteristics extends StatefulWidget {
  const JobVocalCharacteristics({super.key});

  @override
  State<JobVocalCharacteristics> createState() =>
      _JobVocalCharacteristicsState();
}

class _JobVocalCharacteristicsState extends State<JobVocalCharacteristics> {
  DropdownData? selectedLanguage;
  DropdownData? selectedAccent;
  DropdownData? selectedVoiceGender;
  AgeRange? selectedVoiceAge;
  DropdownData? selectedVoiceChar;
  DropdownData? selectedExpelience;
  bool autovalidation = false;
  late TextEditingController otherCharController;

  @override
  void initState() {
    super.initState();
    otherCharController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      otherCharController.text = jobPostModel.otherVocalCharacter ?? '';
      final state = context.read<StaticDataDropdownBloc>().state;

      if (state is StaticDataDropdownSuccessState) {
        final voiceCharacter = state.dropDownResponseModel!.voiceCharacter;
        final vocalCharactersId = jobPostModel.vocalCharacters;
        if (vocalCharactersId != null) {
          selectedVoiceChar = voiceCharacter?.firstWhereOrNull((e) => e == vocalCharactersId);
        }
        final experienceLevel = state.dropDownResponseModel!.experience;
        final experienceId = jobPostModel.experienceLevel;
        if (experienceId != null) {
          selectedExpelience = experienceLevel?.firstWhereOrNull((e) => e == experienceId);
        }

        final voiceLanguage = state.dropDownResponseModel?.voiceLanguage;
        final languageId = jobPostModel.language;
        if (languageId != null) {
          selectedLanguage = voiceLanguage?.firstWhereOrNull((e) => e == languageId);
        }

        final accent = state.dropDownResponseModel?.accent;
        final accentId = jobPostModel.accent;
        if (accentId != null) {
          selectedAccent = accent?.firstWhereOrNull((e) => e == accentId);
        }

        final gender = state.dropDownResponseModel?.gender;
        final voiceGenderId = jobPostModel.voiceGender;
        if (voiceGenderId != null) {
          selectedVoiceGender = gender?.firstWhereOrNull((e) => e == voiceGenderId);
        }

        final ageRange = state.dropDownResponseModel?.ageRange;
        final voiceAgeId = jobPostModel.voiceAge;
        if (voiceAgeId != null) {
          selectedVoiceAge = ageRange?.firstWhereOrNull((e) => e == voiceAgeId);
        }
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return PostJobPageFrame(
      onNextPressed: () async {
        if (selectedVoiceChar == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("voice character"));
          return;
        }
        if (selectedExpelience == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("experience"));
          return;
        }
        if (selectedLanguage == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("language"));
          return;
        }
        if (selectedAccent == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("accent"));
          return;
        }
        if (selectedVoiceGender == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("voice gender"));
          return;
        }
        if (selectedVoiceAge == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("voice age"));
          return;
        }
        final cubit = context.read<PostAJobCubit>();
        JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
        jobPostModel?.vocalCharacters = selectedVoiceChar;
        if (selectedVoiceChar != null && selectedVoiceChar!.name!.toLowerCase().contains("other")) {
          jobPostModel?.otherVocalCharacter = otherCharController.text;
        }
        jobPostModel?.experienceLevel = selectedExpelience;
        jobPostModel?.language = selectedLanguage;
        jobPostModel?.accent = selectedAccent;
        jobPostModel?.voiceGender = selectedVoiceGender;
        jobPostModel?.voiceAge = selectedVoiceAge;
        if (jobPostModel != null) cubit.updateJobDetails(jobPostModel: jobPostModel);
        cubit.goToNextPage();
      },
      children: [
        const Align(
            alignment: Alignment.centerLeft,
            child: Text24And20SemiBold(AppStrings.whichOptionDescribes)),
        16.ph,
        BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
          builder: (context, state) {
            if (state is StaticDataDropdownLoadingState) {
              return Shimmer.fromColors(
                baseColor: colorScheme.lightGreyB2B2B2,
                highlightColor: colorScheme.white,
                child: Container(
                  width: double.infinity,
                  height: 56.h,
                  decoration: BoxDecoration(
                      color: colorScheme.white,
                      borderRadius: BorderRadius.circular(12.r)),
                ),
              );
            }
            if (state is StaticDataDropdownErrorState) {
              return InkWell(
                onTap: () {},
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Error occured while loading data",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 10,
                        color: colorScheme.primaryGrey,
                      ),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: colorScheme.primary,
                          width: 2.0,
                        ),
                        //Border.all
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            AppStrings.refresh,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Icon(
                            Icons.refresh,
                            size: 15,
                            color: colorScheme.primary,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }
            if (state is StaticDataDropdownSuccessState) {
              return Align(
                alignment: Alignment.centerLeft,
                child: Wrap(
                  runSpacing: 12.w,
                  runAlignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.start,
                  alignment: WrapAlignment.start,
                  children:
                      state.dropDownResponseModel!.voiceCharacter!.map((item) {
                    return InkWell(
                      onTap: () {
                        selectedVoiceChar = item;
                        setState(() {});
                      },
                      child: Card(
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                                color: selectedVoiceChar?.id == item.id
                                    ? colorScheme.secondary
                                    : colorScheme.lightGreyD9D9D9,
                                width: selectedVoiceChar?.id == item.id
                                    ? 1
                                    : 1.2)),
                        color: selectedVoiceChar?.id == item.id
                            ? colorScheme.lightBlueEBF1FF
                            : colorScheme.white,
                        child: Padding(
                          padding: !Responsive.isDesktop(context) ? EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.w) : EdgeInsets.all(16.h),
                          child: TextTitle18And14(
                            item.name ?? "",
                            fontWeight: selectedVoiceChar?.id == item.id
                                ? FontWeight.w600
                                : null,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              );
            }
            return const SizedBox();
          },
        ),
        if (selectedVoiceChar != null && selectedVoiceChar!.name!.toLowerCase().contains("other")) ...[
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          AppTextFormField(
            isParentField: true,
            maxLength: 100,
            controller: otherCharController,
            hintText: AppStrings.specifyOtherEnter,
            titleText: AppStrings.specifyOther,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ]")),
            ],
            validator: (value) {
              return Validator.emptyValidator(
                  value, ValidationMsg.plsEntervalidCountry);
            },
            isAutovalidateModeOn: autovalidation,
          ),
        ],

        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        !Responsive.isDesktop(context) ? 
        BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
          builder: (context, state) {
            List<DropdownData>? experience = [];
            if (state is StaticDataDropdownSuccessState) {
              experience.addAll(state.dropDownResponseModel?.experience ?? []);
            }
            return CustomDropDownWidget<DropdownData>(
                isParentField: true,
                isLoading: state is StaticDataDropdownLoadingState,
                isError: state is StaticDataDropdownErrorState,
                hintText: AppStrings.selectExperience,
                titleText: AppStrings.experience,
                selectedValue: selectedExpelience?.name,
                items: experience
                    .map((item) => DropdownMenuItem(
                        value: item,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextTitle18And14(
                              item.name ?? "",
                            ),
                            if (item.id == selectedExpelience?.id)
                              const Icon(Icons.check),
                          ],
                        )))
                    .toList(),
                onChanged: (value) {
                  selectedExpelience = value;
                  setState(() {});
                },
                value: selectedExpelience);
          },
        ) : Row(
          children: [
           Expanded(
             child: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
                       builder: (context, state) {
              List<DropdownData>? experience = [];
              if (state is StaticDataDropdownSuccessState) {
                experience.addAll(state.dropDownResponseModel?.experience ?? []);
              }
              return CustomDropDownWidget<DropdownData>(
                  isParentField: true,
                  isLoading: state is StaticDataDropdownLoadingState,
                  isError: state is StaticDataDropdownErrorState,
                  hintText: AppStrings.selectExperience,
                  titleText: AppStrings.experience,
                  selectedValue: selectedExpelience?.name,
                  items: experience
                      .map((item) => DropdownMenuItem(
                          value: item,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextTitle18And14(
                                item.name ?? "",
                              ),
                              if (item.id == selectedExpelience?.id)
                                const Icon(Icons.check),
                            ],
                          )))
                      .toList(),
                  onChanged: (value) {
                    selectedExpelience = value;
                    setState(() {});
                  },
                  value: selectedExpelience);
              }),
           ),
         20.pw,
          Expanded(
            child: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
            builder: (context, state) {
              List<DropdownData>? voiceLanguage = [];
              if (state is StaticDataDropdownSuccessState) {
                voiceLanguage
                    .addAll(state.dropDownResponseModel?.voiceLanguage ?? []);
              }
              return CustomDropDownWidget<DropdownData>(
                  isParentField: true,
                  isLoading: state is StaticDataDropdownLoadingState,
                  isError: state is StaticDataDropdownErrorState,
                  hintText: AppStrings.selectLanguage,
                  titleText: AppStrings.language,
                  selectedValue: selectedLanguage?.name,
                  items: voiceLanguage
                      .map((item) => DropdownMenuItem(
                          value: item,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextTitle18And14(
                                item.name ?? "",
                              ),
                              if (item.id == selectedLanguage?.id)
                                const Icon(Icons.check),
                            ],
                          )))
                      .toList(),
                  onChanged: (value) {
                    selectedLanguage = value;
                    setState(() {});
                  },
                  value: selectedLanguage);
            }),
          ),
        ],),
        if(!Responsive.isDesktop(context))...[
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
          builder: (context, state) {
            List<DropdownData>? voiceLanguage = [];
            if (state is StaticDataDropdownSuccessState) {
              voiceLanguage
                  .addAll(state.dropDownResponseModel?.voiceLanguage ?? []);
            }
            return CustomDropDownWidget<DropdownData>(
                isParentField: true,
                isLoading: state is StaticDataDropdownLoadingState,
                isError: state is StaticDataDropdownErrorState,
                hintText: AppStrings.selectLanguage,
                titleText: AppStrings.language,
                selectedValue: selectedLanguage?.name,
                items: voiceLanguage
                    .map((item) => DropdownMenuItem(
                        value: item,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextTitle18And14(
                              item.name ?? "",
                            ),
                            if (item.id == selectedLanguage?.id)
                              const Icon(Icons.check),
                          ],
                        )))
                    .toList(),
                onChanged: (value) {
                  selectedLanguage = value;
                  setState(() {});
                },
                value: selectedLanguage);
          },
        ),
        ],
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        !Responsive.isDesktop(context) ?
        BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
          builder: (context, state) {
            List<DropdownData>? accent = [];
            if (state is StaticDataDropdownSuccessState) {
              accent.addAll(state.dropDownResponseModel?.accent ?? []);
            }
            return CustomDropDownWidget<DropdownData>(
                isParentField: true,
                isLoading: state is StaticDataDropdownLoadingState,
                isError: state is StaticDataDropdownErrorState,
                hintText: AppStrings.selectAccent,
                titleText: AppStrings.accent,
                selectedValue: selectedAccent?.name,
                items: accent
                    .map((item) => DropdownMenuItem(
                        value: item,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextTitle18And14(
                              item.name ?? "",
                            ),
                            if (item.id == selectedAccent?.id)
                              const Icon(Icons.check),
                          ],
                        )))
                    .toList(),
                onChanged: (value) {
                  selectedAccent = value;
                  setState(() {});
                },
                value: selectedAccent);
          },
        ) : Row(
          children: [
          Expanded(
            child: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
            builder: (context, state) {
              List<DropdownData>? accent = [];
              if (state is StaticDataDropdownSuccessState) {
                accent.addAll(state.dropDownResponseModel?.accent ?? []);
              }
              return CustomDropDownWidget<DropdownData>(
                  isParentField: true,
                  isLoading: state is StaticDataDropdownLoadingState,
                  isError: state is StaticDataDropdownErrorState,
                  hintText: AppStrings.selectAccent,
                  titleText: AppStrings.accent,
                  selectedValue: selectedAccent?.name,
                  items: accent
                      .map((item) => DropdownMenuItem(
                          value: item,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextTitle18And14(
                                item.name ?? "",
                              ),
                              if (item.id == selectedAccent?.id)
                                const Icon(Icons.check),
                            ],
                          )))
                      .toList(),
                  onChanged: (value) {
                    selectedAccent = value;
                    setState(() {});
                  },
                  value: selectedAccent);
            },
                    ),
          ), 
          20.pw,
          Expanded(
            child: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
            builder: (context, state) {
              List<DropdownData>? gender = [];
              if (state is StaticDataDropdownSuccessState) {
                gender.addAll(state.dropDownResponseModel?.gender ?? []);
              }
              return CustomDropDownWidget<DropdownData>(
                  isParentField: true,
                  isLoading: state is StaticDataDropdownLoadingState,
                  isError: state is StaticDataDropdownErrorState,
                  hintText: AppStrings.selectVoiceGender,
                  titleText: AppStrings.voiceGender,
                  selectedValue: selectedVoiceGender?.name,
                  items: gender
                      .map((item) => DropdownMenuItem(
                          value: item,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextTitle18And14(
                                item.name ?? "",
                              ),
                              if (item.id == selectedVoiceGender?.id)
                                const Icon(Icons.check),
                            ],
                          )))
                      .toList(),
                  onChanged: (value) {
                    selectedVoiceGender = value;
                    setState(() {});
                  },
                  value: selectedVoiceGender);
            },
                    ),
          ),
        ]),
        if(!Responsive.isDesktop(context))...[
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
          builder: (context, state) {
            List<DropdownData>? gender = [];
            if (state is StaticDataDropdownSuccessState) {
              gender.addAll(state.dropDownResponseModel?.gender ?? []);
            }
            return CustomDropDownWidget<DropdownData>(
                isParentField: true,
                isLoading: state is StaticDataDropdownLoadingState,
                isError: state is StaticDataDropdownErrorState,
                hintText: AppStrings.selectVoiceGender,
                titleText: AppStrings.voiceGender,
                selectedValue: selectedVoiceGender?.name,
                items: gender
                    .map((item) => DropdownMenuItem(
                        value: item,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextTitle18And14(
                              item.name ?? "",
                            ),
                            if (item.id == selectedVoiceGender?.id)
                              const Icon(Icons.check),
                          ],
                        )))
                    .toList(),
                onChanged: (value) {
                  selectedVoiceGender = value;
                  setState(() {});
                },
                value: selectedVoiceGender);
          },
        )],
        !Responsive.isDesktop(context) ? 16.ph : 24.ph,
        !Responsive.isDesktop(context) ?
        Column(
          children: [
            BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
              builder: (context, state) {
                List<AgeRange>? ageRange = [];
                if (state is StaticDataDropdownSuccessState) {
                  ageRange.addAll(state.dropDownResponseModel?.ageRange ?? []);
                }
                return CustomDropDownWidget<AgeRange>(
                    isParentField: true,
                    isLoading: state is StaticDataDropdownLoadingState,
                    isError: state is StaticDataDropdownErrorState,
                    hintText: AppStrings.selectVoiceAge,
                    titleText: AppStrings.voiceAge,
                    selectedValue: selectedVoiceAge?.name,
                    items: ageRange
                        .map((item) => DropdownMenuItem(
                            value: item,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                TextTitle18And14(
                                  item.name ?? "",
                                ),
                                if (item.id == selectedVoiceAge?.id)
                                  const Icon(Icons.check),
                              ],
                            )))
                        .toList(),
                    onChanged: (value) {
                      selectedVoiceAge = value;
                      setState(() {});
                    },
                    value: selectedVoiceAge);
              },
            ),
          ],
        ): Row(children: [
             Expanded(
               child: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
                builder: (context, state) {
                  List<AgeRange>? ageRange = [];
                  if (state is StaticDataDropdownSuccessState) {
                    ageRange.addAll(state.dropDownResponseModel?.ageRange ?? []);
                  }
                  return CustomDropDownWidget<AgeRange>(
                      isParentField: true,
                      isLoading: state is StaticDataDropdownLoadingState,
                      isError: state is StaticDataDropdownErrorState,
                      hintText: AppStrings.selectVoiceAge,
                      titleText: AppStrings.voiceAge,
                      selectedValue: selectedVoiceAge?.name,
                      items: ageRange
                          .map((item) => DropdownMenuItem(
                              value: item,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  TextTitle18And14(
                                    item.name ?? "",
                                  ),
                                  if (item.id == selectedVoiceAge?.id)
                                    const Icon(Icons.check),
                                ],
                              )))
                          .toList(),
                      onChanged: (value) {
                        selectedVoiceAge = value;
                        setState(() {});
                      },
                      value: selectedVoiceAge);
                },
              )),
             20.pw,
             Spacer()
        ],),
        96.ph,
      ],
    );
  }
}
