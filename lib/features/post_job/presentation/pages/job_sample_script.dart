import 'package:audioplayers/audioplayers.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_audio_video_cubit.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../data/enums/sample_script_options.dart';
import '../../data/enums/sample_script_type.dart';
import '../../models/job_post_model.dart';

class JobSampleScript extends StatefulWidget {
  const JobSampleScript({super.key});

  @override
  State<JobSampleScript> createState() => _JobSampleScriptState();
}

class _JobSampleScriptState extends State<JobSampleScript> {
  bool autovalidation = false;
  final formGlobalKey = GlobalKey<FormState>();
  final TextEditingController pasteScriptController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final audioCubit = context.read<UploadAudioVideoCubit>();
    audioCubit.maxFileSize = 10;
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      pasteScriptController.text = jobPostModel.urlScriptSample ?? '';

      final sampleScriptType = jobPostModel.sampleScriptType;

      if (sampleScriptType != null) {
        SampleScriptOptions? option = mapScriptTypeToOption(sampleScriptType);
        if (option != null) {
          cubit.updateSelectedScriptOption(option);
        }
        if (option == SampleScriptOptions.uploadFile &&
            jobPostModel.uploadMediaInfo != null) {
          context.read<UploadAudioVideoCubit>().deleteAudio(0);
          context.read<UploadAudioVideoCubit>().addMediaFiles(
            newFiles: [jobPostModel.uploadMediaInfo!],
            isAudio: true,
          );
        }
      }
      setState(() {});
    });
  }

  SampleScriptOptions? mapScriptTypeToOption(SampleScriptType type) {
    switch (type) {
      case SampleScriptType.urlSampleScript:
        return SampleScriptOptions.pasteScript; //  URL -> Paste
      case SampleScriptType.fileSampleScript:
        return SampleScriptOptions.uploadFile; // File -> Upload
      case SampleScriptType.noScriptSample:
        return SampleScriptOptions.noSample; // No Script -> No Sample
      default:
        return null;
    }
  }

  static SampleScriptType getSampleScriptTypeFromString(SampleScriptOptions options) {
    switch (options) {
      case SampleScriptOptions.pasteScript:
        return SampleScriptType.urlSampleScript;
      case SampleScriptOptions.uploadFile:
        return SampleScriptType.fileSampleScript;
      case SampleScriptOptions.noSample:
        return SampleScriptType.noScriptSample;
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocBuilder<PostAJobCubit, PostAJobState>(
      builder: (context, state) {
        final postAJobCubit = context.read<PostAJobCubit>();
        return PostJobPageFrame(
          formKey: formGlobalKey,
          // onBackPressed: () async {
          //   final cubit = context.read<UploadAudioVideoCubit>();
          //   for (var player in cubit.state.audioPlayers) {
          //     if (player.state == PlayerState.playing) {
          //       await player.pause();
          //     }
          //   }
          //   postAJobCubit.goToPreviousPage();
          // },
          onNextPressed: () async {
        FocusManager.instance.primaryFocus?.unfocus();
            if (state.selectedScriptOption == null) {
              CustomToast.show(
                  context: context,
                  message: ValidationMsg.plsSelect("script type"));
              return;
            }
            if (state.selectedScriptOption == SampleScriptOptions.uploadFile &&
                context.read<UploadAudioVideoCubit>().state.audioFiles.isEmpty) {
              CustomToast.show(
                  context: context, message: ValidationMsg.plsSelectAudio);
              return;
            }
            if (formGlobalKey.currentState!.validate()) {
              final audioCubit = context.read<UploadAudioVideoCubit>();
              for (var player in audioCubit.state.audioPlayers) {
                if (player.state == PlayerState.playing) {
                  await player.pause();
                }
              }

              final cubit = context.read<PostAJobCubit>();
              JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
              jobPostModel?.sampleScriptType = getSampleScriptTypeFromString(state.selectedScriptOption!);
              if (state.selectedScriptOption == SampleScriptOptions.pasteScript) {
                jobPostModel?.urlScriptSample = pasteScriptController.text.trim();
                context.read<UploadAudioVideoCubit>().deleteAudio(0);
                jobPostModel?.uploadMediaInfo = null;
              }
              if (state.selectedScriptOption == SampleScriptOptions.uploadFile) {
                jobPostModel?.urlScriptSample = null;
                final file = context.read<UploadAudioVideoCubit>().state.audioFiles[0];
                jobPostModel?.uploadMediaInfo = file;
              }
              if (state.selectedScriptOption == SampleScriptOptions.noSample) {
                jobPostModel?.urlScriptSample = null;
                context.read<UploadAudioVideoCubit>().deleteAudio(0);
                jobPostModel?.uploadMediaInfo = null;
              }
              cubit.updateJobDetails(jobPostModel: jobPostModel!);
              postAJobCubit.goToNextPage();
            } else {
              autovalidation = true;
              setState(() {});
            }
          },
          children: [
            const Align(
                alignment: Alignment.centerLeft,
                child: Text24And20SemiBold(AppStrings.sampleScript)),
            16.ph,
            !Responsive.isDesktop(context)
                ? Column(
                    children: SampleScriptOptions.values.map((e) {
                      return Container(
                        margin: EdgeInsets.only(bottom: 24.h),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: state.selectedScriptOption == e
                                ? colorScheme.primary
                                : colorScheme.lightGreyD9D9D9,
                            width: 1.2,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: RadioListTile<SampleScriptOptions>(
                          title: TextTitle18And14(
                            e.toString(),
                            fontWeight: state.selectedScriptOption == e
                                ? FontWeight.w600
                                : null,
                          ),
                          value: e,
                          groupValue: state.selectedScriptOption,
                          onChanged: postAJobCubit.updateSelectedScriptOption,
                        ),
                      );
                    }).toList(),
                  )
                : Row(
                    children: SampleScriptOptions.values.map((e) {
                      return Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Container(
                            margin: EdgeInsets.only(bottom: 24.h),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: state.selectedScriptOption == e
                                    ? colorScheme.primary
                                    : colorScheme.lightGreyD9D9D9,
                                width: 1.2,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: RadioListTile<SampleScriptOptions>(
                              title: TextTitle18And14(
                                e.toString(),
                                fontWeight: state.selectedScriptOption == e
                                    ? FontWeight.w600
                                    : null,
                              ),
                              value: e,
                              groupValue: state.selectedScriptOption,
                              onChanged: postAJobCubit.updateSelectedScriptOption,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
            24.ph,
            if (state.selectedScriptOption ==
                SampleScriptOptions.pasteScript) ...[
              const Align(
                  alignment: Alignment.centerLeft,
                  child: Text24And20SemiBold(AppStrings.pasteUrl)),
              16.0.ph,
              AppTextFormField(
                controller: pasteScriptController,
                hintText: AppStrings.pasteScriptHint,
                maxLines: 5,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
                validator: (value) {
                  return Validator.emptyValidator(
                      value, ValidationMsg.plsEntervalid("script or URL"));
                },
              ),
            ],
            if (state.selectedScriptOption ==
                SampleScriptOptions.uploadFile) ...[
              const Align(
                  alignment: Alignment.centerLeft,
                  child: Text24And20SemiBold(AppStrings.uploadScriptSample)),
              16.ph,
              BlocConsumer<UploadAudioVideoCubit, UploadAudioVideoState>(
                listener: (context, state) {
                  if (state.errorMessage != null &&
                      state.errorMessage!.isNotEmpty) {
                    CustomToast.show(
                        context: context, message: state.errorMessage!);
                    context.read<UploadAudioVideoCubit>().resetError();
                  }
                },
                builder: (context, state) {
                  if (state.audioFiles.isNotEmpty) {
                    return ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.audioFiles.length,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        final isPlaying = state.audioPlayers[index].state ==
                            PlayerState.playing;
                        final duration = state.durations[index];
                        final position = state.positions[index];
                        return Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 9.h),
                                  decoration: BoxDecoration(
                                    color: colorScheme.white,
                                    borderRadius: BorderRadius.circular(12.r),
                                    border: Border.all(
                                        color: colorScheme.lightGreyD9D9D9),
                                  ),
                                  child: Row(
                                    children: [
                                      IconButton(
                                        icon: SvgPicture.asset(
                                          isPlaying
                                              ? AppImages.pauseIcon
                                              : AppImages.play,
                                        ),
                                        onPressed: () => context
                                            .read<UploadAudioVideoCubit>()
                                            .playAudio(index),
                                      ),
                                      Expanded(
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: SliderTheme(
                                                data: SliderThemeData(
                                                  thumbShape:
                                                      RoundSliderThumbShape(
                                                          enabledThumbRadius:
                                                              6.r),
                                                ),
                                                child: Slider(
                                                  thumbColor: colorScheme.white,
                                                  activeColor:
                                                      colorScheme.primary,
                                                  inactiveColor: colorScheme
                                                      .lightGreyD9D9D9,
                                                  value: position.inSeconds
                                                      .toDouble(),
                                                  max: duration.inSeconds
                                                      .toDouble(),
                                                  onChanged: (value) async {
                                                    final newPosition =
                                                        Duration(
                                                            seconds:
                                                                value.toInt());
                                                    await state
                                                        .audioPlayers[index]
                                                        .seek(newPosition);
                                                  },
                                                ),
                                              ),
                                            ),
                                            TextTitle14(
                                              isPlaying
                                                  ? "${(duration - position).inMinutes}:${((duration - position).inSeconds % 60).toString().padLeft(2, '0')}"
                                                  : "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: SvgPicture.asset(AppImages.trash),
                                onPressed: () {
                                  final cubit = context.read<PostAJobCubit>();
                                  context
                                      .read<UploadAudioVideoCubit>()
                                      .deleteAudio(index);
                                  JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
                                  jobPostModel?.uploadMediaInfo = null;
                                  cubit.updateJobDetails(jobPostModel: jobPostModel!);
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  }
                  return DottedBorder(
                    borderType: BorderType.RRect,
                    radius: Radius.circular(12.r),
                    padding: EdgeInsets.all(20.h),
                    color: colorScheme.secondary,
                    strokeWidth: 1.5,
                    dashPattern: const [10, 10],
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0.r),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: 20.h,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(AppImages.export),
                          12.ph,
                          TextTitle14(
                            AppStrings.browseFileToUploadFileType(maxSize: 10),
                            textAlign: TextAlign.center,
                          ),
                          12.ph,
                          PrimaryButton(
                            width: 164.w,
                            height: 44.h,
                            backgroundColor: colorScheme.lightGreenFDFFDA,
                            onPressed: () async {
                              if (state.audioFiles.isEmpty) {
                                context
                                    .read<UploadAudioVideoCubit>()
                                    .browseFiles(context, maxFiles: 1);
                              } else {
                                CustomToast.show(
                                  context: context,
                                  message: AppStrings.youCanUploadMaximum1Files,
                                );
                              }
                            },
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(40.r),
                              side: BorderSide(
                                color: colorScheme.primary,
                                width: 1.2.h,
                              ),
                            ),
                            child: TextTitle14(
                              AppStrings.browseFile,
                              style: theme.textTheme.titleMedium!
                                  .copyWith(fontSize: 16.sp),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
            96.ph,
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    pasteScriptController.dispose();
    super.dispose();
  }
}
