import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/enums/location_type_enum.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/features/google_address/presentation.dart/address_input_field.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import '../../../../utils/custom_toast.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/radio_container.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/post_a_job_cubit.dart';
import '../../models/job_post_model.dart';
import '../widgets/post_job_page_frame.dart';

class JobLocation extends StatefulWidget {
  const JobLocation({super.key});

  @override
  State<JobLocation> createState() => _JobLocationState();
}

class _JobLocationState extends State<JobLocation> {
  bool autovalidation = false;
  LocationType? locationType;
  Map<String, dynamic>? locationData;
  late TextEditingController studioNameController;
  late TextEditingController address1Controller;
  late TextEditingController address2Controller;
  late TextEditingController cityController;
  late TextEditingController stateController;
  late TextEditingController postalController;
  late TextEditingController countryController;
  final formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    studioNameController = TextEditingController();
    address1Controller = TextEditingController();
    address2Controller = TextEditingController();
    cityController = TextEditingController();
    stateController = TextEditingController();
    postalController = TextEditingController();
    countryController = TextEditingController();
    countryController.text = "India";

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cubit = context.read<PostAJobCubit>();
      final jobPostModel = cubit.loadJobPostFromHive();
      if (jobPostModel == null) return;

      if (jobPostModel.locationType == LocationType.remote) {
        locationType = LocationType.remote;
      } else if (jobPostModel.locationType == LocationType.inStudio) {
        locationType = LocationType.inStudio;
        studioNameController.text = jobPostModel.studioName ?? '';
        address1Controller.text = jobPostModel.addressLine1 ?? '';
        address2Controller.text = jobPostModel.addressLine2 ?? '';
        cityController.text = jobPostModel.city ?? '';
        stateController.text = jobPostModel.state ?? '';
        postalController.text = jobPostModel.postalCode?.toString() ?? '';
        countryController.text = jobPostModel.country ?? '';
      }
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return PostJobPageFrame(
      formKey: formGlobalKey,
      onNextPressed: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        if (locationType == null) {
          CustomToast.show(
              context: context, message: ValidationMsg.plsSelect("location"));
          return;
        }
        if (formGlobalKey.currentState!.validate()) {
          final cubit = context.read<PostAJobCubit>();
          JobPostModel? jobPostModel = cubit.loadJobPostFromHive();
          jobPostModel?.locationType = locationType;
          if (locationType == LocationType.inStudio) {
            jobPostModel?.studioName = studioNameController.text.trim();
            jobPostModel?.addressLine1 = address1Controller.text.trim();
            jobPostModel?.addressLine2 = address2Controller.text.trim();
            jobPostModel?.city = cityController.text.trim();
            jobPostModel?.state = stateController.text.trim();
            jobPostModel?.postalCode = int.tryParse(postalController.text);
            jobPostModel?.country = countryController.text.trim();
            jobPostModel?.latitude = locationData?['location']['lat'];
            jobPostModel?.longitude = locationData?['location']['lng'];
          } else {
            // In case user selects remote location after selecting In Studio
            jobPostModel?.studioName = null;
            jobPostModel?.addressLine1 = null;
            jobPostModel?.addressLine2 = null;
            jobPostModel?.city = null;
            jobPostModel?.state = null;
            jobPostModel?.postalCode = null;
            jobPostModel?.country = null;
          }
          cubit.updateJobDetails(jobPostModel: jobPostModel!);
          cubit.goToNextPage();
        } else {
          autovalidation = true;
          setState(() {});
          return;
        }
      },
      children: [
        const Align(
            alignment: Alignment.centerLeft,
            child: Text24And20SemiBold(AppStrings.selectLocation)),
        16.ph,
        !Responsive.isDesktop(context)
            ? Row(
                children: [
                  Expanded(
                    child: RadioContainer(
                        groupValue: locationType,
                        onChanged: onLocationTypeChange,
                        value: LocationType.remote,
                        title: AppStrings.remote),
                  ),
                  11.pw,
                  Expanded(
                    child: RadioContainer(
                        groupValue: locationType,
                        onChanged: onLocationTypeChange,
                        value: LocationType.inStudio,
                        title: AppStrings.inStudio),
                  ),
                ],
              )
            : Row(
                children: [
                  RadioContainer(
                      width: 308.w,
                      groupValue: locationType,
                      onChanged: onLocationTypeChange,
                      value: LocationType.remote,
                      title: AppStrings.remote),
                  14.pw,
                  RadioContainer(
                      width: 308.w,
                      groupValue: locationType,
                      onChanged: onLocationTypeChange,
                      value: LocationType.inStudio,
                      title: AppStrings.inStudio),
                ],
              ),
        if (locationType == LocationType.inStudio) ...[
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          AppTextFormField(
            isParentField: true,
            maxLength: 250,
            controller: studioNameController,
            hintText: AppStrings.enterStudioName,
            titleText: AppStrings.studioName,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
            ],
            validator: (String? value) {
              return Validator.emptyValidator(
                  value, ValidationMsg.plsEntervalid("studio name"));
            },
            isAutovalidateModeOn: autovalidation,
          ),
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          const Align(
              alignment: Alignment.centerLeft,
              child: Text24And20SemiBold(AppStrings.addAddress)),
          16.ph,
          AddressInputField(
            addressController: address1Controller,
            cityController: cityController,
            stateController: stateController,
            postalController: postalController,
            countryController: countryController,
            hintText: AppStrings.addressLine1Enter,
            titleText: AppStrings.addressLine1,
            isAutovalidateModeOn: autovalidation,
            validator: (String? value) {
              return Validator.emptyValidator(
                  value, ValidationMsg.plsEntervalid("address line 1"));
            },
            onLocationSelected: (data) {
              setState(() {
                locationData = data;
              });
            },
          ),
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          AppTextFormField(
            maxLength: 250,
            controller: address2Controller,
            hintText: AppStrings.addressLine2Enter,
            titleText: AppStrings.addressLine2,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,.\\-/#]")),
            ],
          ),
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: AppTextFormField(
                  maxLength: 100,
                  controller: cityController,
                  hintText: AppStrings.enterCity,
                  titleText: AppStrings.city,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
                  ],
                  validator: (value) {
                    return Validator.emptyValidator(
                        value, ValidationMsg.plsEntervalidCity);
                  },
                  isAutovalidateModeOn: autovalidation,
                ),
              ),
              16.pw,
              Expanded(
                child: AppTextFormField(
                  maxLength: 100,
                  controller: stateController,
                  hintText: AppStrings.enterState,
                  titleText: AppStrings.state,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
                  ],
                  validator: (value) {
                    return Validator.emptyValidator(
                        value, ValidationMsg.plsEntervalidState);
                  },
                  isAutovalidateModeOn: autovalidation,
                ),
              ),
            ],
          ),
          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: AppTextFormField(
                  maxLength: 6,
                  controller: postalController,
                  hintText: AppStrings.enterPCode,
                  titleText: AppStrings.postalCode,
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    return Validator.postalCode(value!);
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                  ],
                  isAutovalidateModeOn: autovalidation,
                ),
              ),
              16.pw,
              Expanded(
                child: AppTextFormField(
                  maxLength: 100,
                  // filled: true,
                  // fillColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                  // readOnly: true,
                  controller: countryController,
                  hintText: AppStrings.enterCountry,
                  titleText: AppStrings.country,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9 ,]")),
                  ],
                  validator: (value) {
                    return Validator.emptyValidator(
                        value, ValidationMsg.plsEntervalidCountry);
                  },
                  isAutovalidateModeOn: autovalidation,
                ),
              ),
            ],
          ),
        ],
        96.ph,
      ],
    );
  }

  void onLocationTypeChange(LocationType? locationType) {
    this.locationType = locationType!;
    setState(() {});
  }
}
