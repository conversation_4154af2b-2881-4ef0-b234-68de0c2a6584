import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/enums/budget_type_enum.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/enums/location_type_enum.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/post_job_page_frame.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/extensions/string_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/common_success_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../../core/navigation/navigation_service_impl.dart';
import '../../../../core/routes/route_names.dart';
import '../../../../utils/custom_toast.dart';
import '../../../../widgets/dialogs.dart';
import '../../../common/upload_media/bloc/upload_media_bloc.dart';
import '../../../common/upload_media/bloc/upload_media_state.dart';
import '../../../common/upload_media/data/pre_signed_url_req_model.dart';
import '../../../jobs/bloc/jobs_cubit.dart';
import '../../../upload_audio/bloc/upload_audio_video_cubit.dart';
import '../../data/enums/sample_script_type.dart';

class JobSummaryPage extends StatelessWidget {
  const JobSummaryPage({super.key});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    final postAJobCubit = context.read<PostAJobCubit>();
    final audioState = context.read<UploadAudioVideoCubit>();
    final jobPostModel = postAJobCubit.loadJobPostFromHive();

    Widget buildDetailTile(String title, String value, {bool isFile = false}) {
      return Padding(
        padding: EdgeInsets.symmetric(
            vertical: !Responsive.isDesktop(context) ? 8 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            isFile
                ? Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: colorScheme.lightGreyD9D9D9),
                        borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10.0, horizontal: 10),
                      child: Row(
                        children: [
                          Image.asset(AppImages.sampleDocumentIc,
                              height: !Responsive.isDesktop(context) ? 15 : 24,
                              width: !Responsive.isDesktop(context) ? 15 : 24),
                          8.pw,
                          Expanded(
                            child: ClickableText(
                              text: value,
                              fontSizeMobile: 16,
                              color: colorScheme.primaryGrey,
                              fontWeightMobile: FontWeight.w500,
                              fontSizeWeb: 20,
                              fontWeightWeb: FontWeight.w500,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : ClickableText(
                    text: value,
                    fontSizeMobile: 18,
                    fontWeightMobile: FontWeight.w500,
                    fontSizeWeb: 20,
                    fontWeightWeb: FontWeight.w500,
                  ),
            !Responsive.isDesktop(context) ? 4.ph : 2.ph,
            ClickableText(
              text: title,
              fontWeightMobile: FontWeight.w500,
              fontWeightWeb: FontWeight.w300,
              fontSizeMobile: 14,
              color: colorScheme.darkGrey525252,
              fontSizeWeb: 16,
            ),
          ],
        ),
      );
    }

    return jobPostModel == null
        ? const SizedBox()
        : MultiBlocListener(
            listeners: [
                BlocListener<PostAJobCubit, PostAJobState>(
          listener: (context, state) {
            if (state.isLoading) {
              Dialogs.showOnlyLoader(context);
            }
            if (state.errorMsg != null && state.errorMsg!.isNotEmpty) {
              context.pop();
              CustomToast.show(context: context, message: state.errorMsg!);
              postAJobCubit.resetErrorMsg();
            }
          },
    
                ),
                BlocListener<UploadMediaBloc, UploadMediaState>(
                  listener: (context, uploadState) {
                    if (uploadState is UploadMediaLoadingState) {
                      Dialogs.showOnlyLoader(context);
                    }
                    if (uploadState is UploadMediaSuccessState) {
                      context.pop();
    
                      jobPostModel.fileScriptSample = uploadState
                          .getPreSignedUrlModel
                          ?.presignedUrls
                          ?.jobAttachments
                          ?.first
                          .path;
    
                      postAJobCubit.postJobDetails(
                        jobPostModel: jobPostModel,
                        onSuccess: () {
                          context.pop();
                          showSuccessDialog(
                            context: context,
                            imagePath: AppImages.successImg,
                            description: AppStrings.jobPostedSuccess,
                            onPressed: () {
                              context.pop();
                              context.read<JobsCubit>().fetchJobs();
                              if (kIsWeb) {
                                NavigationServiceImpl.getInstance()?.doNavigation(
                                  context,
                                  routeName: RouteName.dashboard,
                                  useGo: true,
                                );
                              } else {
                                context.pop(true);
                              }
                            },
                          );
                        },
                      );
                    }
                    if (uploadState is UploadMediaErrorState) {
                      context.pop();
                      CustomToast.show(
                        context: context,
                        message: uploadState.errorMsg,
                      );
                    }
                  },
          
                ),
              ],
              child: PostJobPageFrame(
                title: AppStrings.jobSummary,
              nextBtnText: AppStrings.post,
              isEditing: true,
              onNextPressed: () {
                final jobPostModel = postAJobCubit.loadJobPostFromHive();
                if (jobPostModel == null) return;
                if (jobPostModel.sampleScriptType ==
                        SampleScriptType.fileSampleScript &&
                    audioState.state.audioFiles.isNotEmpty) {
                  context.read<UploadMediaBloc>().getPreSignedUrl(
                      contentType: "audio/mp3",
                      preSignedUrlReqModel: PreSignedUrlReqModel(
                          jobAttachments: audioState.state.audioFiles
                              .map((item) => MediaDetails(
                                    extn: item.ext,
                                    fileName: item.path?.split('/').last,
                                    fileType: "audio",
                                  ))
                              .toList()),
                      jobAttachments: audioState.state.audioFiles
                          .map((item) => item.bytes)
                          .whereType<Uint8List>()
                          .toList());
                } else {
                  postAJobCubit.postJobDetails(
                    jobPostModel: jobPostModel,
                    onSuccess: () {
                      context.pop();
                      showSuccessDialog(
                        context: context,
                        imagePath: AppImages.successImg,
                        description: AppStrings.jobPostedSuccess,
                        onPressed: () {
                          context.pop();
                          context.read<JobsCubit>().fetchJobs();
                          NavigationServiceImpl.getInstance()!.doNavigation(
                              context,
                              routeName: RouteName.dashboard,
                              useGo: true);
                        },
                      );
                    },
                  );
                }
              },
              children: [
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   crossAxisAlignment: CrossAxisAlignment.center,
                //   children: [
                //     TextDisplayLarge20And16(
                //       AppStrings.summary,
                //       color: colorScheme.primaryGrey,
                //       fontSize: Responsive.isDesktop(context) ? 28 : 20,
                //     ),
                //     TextButton(
                //         onPressed: () {
                //           context.read<PostAJobCubit>().resetErrorMsg();
                //           context.read<PostAJobCubit>().jumpToPage(0);
                //         },
                //         child: TextDisplayLarge20And16(
                //           AppStrings.edit,
                //           color: colorScheme.hyperlinkBlueColor,
                //         )),
                //   ],
                // ),
                // !Responsive.isDesktop(context) ? 16.ph : 32.ph,
                buildDetailTile(AppStrings.jobName, jobPostModel.title ?? ""),
                buildDetailTile(AppStrings.requirementAsterisk.removeLastChar(),
                    jobPostModel.requirement ?? ""),
                if (jobPostModel.jobTags != null &&
                    jobPostModel.jobTags!.isNotEmpty)
                  buildDetailTile(AppStrings.keywords,
                      jobPostModel.jobTags?.join(", ") ?? ""),
                buildDetailTile(AppStrings.category.removeLastChar(),
                    jobPostModel.jobCategory?.name.toString() ?? ''),
                buildDetailTile(AppStrings.location,
                    jobPostModel.locationType?.toString() ?? ''),
                if (jobPostModel.locationType == LocationType.inStudio)
                  buildDetailTile(AppStrings.studioLocation,
                    "${jobPostModel.studioName}, ${jobPostModel.addressLine1}${jobPostModel.addressLine2?.isNotEmpty == true ? ', ${jobPostModel.addressLine2}' : ''}, ${jobPostModel.city}, ${jobPostModel.state}, ${jobPostModel.postalCode}, ${jobPostModel.country}"),
                buildDetailTile(AppStrings.vocalRequirement,
                    jobPostModel.vocalCharacters?.name?.toString() ?? ''),
                 buildDetailTile(AppStrings.experience.removeLastChar(),
                    jobPostModel.experienceLevel?.name?.toString() ?? ''),
                buildDetailTile(AppStrings.language.removeLastChar(),
                    jobPostModel.language?.name?.toString() ?? ''),
                buildDetailTile(AppStrings.accent.removeLastChar(),
                    jobPostModel.accent?.name?.toString() ?? ''),
                buildDetailTile(AppStrings.voiceGender.removeLastChar(),
                    jobPostModel.voiceGender?.name?.toString() ?? ''),
                buildDetailTile(AppStrings.voiceAge.removeLastChar(),
                    jobPostModel.voiceAge?.name?.toString() ?? ''),
                buildDetailTile(
                    AppStrings.sampleScriptUrlUploadedFile,
                    jobPostModel.sampleScriptType ==
                            SampleScriptType.noScriptSample
                        ? "None"
                        : jobPostModel.sampleScriptType ==
                                SampleScriptType.urlSampleScript
                            ? jobPostModel.urlScriptSample ?? ""
                            : jobPostModel.sampleScriptType ==
                                    SampleScriptType.fileSampleScript
                                ? jobPostModel.uploadMediaInfo?.name ?? ""
                                : "",
                    isFile: jobPostModel.sampleScriptType ==
                        SampleScriptType.fileSampleScript),
                buildDetailTile(AppStrings.duration.removeLastChar(), Validator.formatDuration(jobPostModel.dubbingDurationHours,jobPostModel.dubbingDuration, jobPostModel.dubbingDurationSeconds)),
                buildDetailTile(
                  AppStrings.responseDeadline,
                  jobPostModel.responseDeadline != null
                      ? DateFormat('d MMMM yyyy')
                          .format(jobPostModel.responseDeadline!)
                      : 'N/A',
                ),
                buildDetailTile(
                  AppStrings.projectDeadline.removeLastChar(),
                  jobPostModel.projectDeadline != null
                      ? DateFormat('d MMMM yyyy')
                          .format(jobPostModel.projectDeadline!)
                      : 'N/A',
                ),
                buildDetailTile(AppStrings.budgetType.removeLastChar(),
                    jobPostModel.budgetType?.toString() ?? ''),
                buildDetailTile(
                    AppStrings.jobBudget,
                    jobPostModel.budgetType == BudgetType.fixed
                        ? '₹${jobPostModel.fixedBudget}'
                        : '₹${jobPostModel.minBudgetRange} - ₹${jobPostModel.maxBudgetRange}'),
                buildDetailTile(AppStrings.invitation,
                    jobPostModel.jobPostType?.toString() ?? ''),
                96.ph
              ],
            ));
  }
}
