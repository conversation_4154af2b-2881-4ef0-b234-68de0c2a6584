// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_post_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class JobPostModelAdapter extends TypeAdapter<JobPostModel> {
  @override
  final int typeId = 5;

  @override
  JobPostModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return JobPostModel(
      id: fields[0] as int?,
      client: fields[1] as int?,
      title: fields[2] as String?,
      requirement: fields[3] as String?,
      jobCategory: fields[4] as DropdownData?,
      locationType: fields[5] as LocationType?,
      studioName: fields[6] as String?,
      googleAddress: fields[7] as String?,
      addressLine1: fields[8] as String?,
      addressLine2: fields[9] as String?,
      city: fields[10] as String?,
      state: fields[11] as String?,
      postalCode: fields[12] as int?,
      country: fields[13] as String?,
      vocalCharacters: fields[14] as DropdownData?,
      otherVocalCharacter: fields[15] as String?,
      language: fields[16] as DropdownData?,
      accent: fields[17] as DropdownData?,
      voiceGender: fields[18] as DropdownData?,
      voiceAge: fields[19] as AgeRange?,
      sampleScriptType: fields[20] as SampleScriptType?,
      urlScriptSample: fields[21] as String?,
      fileScriptSample: fields[22] as String?,
      budgetType: fields[23] as BudgetType?,
      fixedBudget: fields[24] as String?,
      minBudgetRange: fields[25] as String?,
      maxBudgetRange: fields[26] as String?,
      dubbingDuration: fields[27] as String?,
      projectDeadline: fields[28] as DateTime?,
      responseDeadline: fields[29] as DateTime?,
      jobTags: (fields[30] as List?)?.cast<String>(),
      jobPostType: fields[31] as JobPostType?,
      associatedUsers: (fields[32] as List?)?.cast<int>(),
      location: (fields[33] as Map?)?.cast<String, dynamic>(),
      selectedUsers: fields[34] as UsersList?,
      uploadMediaInfo: fields[35] as MediaInfoModel?,
      experienceLevel: fields[36] as DropdownData?,
      dubbingDurationSeconds: fields[37] as String?,
      latitude: fields[38] as double?,
      longitude: fields[39] as double?,
      dubbingDurationHours: fields[40] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, JobPostModel obj) {
    writer
      ..writeByte(41)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.client)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.requirement)
      ..writeByte(4)
      ..write(obj.jobCategory)
      ..writeByte(5)
      ..write(obj.locationType)
      ..writeByte(6)
      ..write(obj.studioName)
      ..writeByte(7)
      ..write(obj.googleAddress)
      ..writeByte(8)
      ..write(obj.addressLine1)
      ..writeByte(9)
      ..write(obj.addressLine2)
      ..writeByte(10)
      ..write(obj.city)
      ..writeByte(11)
      ..write(obj.state)
      ..writeByte(12)
      ..write(obj.postalCode)
      ..writeByte(13)
      ..write(obj.country)
      ..writeByte(14)
      ..write(obj.vocalCharacters)
      ..writeByte(15)
      ..write(obj.otherVocalCharacter)
      ..writeByte(16)
      ..write(obj.language)
      ..writeByte(17)
      ..write(obj.accent)
      ..writeByte(18)
      ..write(obj.voiceGender)
      ..writeByte(19)
      ..write(obj.voiceAge)
      ..writeByte(20)
      ..write(obj.sampleScriptType)
      ..writeByte(21)
      ..write(obj.urlScriptSample)
      ..writeByte(22)
      ..write(obj.fileScriptSample)
      ..writeByte(23)
      ..write(obj.budgetType)
      ..writeByte(24)
      ..write(obj.fixedBudget)
      ..writeByte(25)
      ..write(obj.minBudgetRange)
      ..writeByte(26)
      ..write(obj.maxBudgetRange)
      ..writeByte(27)
      ..write(obj.dubbingDuration)
      ..writeByte(28)
      ..write(obj.projectDeadline)
      ..writeByte(29)
      ..write(obj.responseDeadline)
      ..writeByte(30)
      ..write(obj.jobTags)
      ..writeByte(31)
      ..write(obj.jobPostType)
      ..writeByte(32)
      ..write(obj.associatedUsers)
      ..writeByte(33)
      ..write(obj.location)
      ..writeByte(34)
      ..write(obj.selectedUsers)
      ..writeByte(35)
      ..write(obj.uploadMediaInfo)
      ..writeByte(36)
      ..write(obj.experienceLevel)
      ..writeByte(37)
      ..write(obj.dubbingDurationSeconds)
      ..writeByte(38)
      ..write(obj.latitude)
      ..writeByte(39)
      ..write(obj.longitude)
      ..writeByte(40)
      ..write(obj.dubbingDurationHours);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JobPostModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
