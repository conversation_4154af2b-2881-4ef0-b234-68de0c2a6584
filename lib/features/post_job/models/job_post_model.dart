import 'package:hive/hive.dart';
import 'package:the_voice_directory_flutter/utils/common_models/media_info_model.dart';

import '../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../data/enums/budget_type_enum.dart';
import '../data/enums/job_post_type.dart';
import '../data/enums/location_type_enum.dart';
import '../data/enums/sample_script_type.dart';
import '../data/model/users_list.dart';

part 'job_post_model.g.dart';

@HiveType(typeId: 5)
class JobPostModel {
  @HiveField(0)
  int? id;

  @HiveField(1)
  int? client;

  @HiveField(2)
  String? title;

  @HiveField(3)
  String? requirement;

  @HiveField(4)
  DropdownData? jobCategory;

  @HiveField(5)
  LocationType? locationType;

  @HiveField(6)
  String? studioName;

  @HiveField(7)
  String? googleAddress;

  @HiveField(8)
  String? addressLine1;

  @HiveField(9)
  String? addressLine2;

  @HiveField(10)
  String? city;

  @HiveField(11)
  String? state;

  @HiveField(12)
  int? postalCode;

  @HiveField(13)
  String? country;

  @HiveField(14)
  DropdownData? vocalCharacters;

  @HiveField(15)
  String? otherVocalCharacter;

  @HiveField(16)
  DropdownData? language;

  @HiveField(17)
  DropdownData? accent;

  @HiveField(18)
  DropdownData? voiceGender;

  @HiveField(19)
  AgeRange? voiceAge;

  @HiveField(20)
  SampleScriptType? sampleScriptType;

  @HiveField(21)
  String? urlScriptSample;

  @HiveField(22)
  String? fileScriptSample;

  @HiveField(23)
  BudgetType? budgetType;

  @HiveField(24)
  String? fixedBudget;

  @HiveField(25)
  String? minBudgetRange;

  @HiveField(26)
  String? maxBudgetRange;

  @HiveField(27)
  String? dubbingDuration;

  @HiveField(28)
  DateTime? projectDeadline;

  @HiveField(29)
  DateTime? responseDeadline;

  @HiveField(30)
  List<String>? jobTags;

  @HiveField(31)
  JobPostType? jobPostType;

  @HiveField(32)
  List<int>? associatedUsers;

  @HiveField(33)
  Map<String, dynamic>? location;

  @HiveField(34)
  UsersList? selectedUsers;

  @HiveField(35)
  MediaInfoModel? uploadMediaInfo;

  @HiveField(36)
  DropdownData? experienceLevel;

  @HiveField(37)
  String? dubbingDurationSeconds;

  @HiveField(38)
  double? latitude;

  @HiveField(39)
  double? longitude;

  @HiveField(40)
  String? dubbingDurationHours;

  JobPostModel({
    this.id,
    this.client,
    this.title,
    this.requirement,
    this.jobCategory,
    this.locationType,
    this.studioName,
    this.googleAddress,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.vocalCharacters,
    this.otherVocalCharacter,
    this.language,
    this.accent,
    this.voiceGender,
    this.voiceAge,
    this.sampleScriptType,
    this.urlScriptSample,
    this.fileScriptSample,
    this.budgetType,
    this.fixedBudget,
    this.minBudgetRange,
    this.maxBudgetRange,
    this.dubbingDuration,
    this.projectDeadline,
    this.responseDeadline,
    this.jobTags,
    this.jobPostType,
    this.associatedUsers,
    this.location,
    this.selectedUsers,
    this.uploadMediaInfo,
    this.experienceLevel,
    this.dubbingDurationSeconds,
    this.latitude,
    this.longitude,
    this.dubbingDurationHours,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    // if (id != null) {
    //   data['id'] = id;
    // }
    if (client != null) {
      data['client'] = client;
    }
    if (title != null) {
      data['title'] = title;
    }
    if (requirement != null) {
      data['requirement'] = requirement;
    }
    if (jobCategory != null && jobCategory!.id != null) {
      data['job_category'] = jobCategory!.id;
    }
    if (locationType != null) {
      data['location_type'] = locationType.toString();
    }
    if (studioName != null) {
      data['studio_name'] = studioName;
    }
    if (addressLine1 != null) {
      data['address_line_1'] = addressLine1;
    } 
    if (addressLine2 != null) {
      data['address_line_2'] = addressLine2;
    }
    if (city != null) {
      data['city'] = city;
    }
    if (state != null) {
      data['state'] = state;
    }
    if (postalCode != null) {
      data['postal_code'] = postalCode;
    }
    if (country != null) {
      data['country'] = country;
    }
    
    // Create location object with lat/lng if both are available
    if (latitude != null && longitude != null) {
      data['location'] = {
        'lat': latitude,
        'lng': longitude
      };
    } else if (location != null) {
      // Use existing location map if available
      data['location'] = location;
    }
    
    if (vocalCharacters != null && vocalCharacters!.id != null) {
      data['vocal_characters'] = vocalCharacters!.id;
    }
    if (otherVocalCharacter != null) {
      data['other_vocal_character'] = otherVocalCharacter;
    }
    if (language != null && language!.id != null) {
      data['language'] = language!.id;
    }
    if (accent != null && accent!.id != null) {
      data['accent'] = accent!.id;
    }
    if (voiceGender != null && voiceGender!.id != null) {
      data['voice_gender'] = voiceGender!.id;
    }
    if (voiceAge != null && voiceAge!.id != null) {
      data['voice_age'] = voiceAge!.id;
    }
    if (sampleScriptType != null) {
      data['sample_script_type'] = sampleScriptType.toString();
    }
    if (urlScriptSample != null) {
      data['url_script_sample'] = urlScriptSample;
    }
    if (fileScriptSample != null) {
      data['file_script_sample'] = fileScriptSample;
    }
    if (budgetType != null) {
      data['budget_type'] = budgetType.toString();
    }
    if (fixedBudget != null && fixedBudget!.isNotEmpty && double.tryParse(fixedBudget!) != null) {
      data['fixed_budget'] = double.tryParse(fixedBudget!);
    }
    if (minBudgetRange != null && minBudgetRange!.isNotEmpty && double.tryParse(minBudgetRange!) != null) {
      data['min_budget_range'] = double.tryParse(minBudgetRange!);
    }
    if (maxBudgetRange != null && maxBudgetRange!.isNotEmpty && double.tryParse(maxBudgetRange!) != null) {
      data['max_budget_range'] = double.tryParse(maxBudgetRange!);
    }
    if (dubbingDuration != null) {
      data['dubbing_duration'] = dubbingDuration;
    }
    if (projectDeadline != null) {
      data['project_deadline'] = projectDeadline!.toIso8601String();
    }
    if (responseDeadline != null) {
      data['response_deadline'] = responseDeadline!.toIso8601String();
    }
    if (jobTags != null && jobTags!.isNotEmpty) {
      data['job_tags'] = jobTags;
    }
    if (jobPostType != null) {
      data['job_post_type'] = jobPostType.toString();
    }
    if (associatedUsers != null && associatedUsers!.isNotEmpty) {
      data['associated_users'] = associatedUsers;
    }
    if (location != null) {
      data['location'] = location;
    }
    if (experienceLevel != null && experienceLevel!.id != null) {
      data['experience_level'] = experienceLevel!.id;
    }

    if (dubbingDurationSeconds != null) {
      data['dubbing_duration_seconds'] = dubbingDurationSeconds;
    }
    if (dubbingDurationHours != null) {
      data['dubbing_duration_hours'] = dubbingDurationHours;
    }

    return data;
  }
}
