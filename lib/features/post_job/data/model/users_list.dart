import 'package:hive/hive.dart';
part 'users_list.g.dart';
@HiveType(typeId: 12)
class User {
  @HiveField(0)
  int? id;
  @HiveField(1)
  String? firstName;
  @HiveField(2)
  String? lastName;
  @HiveField(3)
  String? email;
  @HiveField(4)
  String? profilePic;

  User({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.profilePic,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      email: json['email'] as String?,
      profilePic: json['profile_pic'] as String?,
    );
  }
}
@HiveType(typeId: 11)
class UsersList {
  @HiveField(0)
  List<User>? users;

  UsersList({this.users});

  factory UsersList.fromJson(List<dynamic> json) {
    return UsersList(
      users: json.map((item) => User.fromJson(item as Map<String, dynamic>)).toList(),
    );
  }
}
