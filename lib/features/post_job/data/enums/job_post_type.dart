import 'package:hive_flutter/hive_flutter.dart';

import '../../../../utils/string_constants/app_strings.dart';

part 'job_post_type.g.dart';

@HiveType(typeId: 9)
enum JobPostType {
  @HiveField(0)
  private(AppStrings.private),

  @HiveField(1)
  public(AppStrings.public);

  final String _name;

  const JobPostType(this._name);

  @override
  String toString() => _name;

  static JobPostType? getJobPostTypeFromString(String jobPostType) {
    switch (jobPostType) {
      case AppStrings.private:
        return JobPostType.private;
      case AppStrings.public:
        return JobPostType.public;
      default:
        return null;
    }
  }
}
