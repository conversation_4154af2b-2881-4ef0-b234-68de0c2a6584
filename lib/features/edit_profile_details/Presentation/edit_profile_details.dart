import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_state.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/media_info_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/verify_otp_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/email_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/mobile_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../utils/environment_config.dart';
import '../../../utils/pick_image.dart';
import '../../verifyOtp/bloc/verify_otp_bloc.dart';

class EditProfileDetails extends StatefulWidget {
  const EditProfileDetails({super.key});

  @override
  State<EditProfileDetails> createState() => _EditClientProfileDetailsState();
}

class _EditClientProfileDetailsState extends State<EditProfileDetails> {
  late TextEditingController _emailController;
  late TextEditingController _fNameController;
  late TextEditingController _lNameController;
  late TextEditingController _phoneNoController;
  MediaInfoModel? _userImage;
  final _formKey = GlobalKey<FormState>();
  final _formKeyPhone = GlobalKey<FormState>();
  List<DropdownData> selectedService = [];
  List<DropdownData> previousSelectedService = [];

  String? countryAbbr;
  String? countryCode;
  String? countryName;
  bool isVerified = true;
  bool isDataLoaded = false;

  @override
  void initState() {
    context.read<UserBloc>().getUserData();
    _emailController = TextEditingController();
    _fNameController = TextEditingController();
    _lNameController = TextEditingController();
    _phoneNoController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneNoController.dispose();
    _fNameController.dispose();
    _lNameController.dispose();
    super.dispose();
  }

  bool areListsNotEqualById(
    List<DropdownData> list1, List<DropdownData> list2) {
    if (list1.length != list2.length) return true;
    List<int?> ids1 = list1.map((e) => e.id).toList()..sort();
    List<int?> ids2 = list2.map((e) => e.id).toList()..sort();
    return !(const ListEquality().equals(ids1, ids2));
  }

  pickImage() async {
    ImagePickerService imagePickerService = ImagePickerService();
    final pickedImage = await imagePickerService.pickImageWithOptions(
      context: context,
    );
    if (pickedImage != null) {
      setState(() {
        _userImage = pickedImage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is UserLoadingState) {
            return const Center(child: Loader());
          }
          if (state is UserErrorState) {
            return ErrorScreen(
                onRetry: () {
                  context.read<UserBloc>().getUserData();
                },
                errorMessage: state.errorMsg,
                imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                    height: 200.h, width: 100.w));
          }

          if (state is UserSuccessState) {
            if (!isDataLoaded) {
              countryAbbr = state.userDataModel.countryAbbr ?? '';
              countryCode = state.userDataModel.countryCode ?? '';
              countryName = state.userDataModel.countryName ?? '';
              _emailController.text = state.userDataModel.email ?? '';
              _fNameController.text = state.userDataModel.firstName ?? "";
              _lNameController.text = state.userDataModel.lastName ?? "";
              _phoneNoController.text = state.userDataModel.phoneNumber ?? "";

               selectedService = state.userDataModel.services!.map((element) {
                return DropdownData(
                  id: element.id,
                  name: element.name,
                );
              }).toList();
              previousSelectedService = List.from(selectedService);
              isDataLoaded = true;
            }

            // }
            return SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: !Responsive.isDesktop(context)
                            ? EdgeInsets.zero
                            : EdgeInsets.all(44.0.h),
                        child: Card(
                          color: colorScheme.white,
                          elevation: !Responsive.isDesktop(context) ? 0 : 8,
                          shadowColor: colorScheme.lightGreyD9D9D9,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal:
                                    !Responsive.isDesktop(context)
                                    ? 16.h
                                    : 60.h,
                                vertical:
                                    !Responsive.isDesktop(context)
                                    ? 24.h
                                    : 40.h),
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment:
                                    !Responsive.isDesktop(context)
                                    ? CrossAxisAlignment.center
                                    : CrossAxisAlignment.start,
                                children: [
                                  if (!Responsive.isDesktop(context)) ...[
                                    Row(
                                      children: [
                                        CustomBackButton(
                                          onTap: () {
                                            context.pop();
                                          },
                                        ),
                                        8.pw,
                                        const Expanded(
                                          child: Center(
                                              child: Text24And20SemiBold(
                                                  AppStrings.editInformation)),
                                        ),
                                        SizedBox(width: 32.w)
                                      ],
                                    ),
                                  ] else
                                    Row(
                                      children: [
                                        const TextDisplayLarge36And26(
                                            AppStrings.editInformation),
                                        const Spacer(),
                                        InkWell(
                                          onTap: () {
                                            context.pop();
                                          },
                                          child: TextTitle18And14(
                                            AppStrings.cancel,
                                            color:
                                                colorScheme.hyperlinkBlueColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  !Responsive.isDesktop(context)
                                      ? 23.ph
                                      : 16.ph,
                                  Column(
                                    children: [
                                      Center(
                                        child: SizedBox(
                                          width: 120.h,
                                          height: 120.h,
                                          child: Stack(
                                            children: [
                                              CircleAvatar(
                                                radius: 60.r,
                                                backgroundColor:
                                                    colorScheme.blackE8E8E8,
                                                backgroundImage: _userImage !=
                                                        null
                                                    ? MemoryImage(
                                                        _userImage!.bytes)
                                                    : (state
                                                                .userDataModel
                                                                .profilePic
                                                                ?.isNotEmpty ==
                                                            true
                                                        ? Uri.parse(state.userDataModel.profilePic ?? '').isAbsolute
                                                            ? NetworkImage(state.userDataModel.profilePic ?? '')
                                                            : NetworkImage("${EnvironmentConfig.imageBaseUrl}${state.userDataModel.profilePic}")
                                                        : null),
                                                child: (_userImage != null ||
                                                        state
                                                                .userDataModel
                                                                .profilePic
                                                                ?.isNotEmpty ==
                                                            true)
                                                    ? null
                                                    : SvgPicture.asset(
                                                        "assets/images/user_icon.svg"),
                                              ),
                                              if (!kIsWeb)
                                                Positioned(
                                                  top: 0,
                                                  right: 0,
                                                  child: Container(
                                                    height: 32.h,
                                                    width: 32.h,
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color: colorScheme
                                                            .blackE8E8E8,
                                                        width: 2.0,
                                                      ),
                                                    ),
                                                    child: CircleAvatar(
                                                      backgroundColor:
                                                          colorScheme.white,
                                                      child: IconButton(
                                                        iconSize: 18,
                                                        padding:
                                                            EdgeInsets.zero,
                                                        icon: SvgPicture.asset(
                                                            AppImages
                                                                .cameraIcon),
                                                        onPressed: () {
                                                          pickImage();
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      16.ph,
                                      if (kIsWeb)
                                        InkWell(
                                          onTap: () {
                                            pickImage();
                                          },
                                          child: TextTitle18And14(
                                            AppStrings.editImage,
                                            style: theme.textTheme.titleMedium!
                                                .copyWith(
                                              color: colorScheme.secondary,
                                              decoration:
                                                  TextDecoration.underline,
                                              decorationColor:
                                                  colorScheme.secondary,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 16.ph
                                      : 40.ph,
                                  !Responsive.isDesktop(context)
                                      ? Column(
                                          children: [
                                            AppTextFormField(
                                              controller: _fNameController,
                                              titleText: AppStrings.fistName,
                                              hintText:
                                                  AppStrings.enterFirstName,
                                              validator: (str) {
                                                return Validator
                                                    .firstNameValidator(
                                                        str!.toLowerCase());
                                              },
                                              inputFormatters: [
                                                LengthLimitingTextInputFormatter(
                                                    50),
                                                FilteringTextInputFormatter
                                                    .allow(
                                                        RegExp(r'[a-zA-Z\s]'))
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 16.ph
                                                : 24.ph,
                                            AppTextFormField(
                                              controller: _lNameController,
                                              titleText: AppStrings.lastName,
                                              hintText:
                                                  AppStrings.enterLastName,
                                              validator: (str) {
                                                return Validator
                                                    .lastNameValidator(
                                                        str!.toLowerCase());
                                              },
                                              inputFormatters: [
                                                LengthLimitingTextInputFormatter(
                                                    50),
                                                FilteringTextInputFormatter
                                                    .allow(
                                                        RegExp(r'[a-zA-Z\s]')),
                                              ],
                                            ),
                                          ],
                                        )
                                      : Row(
                                          children: [
                                            Expanded(
                                              child: AppTextFormField(
                                                controller: _fNameController,
                                                titleText: AppStrings.fistName,
                                                hintText:
                                                    AppStrings.enterFirstName,
                                                validator: (str) {
                                                  return Validator
                                                      .firstNameValidator(
                                                          str!.toLowerCase());
                                                },
                                                inputFormatters: [
                                                  LengthLimitingTextInputFormatter(
                                                      50),
                                                  FilteringTextInputFormatter
                                                      .allow(
                                                          RegExp(r'[a-zA-Z\s]'))
                                                ],
                                              ),
                                            ),
                                            24.pw,
                                            Expanded(
                                              child: AppTextFormField(
                                                controller: _lNameController,
                                                titleText: AppStrings.lastName,
                                                hintText:
                                                    AppStrings.enterLastName,
                                                validator: (str) {
                                                  return Validator
                                                      .lastNameValidator(
                                                          str!.toLowerCase());
                                                },
                                                inputFormatters: [
                                                  LengthLimitingTextInputFormatter(
                                                      50),
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'[a-zA-Z\s]')),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                  !Responsive.isDesktop(context)
                                      ? 16.ph
                                      : 24.ph,
                                  !Responsive.isDesktop(context)
                                      ? Column(
                                          children: [
                                            EmailTextField(
                                              controller: _emailController,
                                              readonly:
                                                  state.userDataModel.email !=
                                                          null ||
                                                      state.userDataModel.email!
                                                          .isNotEmpty,
                                              filled:
                                                  state.userDataModel.email !=
                                                          null ||
                                                      state.userDataModel.email!
                                                          .isNotEmpty,
                                              fillColor: state.userDataModel
                                                              .email !=
                                                          null ||
                                                      state.userDataModel.email!
                                                          .isNotEmpty
                                                  ? colorScheme.lightGreyD9D9D9
                                                  : colorScheme.white,
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 16.ph
                                                : 24.ph,
                                            Form(
                                              key: _formKeyPhone,
                                              child: BlocListener<ResendOtpBloc,
                                                  ResendOtpState>(
                                                listener:
                                                    (context, resendOtpState) {
                                                  if (resendOtpState
                                                      is ResendOtpLoadingState) {
                                                    Dialogs.showOnlyLoader(
                                                        context);
                                                  }
                                                  if (resendOtpState
                                                      is ResendOtpSuccessState) {
                                                    context.pop();
                                                    showDialog(
                                                      barrierDismissible: false,
                                                      context: context,
                                                      builder: (_) {
                                                        return MultiBlocProvider(
                                                          providers: [
                                                            BlocProvider(
                                                              create: (context) =>
                                                                  ResendOtpBloc(),
                                                            ),
                                                            BlocProvider(
                                                              create: (context) =>
                                                                  VerifyOtpBloc(),
                                                            ),
                                                          ],
                                                          child: OtpDialog(
                                                            phoneNumber:
                                                                _phoneNoController
                                                                    .text, // Provide phone number if available
                                                          ),
                                                        );
                                                      },
                                                    ).then(
                                                      (value) {
                                                        isVerified = value;
                                                        setState(() {});
                                                      },
                                                    );
                                                  }
                                                  if (resendOtpState
                                                      is ResendOtpErrorState) {
                                                    context.pop();
                                                    CustomToast.show(
                                                        context: context,
                                                        message: resendOtpState
                                                            .errorMsg,
                                                        isSuccess: true);
                                                  }
                                                },
                                                child: MobileTextfield(
                                                  onChangedValue: (v) {
                                                    if ((state.userDataModel
                                                            .phoneNumber !=
                                                        v)) {
                                                      isVerified = false;
                                                      setState(() {});
                                                    } else {
                                                      isVerified = true;
                                                      setState(() {});
                                                    }
                                                  },
                                                  suffixIcon: isVerified
                                                      ? const SizedBox.shrink()
                                                      : InkWell(
                                                          onTap: () {
                                                            if (!isVerified) {
                                                              if (!_formKeyPhone
                                                                  .currentState!
                                                                  .validate()) {
                                                                return;
                                                              }
                                                              context.read<ResendOtpBloc>().resendOtp(
                                                                  phoneNumber: _phoneNoController
                                                                              .text !=
                                                                          "ND"
                                                                      ? _phoneNoController
                                                                          .text
                                                                      : null,
                                                                  email: state.userDataModel
                                                                              .email !=
                                                                          "ND"
                                                                      ? state
                                                                          .userDataModel
                                                                          .email
                                                                      : null);
                                                            }
                                                          },
                                                          child: Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                                    left: 8.w,
                                                                    right: 8.w,
                                                                    top: 13.h),
                                                            child:
                                                                TextTitle18And14(
                                                              isVerified
                                                                  ? ''
                                                                  : AppStrings
                                                                      .verify,
                                                              color: colorScheme
                                                                  .hyperlinkBlueColor,
                                                            ),
                                                          )),
                                                  controller:
                                                      _phoneNoController,
                                                  onCountryChanged: (country) {
                                                    countryAbbr = country.code;
                                                    countryCode =
                                                        country.dialCode;
                                                    countryName = country.name;
                                                    setState(() {});
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        )
                                      : Row(
                                          children: [
                                            Expanded(
                                              child: Form(
                                                key: _formKeyPhone,
                                                child: MobileTextfield(
                                                  onChangedValue: (v) {
                                                    if ((state.userDataModel
                                                            .phoneNumber !=
                                                        v)) {
                                                      isVerified = false;
                                                      setState(() {});
                                                    } else {
                                                      isVerified = true;
                                                      setState(() {});
                                                    }
                                                  },
                                                  suffixIcon: BlocListener<
                                                      ResendOtpBloc,
                                                      ResendOtpState>(
                                                    listener:
                                                        (context, states) {
                                                      if (states
                                                          is ResendOtpLoadingState) {
                                                        Dialogs.showOnlyLoader(
                                                            context);
                                                      }
                                                      if (states
                                                          is ResendOtpSuccessState) {
                                                        context.pop();
                                                        showDialog(
                                                          context: context,
                                                          barrierDismissible:
                                                              false,
                                                          builder: (_) {
                                                            return MultiBlocProvider(
                                                              providers: [
                                                                BlocProvider(
                                                                  create: (context) =>
                                                                      ResendOtpBloc(),
                                                                ),
                                                                BlocProvider(
                                                                  create: (context) =>
                                                                      VerifyOtpBloc(),
                                                                ),
                                                              ],
                                                              child: OtpDialog(
                                                                phoneNumber:
                                                                    _phoneNoController
                                                                        .text,
                                                              ),
                                                            );
                                                          },
                                                        ).then(
                                                          (value) {
                                                            isVerified = value;
                                                            setState(() {});
                                                          },
                                                        );
                                                      }
                                                      if (states
                                                          is ResendOtpErrorState) {
                                                        context.pop();
                                                        CustomToast.show(
                                                            context: context,
                                                            message:
                                                                states.errorMsg,
                                                            isSuccess: true);
                                                      }
                                                    },
                                                    child: isVerified
                                                        ? const SizedBox
                                                            .shrink()
                                                        : InkWell(
                                                            onTap: () {
                                                              if (!isVerified) {
                                                                if (!_formKeyPhone
                                                                    .currentState!
                                                                    .validate()) {
                                                                  return;
                                                                }
                                                                context.read<ResendOtpBloc>().resendOtp(
                                                                    phoneNumber: _phoneNoController.text !=
                                                                            "ND"
                                                                        ? _phoneNoController
                                                                            .text
                                                                        : null,
                                                                    email: state.userDataModel.email !=
                                                                            "ND"
                                                                        ? state
                                                                            .userDataModel
                                                                            .email
                                                                        : null);
                                                              }
                                                            },
                                                            child: Padding(
                                                              padding: EdgeInsets
                                                                  .only(
                                                                      left: 8.w,
                                                                      right:
                                                                          8.w,
                                                                      top: 8.h),
                                                              child:
                                                                  TextTitle18And14(
                                                                isVerified
                                                                    ? ''
                                                                    : AppStrings
                                                                        .verify,
                                                                color: colorScheme
                                                                    .hyperlinkBlueColor,
                                                              ),
                                                            )),
                                                  ),
                                                  controller:
                                                      _phoneNoController,
                                                  initialCountrySelection:
                                                      countryAbbr,
                                                  onCountryChanged: (country) {
                                                    countryAbbr = country.code;
                                                    countryCode =
                                                        country.dialCode;
                                                    countryName = country.name;
                                                    setState(() {});
                                                  },
                                                ),
                                              ),
                                            ),
                                            24.pw,
                                            Expanded(
                                              child: EmailTextField(
                                                controller: _emailController,
                                                readonly:
                                                    state.userDataModel.email !=
                                                            null ||
                                                        state.userDataModel
                                                            .email!.isNotEmpty,
                                                filled:
                                                    state.userDataModel.email !=
                                                            null ||
                                                        state.userDataModel
                                                            .email!.isNotEmpty,
                                                fillColor:
                                                    state.userDataModel.email !=
                                                                null ||
                                                            state
                                                                .userDataModel
                                                                .email!
                                                                .isNotEmpty
                                                        ? colorScheme
                                                            .lightGreyD9D9D9
                                                        : colorScheme.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        if (state.userDataModel.role == UserType.ancillaryService) ...[
                                              Responsive.isMobile(context)
                                                  ? 16.ph
                                                  : 24.ph,
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: BlocBuilder<
                                                        StaticDataDropdownBloc,
                                                        StaticDataDropdownState>(
                                                      builder: (context, state) {
                                                        List<DropdownData>? service =
                                                            [];
                                                        if (state
                                                            is StaticDataDropdownSuccessState) {
                                                          service.addAll(state
                                                                  .dropDownResponseModel
                                                                  ?.services ??
                                                              []);
                                                        }
                                                        return CustomMultiselectDropdown(
                                                          isLoading: state
                                                              is StaticDataDropdownLoadingState,
                                                          isError: state
                                                              is StaticDataDropdownErrorState,
                                                          hintText:
                                                              AppStrings.selectServices,
                                                          titleText:
                                                              AppStrings.services,
                                                          items: service,
                                                          selectedValues:
                                                              selectedService,
                                                          onSelectionChanged:
                                                              (newSelection) {
                                                            setState(() {
                                                              selectedService =
                                                                  newSelection;
                                                            });
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  ),
                                        if (!!Responsive.isDesktop(
                                            context)) ...[
                                                    24.pw,
                                                    Expanded(child: SizedBox())
                                                  ],
                                                ],
                                              ),
                                              if (selectedService.isNotEmpty)
                                                16.ph,
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child: ChipsWidget(
                                                  items: selectedService,
                                                  onRemove: (item) {
                                                    selectedService.remove(item);
                                                    setState(() {});
                                                  },
                                                ),
                                              ),
                                            ],
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 44.ph,
                                  MultiBlocListener(
                                    listeners: [
                                      BlocListener<ProfileBloc, ProfileState>(
                                        listener: (context, state) {
                                          if (state is ProfileLoadingState) {
                                            Dialogs.showOnlyLoader(context);
                                          }
                                          if (state is ProfileSuccessState) {
                                            CustomToast.show(
                                                context: context,
                                                message: AppStrings.updated,
                                                isSuccess: true);
                                            context
                                                .read<UserBloc>()
                                                .getUserData();
                                            context.pop();
                                            context.pop();
                                          }
                                          if (state is ProfileErrorState) {
                                            // Show Error Message
                                            context.pop();
                                            CustomToast.show(
                                                context: context,
                                                message: state.errorMsg,
                                                isSuccess: true);
                                          }
                                        },
                                      ),
                                      BlocListener<UploadMediaBloc,
                                          UploadMediaState>(
                                        listener: (context, uploadMediaState) {
                                          if (uploadMediaState is UploadMediaLoadingState) {
                                            Dialogs.showOnlyLoader(context);
                                          }
                                          if (uploadMediaState
                                              is UploadMediaSuccessState) {
                                          final isFirstNameChanged =
                                              _fNameController.text.trim() !=
                                                  state.userDataModel.firstName;
                                          final isLastNameChanged =
                                              _lNameController.text.trim() !=
                                                  state.userDataModel.lastName;
                                          final isPhoneNumberChanged =
                                              _phoneNoController.text.trim() !=
                                                  state.userDataModel
                                                      .phoneNumber;
                                          final isServiceChange = areListsNotEqualById(previousSelectedService,selectedService);
                                          final userInfoRequestModel =
                                              UserInfoRequestModel(
                                            firstName: isFirstNameChanged
                                                ? Validator.formatName(_fNameController.text.trim())
                                                : null,
                                            lastName: isLastNameChanged
                                                ? Validator.formatName(_lNameController.text.trim())
                                                : null,
                                            phoneNumber: isPhoneNumberChanged
                                                ? _phoneNoController.text.trim()
                                                : null,
                                            countryAbbr: isPhoneNumberChanged
                                                ? countryAbbr
                                                : null,
                                            countryCode: isPhoneNumberChanged
                                                ? countryCode
                                                : null,
                                            countryName: isPhoneNumberChanged
                                                ? countryName
                                                : null,
                                                  profilePic: uploadMediaState
                                                      .getPreSignedUrlModel
                                                      ?.presignedUrls
                                                      ?.profilePicture?[0]
                                                      .path,
                                            services: isServiceChange ? selectedService.map((e) => e.id).toList() : null,
                                          );
                                            context
                                                .read<ProfileBloc>()
                                                .updateProfile(
                                                    userInfoRequestModel: userInfoRequestModel,);
                                            context.pop();
                                          }
                                          if (uploadMediaState is UploadMediaErrorState) {
                                            // Show Error Message
                                            CustomToast.show(
                                                context: context,
                                                message: uploadMediaState.errorMsg,
                                                isSuccess: true);
                                          }
                                        },
                                      ),
                                    ],
                                    child: Align(
                                      alignment: Alignment.center,
                                      child: PrimaryButton(
                                        width: !Responsive.isDesktop(context)
                                            ? null
                                            : MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                .6,
                                        onPressed: () {
                                          if (!_formKey.currentState!
                                              .validate()) return;
                                          if (!_formKeyPhone.currentState!
                                              .validate()) return;
                                          final isFirstNameChanged =
                                              _fNameController.text.trim() !=
                                                  state.userDataModel.firstName;
                                          final isLastNameChanged =
                                              _lNameController.text.trim() !=
                                                  state.userDataModel.lastName;
                                          final isPhoneNumberChanged =
                                              _phoneNoController.text.trim() !=
                                                  state.userDataModel
                                                      .phoneNumber;
                                          final isImageChanged =
                                              _userImage != null;
                                          final isServiceChange = areListsNotEqualById(previousSelectedService,selectedService);

                                          if (isPhoneNumberChanged &&
                                              !isVerified) {
                                            CustomToast.show(
                                              context: context,
                                              message:
                                                  AppStrings.verifyMobileNumber,
                                              isSuccess: false,
                                            );
                                            return;
                                          }
                                          if (state.userDataModel.role == UserType.ancillaryService && selectedService.isEmpty) {
                                            CustomToast.show(
                                              context: context,
                                              message: ValidationMsg.plsSelectServices,
                                              isSuccess: false,
                                            );
                                            return;
                                          }
                                          final userInfoRequestModel =
                                              UserInfoRequestModel(
                                            firstName: isFirstNameChanged
                                                ? Validator.formatName(_fNameController.text.trim())
                                                : null,
                                            lastName: isLastNameChanged
                                                ? Validator.formatName(_lNameController.text.trim())
                                                : null,
                                            phoneNumber: isPhoneNumberChanged
                                                ? _phoneNoController.text.trim()
                                                : null,
                                            countryAbbr: isPhoneNumberChanged
                                                ? countryAbbr
                                                : null,
                                            countryCode: isPhoneNumberChanged
                                                ? countryCode
                                                : null,
                                            countryName: isPhoneNumberChanged
                                                ? countryName
                                                : null,
                                            services: isServiceChange ? selectedService.map((e) => e.id).toList() : null,
                                          );

                                          if (isImageChanged) {
                                            context
                                                .read<UploadMediaBloc>()
                                                .getPreSignedUrl(
                                                  contentType:
                                                      "image/${_userImage?.ext}",
                                                  preSignedUrlReqModel:
                                                      PreSignedUrlReqModel(
                                                    profilePicture: [
                                                      MediaDetails(
                                                        extn: _userImage?.ext,
                                                        fileName:
                                                            _userImage?.name,
                                                        fileType: "image",
                                                      )
                                                    ],
                                                  ),
                                                  profilePhoto:
                                                      _userImage?.bytes,
                                                );
                                          }

                                        else if (isFirstNameChanged ||
                                              isLastNameChanged ||
                                              isPhoneNumberChanged|| isServiceChange) {
                                            context
                                                .read<ProfileBloc>()
                                                .updateProfile(
                                                    userInfoRequestModel:
                                                        userInfoRequestModel);
                                          } else{
                                            CustomToast.show(
                                              context: context,
                                              message: AppStrings.noChangesMade,
                                              isSuccess: false,
                                            );
                                          }
                                        },
                                        buttonText: AppStrings.save,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }
}
