import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';

enum ArtistListType {
  allArtists(AppStrings.voice, UserType.client),
  ancillary(AppStrings.ancillaryServices, UserType.client),
  allAncillaryArtists(AppStrings.voice, UserType.ancillaryService),
  ancillaryArtists(AppStrings.ancillaryServices, UserType.ancillaryService),
  allAncillaryForVoice(AppStrings.ancillaryServices, UserType.voice);

  final String _name;
  final UserType _userType;

  const ArtistListType(this._name, this._userType);

  @override
  String toString() => _name;

  static const Map<String, ArtistListType> _stringToTypeMap = {
    AppStrings.voice: ArtistListType.allArtists,
    // AppStrings.voices: ArtistListType.allAncillaryArtists,
    // AppStrings.ancillariesServices: ArtistListType.ancillary,
    // AppStrings.previouslyHiredVoices: ArtistListType.hiredArtists,
  };

  static ArtistListType? getArtistListTypeFromString(String type) {
    return _stringToTypeMap[type];
  }

  static List<ArtistListType> getByUserType(UserType userType) {
    return ArtistListType.values
        .where((artistType) => artistType._userType == userType)
        .toList();
  }
}
