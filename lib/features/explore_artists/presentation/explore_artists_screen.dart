import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/explore_artists/enums/artist_list_type.dart';
import 'package:the_voice_directory_flutter/features/google_address/presentation.dart/address_input_field.dart';

import '../../../core/api/api_params.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/common_app_bar.dart';
import '../../../widgets/loading_dialog.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import '../../prefered_project_type/bloc/static_data_dropdown_state.dart';
import '../bloc/explore_artists_bloc.dart';
import '../widgets/artist_tile.dart';
import '../widgets/empty_voice_result.dart';
import '../widgets/filter_widget.dart';

class ExploreArtistsScreen extends StatefulWidget {
  const ExploreArtistsScreen({super.key});

  @override
  State<ExploreArtistsScreen> createState() => _ExploreArtistsScreenState();
}

class _ExploreArtistsScreenState extends State<ExploreArtistsScreen> {
  late final TextEditingController searchController;
  late final TextEditingController addressController;
  Timer? _debounceTimer;
  bool _hasSearched = false;
  ArtistListType _selectedFilter = ArtistListType.allArtists;
  bool isFilterApplied = false;
  bool _wasAddressNotEmpty = false;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    addressController = TextEditingController();

    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    if (userData?.role == UserType.ancillaryService) {
      _selectedFilter = ArtistListType.allAncillaryArtists;
    } else if (userData?.role == UserType.voice) {
      _selectedFilter = ArtistListType.allAncillaryForVoice;
    }

    context.read<ExploreArtistsBloc>().initFetchArtists();

    addressController.addListener(() {
      final isNowEmpty = addressController.text.isEmpty;
      if (_wasAddressNotEmpty && isNowEmpty) {
        if (mounted) {
          context.read<ExploreArtistsBloc>().updateLocationQuery(null);
        }
      }
      _wasAddressNotEmpty = !isNowEmpty;
    });
  }

  void onSearchChanges(BuildContext context, {bool forceSearch = false}) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer?.cancel();

    if (searchController.text.length < 2 && !forceSearch) {
      if (searchController.text.isEmpty && !_hasSearched) return;
      if (searchController.text.isNotEmpty) return;
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (forceSearch) FocusManager.instance.primaryFocus?.unfocus();
      context.read<ExploreArtistsBloc>().updateSearchQuery(searchController.text);
      _hasSearched = searchController.text.length >= 2 || forceSearch;
    });
  }

  Widget _buildVoiceFilterButton({
    required String text,
    required ArtistListType filter,
    required ColorScheme colorScheme,
  }) {
    final bloc = context.read<ExploreArtistsBloc>();
    bool isSelected = _selectedFilter == filter;

    return Container(
      height: 45.h,
      decoration: BoxDecoration(
        color: isSelected ? colorScheme.hyperlinkBlueColor : Colors.transparent,
        borderRadius: BorderRadius.circular(32.r),
        border: Border.all(
          color: isSelected ? colorScheme.hyperlinkBlueColor : colorScheme.lightGreyD9D9D9,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          onTap: () {
            // Handle tab switching for different user types
            bloc.resetFilters();
            // This is handled in changeSelectedTab now
            // context.read<ExploreArtistsBloc>().changeFavoriteQuery();
            if (filter == ArtistListType.ancillaryArtists || filter == ArtistListType.ancillary) {
              bloc.changeSelectedTab('ancillary');
            } else if (filter == ArtistListType.allAncillaryArtists || filter == ArtistListType.allArtists) {
              bloc.changeSelectedTab('voice');
            } else if (filter == ArtistListType.allAncillaryForVoice) {
              bloc.changeSelectedTab('ancillary');
            }

            if (_selectedFilter != filter) {
              setState(() {
                _selectedFilter = filter;
              });
            }
          },
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: TextTitle18And14(
                text,
                color: isSelected ? colorScheme.white : colorScheme.primaryGrey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    final List<ArtistListType> filters = ArtistListType.getByUserType(userData?.role ?? UserType.client);
    bool isFilterApplied = context.watch<ExploreArtistsBloc>().filters.isAnyFilterApplied;
    bool isFavoriteQuery = context.watch<ExploreArtistsBloc>().queryFavorite;
    return BlocProvider(
      create: (context) => StaticDataDropdownBloc(isFlexible: false),
      child: Scaffold(
        appBar: Responsive.isDesktop(context)
            ? null
            : const CommonAppBar(title: AppStrings.explore),
        body: BlocConsumer<StaticDataDropdownBloc, StaticDataDropdownState>(
          listener: (context, state) {
            if (state is StaticDataDropdownErrorState) {
              if (state is ExploreArtistsErrorState) {
                CustomToast.show(context: context, message: state.errorMsg);
              }
            }
          },
          builder: (context, dropDownState) {
            if (dropDownState is StaticDataDropdownLoadingState) {
              return const Center(
                child: Loader(),
              );
            }
            return BlocConsumer<ExploreArtistsBloc, ExploreArtistsState>(
              listener: (context, state) {
                if (state is ExploreArtistsErrorState) {
                  CustomToast.show(context: context, message: state.errorMsg);
                }
              },
              builder: (context, state) {
                if (state is ExploreArtistsInitialState) {
                  return Card(
                    color: colorScheme.white,
                    elevation: Responsive.isDesktop(context) ? 8 : 0,
                    margin: Responsive.isDesktop(context)
                        ? EdgeInsets.only(left: 120.w, right: 120.w, top: 28.h, bottom: 56.h)
                        : EdgeInsets.zero,
                    child: const Center(
                      child: Loader(),
                    ),
                  );
                }
                return Theme(
                  data: theme.copyWith(
                    scrollbarTheme: ScrollbarThemeData(
                      thickness: WidgetStatePropertyAll(10.h),
                      thumbVisibility: WidgetStatePropertyAll(
                        Responsive.isDesktop(context),
                      ),
                      mainAxisMargin: 20,
                      thumbColor: WidgetStatePropertyAll(
                        Responsive.isDesktop(context)
                            ? colorScheme.lightGreyB2B2B2
                            : Colors.transparent,
                      ),
                    ),
                  ),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints.expand(),
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: Responsive.isDesktop(context) ? 80.w : 16.w,
                        right: Responsive.isDesktop(context) ? 80.w : 16.w,
                        top: Responsive.isDesktop(context) ? 40.h : 4.h,
                        bottom: Responsive.isDesktop(context) ? 32.h : 0,
                      ),
                      child: Column(
                        children: [
                          if (Responsive.isDesktop(context)) ...[
                            Row(
                              children: [
                                Expanded(
                                  flex: 10,
                                  child: TextDisplayLarge36And26(
                                    AppStrings.explore,
                                    color: colorScheme.primaryGrey,
                                  ),
                                ),
                                Expanded(
                                  flex: 5,
                                  child: AddressInputField(
                                    addressController: addressController,
                                    prefixIcon: Padding(
                                      padding: EdgeInsets.all(14.h),
                                      child: SvgPicture.asset(AppImages.locationIc),
                                    ),
                                    maxLines: 1,
                                    isTitle: false,
                                    hintText: AppStrings.enterLocation,
                                    hintTextColor: colorScheme.darkGrey525252,
                                    showFullAddress: true,
                                    onLocationSelected: (locationData) {
                                      context.read<ExploreArtistsBloc>().updateLocationQuery(locationData);
                                    },
                                    suffixIcon: addressController.text.isNotEmpty
                                        ? GestureDetector(
                                            onTap: () {
                                              addressController.clear();
                                              setState(() {});
                                              context.read<ExploreArtistsBloc>().updateLocationQuery(null);
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.all(8.0.h),
                                              child: SvgPicture.asset(AppImages.closeSquareIc),
                                            ),
                                          )
                                        : null,
                                    onChanged: (text) {
                                      setState(() {});
                                    },
                                  ),
                                ),
                                16.pw,
                                Expanded(
                                  flex: 5,
                                  child: AppTextFormField(
                                    maxLength: 100,
                                    maxLines: 1,
                                    controller: searchController,
                                    hintText: AppStrings.searchArtistsHintText,
                                    hintTextFontWeight: FontWeight.w500,
                                    hintTextColor: colorScheme.darkGrey525252,
                                    prefixIcon: Padding(
                                      padding: EdgeInsets.all(14.h),
                                      child: SvgPicture.asset(AppImages.searchIc),
                                    ),
                                    onChanged: (_) {
                                      setState(() {});
                                      onSearchChanges(context);
                                    },
                                    suffixIcon: searchController.text.isNotEmpty
                                        ? GestureDetector(
                                            onTap: () {
                                              searchController.clear();
                                              setState(() {});
                                              onSearchChanges(context, forceSearch: true);
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.all(8.0.h),
                                              child: SvgPicture.asset(AppImages.closeSquareIc),
                                            ),
                                          )
                                        : null,
                                  ),
                                ),
                              ],
                            )
                          ],
                          20.ph,
                          Row(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: filters.map((filter) => _buildVoiceFilterButton(
                                          text: filter.toString(),
                                          filter: filter,
                                          colorScheme: colorScheme,
                                        ))
                                    .expand((widget) => [widget, 16.pw])
                                    .toList()
                                  ..removeLast(),
                              ),
                            ],
                          ),
                          // !Responsive.isDesktop(context) ? 0.ph : 10.ph,
                          if (!Responsive.isDesktop(context)) ...[
                            24.ph,
                            AddressInputField(
                              addressController: addressController,
                              prefixIcon:  Padding(
                              padding: EdgeInsets.all(14.h),
                              child: SvgPicture.asset(AppImages.locationIc),
                              ),
                              maxLines: 1,
                              isTitle: false,
                              hintText: AppStrings.enterLocation,
                              hintTextColor: colorScheme.darkGrey525252,
                              showFullAddress: true,
                              onLocationSelected: (locationData) {
                              context.read<ExploreArtistsBloc>().updateLocationQuery(locationData);
                                  },
                            suffixIcon: addressController.text.isNotEmpty
                                  ? GestureDetector(
                                      onTap: () {
                                        addressController.clear();
                                        setState(() {});
                                        context.read<ExploreArtistsBloc>().updateLocationQuery(null);
                                      },
                                      child: Padding(
                                        padding: EdgeInsets.all(8.0.h),
                                        child: SvgPicture.asset(AppImages.closeSquareIc),
                                      ),
                                    )
                                  : null,
                                  onChanged: (text) {
                                    setState(() {});
                                  }, 
                            ),
                            24.ph,
                            Row(
                              children: [
                                Expanded(
                                  child: AppTextFormField(
                                    maxLength: 100,
                                    maxLines: 1,
                                    controller: searchController,
                                    hintText: AppStrings.searchArtistsHintText,
                                    hintTextFontWeight: FontWeight.w500,
                                    hintTextColor: colorScheme.darkGrey525252,
                                    prefixIcon: Padding(
                                      padding: EdgeInsets.all(14.h),
                                      child: SvgPicture.asset(AppImages.searchIc),
                                    ),
                                    onChanged: (_) {
                                      setState(() {});
                                      onSearchChanges(context);
                                    },
                                    suffixIcon: searchController.text.isNotEmpty
                                        ? GestureDetector(
                                            onTap: () {
                                              searchController.clear();
                                              setState(() {});
                                              onSearchChanges(context, forceSearch: true);
                                            },
                                            child: Padding(
                                              padding: EdgeInsets.all(8.0.h),
                                              child: SvgPicture.asset(AppImages.closeSquareIc),
                                            ),
                                          )
                                        : null,
                                  ),
                                ),
                                12.pw,
                                InkWell(
                                  onTap: () async {
                                    if (dropDownState is StaticDataDropdownSuccessState &&
                                        dropDownState.dropDownResponseModel != null) {
                                      await showModalBottomSheet(
                                        context: context,
                                        backgroundColor: Colors.transparent,
                                        isScrollControlled: true,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                            top: Radius.circular(20.r),
                                          ),
                                        ),
                                        builder: (_) {
                                          if (_selectedFilter == ArtistListType.ancillaryArtists || _selectedFilter == ArtistListType.allAncillaryForVoice || _selectedFilter == ArtistListType.ancillary) {
                                            return ClipRRect(
                                              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                              child: BackdropFilter(
                                                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                                child: BlocProvider.value(
                                                  value: BlocProvider.of<ExploreArtistsBloc>(context),
                                                  child: FilterWidget(
                                                    dropDownResponseModel: dropDownState.dropDownResponseModel!,
                                                    selectedTab: _selectedFilter,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }
                                          return ClipRRect(
                                            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                            child: BackdropFilter(
                                              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                                              child: DraggableScrollableSheet(
                                                initialChildSize: 0.5,
                                                minChildSize: 0.4,
                                                maxChildSize: 0.75,
                                                expand: false,
                                                builder: (_, scrollController) {
                                                  return BlocProvider.value(
                                                    value: BlocProvider.of<ExploreArtistsBloc>(context),
                                                    child: FilterWidget(
                                                      dropDownResponseModel: dropDownState.dropDownResponseModel!,
                                                      scrollController: scrollController,
                                                      selectedTab: _selectedFilter,
                                                    ),
                                                  );
                                                }
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    }
                                  },
                                  borderRadius: BorderRadius.circular(24.h),
                                  child: Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      Container(
                                        width: 44.h,
                                        height: 44.h,
                                        decoration: BoxDecoration(
                                          color: colorScheme.white,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: colorScheme.lightGreyD9D9D9,
                                          ),
                                        ),
                                        child: Center(
                                          child: SvgPicture.asset(
                                            AppImages.filtersIc,
                                          ),
                                        ),
                                      ),
                                      if (isFilterApplied || isFavoriteQuery)
                                        Positioned(
                                          top: 3,
                                          right: 2,
                                          child: Container(
                                            width: 10,
                                            height: 10,
                                            decoration: BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle,
                                              border: Border.all(color: Colors.white, width: 1.5),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            )
                          ],
                          20.ph,
                          Builder(builder: (context) {
                            return Expanded(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Left filters panel - always visible
                                  if (Responsive.isDesktop(context) &&
                                      dropDownState is StaticDataDropdownSuccessState &&
                                      dropDownState.dropDownResponseModel != null)
                                    FilterWidget(
                                      dropDownResponseModel: dropDownState.dropDownResponseModel!,
                                      selectedTab: _selectedFilter,
                                    ),
                                  
                                  // Main content area
                                  Expanded(
                                    child: state is ExploreArtistsLoadingState 
                                        ? Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Column(
                                                children: [
                                                  Responsive.isDesktop(context) ? 220.ph : 120.ph,
                                                  const Loader(),
                                                ],
                                              ),
                                            ],
                                          )
                                        : state is ExploreArtistsSuccessState
                                            ? state.artistsList.isEmpty
                                                ? Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: const [EmptyVoiceResult()],
                                                  )
                                                : GridView.builder(
                                                    padding: Responsive.isDesktop(context)
                                                        ? EdgeInsets.symmetric(horizontal: 24.w, vertical: 3.h)
                                                        : EdgeInsets.only(bottom: 20.h),
                                                    physics: const BouncingScrollPhysics(),
                                                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                                      mainAxisExtent: Responsive.isDesktop(context) ? 84.h : 68.h,
                                                      crossAxisCount: Responsive.isDesktop(context) ? 2 : 1,
                                                      mainAxisSpacing: Responsive.isDesktop(context) ? 24.h : 16.h,
                                                      crossAxisSpacing: Responsive.isDesktop(context) ? 25.w : 0,
                                                    ),
                                                    itemCount: state.artistsList.length,
                                                    itemBuilder: (context, index) {
                                                      return ArtistTile(
                                                        user: state.artistsList[index],
                                                        onTap: () {
                                                          NavigationServiceImpl.getInstance()?.doNavigation(
                                                            context,
                                                            routeName: RouteName.profile,
                                                            pathParameters: {
                                                              Params.id: state.artistsList[index].id?.toString() ?? "0",
                                                            },
                                                          );
                                                        },
                                                      );
                                                    },
                                                  )
                                            : const SizedBox.shrink(),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    searchController.dispose();
    addressController.dispose();
    super.dispose();
  }
}
