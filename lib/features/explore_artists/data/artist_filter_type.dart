import '../../../core/api/api_params.dart';
import '../../../utils/string_constants/app_strings.dart';

enum ArtistFilterType {
  gender(AppStrings.gender_, Params.gender),
  projectType(AppStrings.preferredProjectType, Params.projectType),
  ageRange(AppStrings.voiceAgeWithoutAsterisk, Params.ageRange),
  language(AppStrings.languageWithoutAsterisk, Params.language),
  accent(AppStrings.accentWithoutAsterisk, Params.accent),
  experience(AppStrings.experienceWithoutAsterisk, Params.experience),
  services(AppStrings.servicesWithoutAsterisk, Params.services);

  final String _name;
  final String apiParamName;

  const ArtistFilterType(this._name, this.apiParamName);

  @override
  String toString() => _name;
}
