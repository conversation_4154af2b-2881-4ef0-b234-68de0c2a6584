class ArtistFilterSelection {
  List<int>? genderIds;
  List<int>? projectTypeIds;
  List<int>? ageRangeIds;
  List<int>? languageIds;
  List<int>? accentIds;
  List<int>? experienceIds;
  List<int>? servicesIds;

  ArtistFilterSelection({
    this.genderIds,
    this.projectTypeIds,
    this.ageRangeIds,
    this.languageIds,
    this.accentIds,
    this.experienceIds,
    this.servicesIds,
  });

  ArtistFilterSelection copyWith({
    List<int>? genderIds,
    List<int>? projectTypeIds,
    List<int>? ageRangeIds,
    List<int>? languageIds,
    List<int>? accentIds,
    List<int>? experienceIds,
    List<int>? servicesIds,
  }) {
    return ArtistFilterSelection(
      genderIds: genderIds ?? this.genderIds,
      projectTypeIds: projectTypeIds ?? this.projectTypeIds,
      ageRangeIds: ageRangeIds ?? this.ageRangeIds,
      languageIds: languageIds ?? this.languageIds,
      accentIds: accentIds ?? this.accentIds,
      experienceIds: experienceIds ?? this.experienceIds,
      servicesIds: servicesIds ?? this.servicesIds,
    );
  }

  bool get isAnyFilterApplied {
    return (genderIds != null && genderIds!.isNotEmpty) ||
        (projectTypeIds != null && projectTypeIds!.isNotEmpty) ||
        (ageRangeIds != null && ageRangeIds!.isNotEmpty) ||
        (languageIds != null && languageIds!.isNotEmpty) ||
        (accentIds != null && accentIds!.isNotEmpty) ||
        (experienceIds != null && experienceIds!.isNotEmpty) ||
        (servicesIds != null && servicesIds!.isNotEmpty);
  }
}
