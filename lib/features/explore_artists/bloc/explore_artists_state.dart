part of 'explore_artists_bloc.dart';

sealed class ExploreArtistsState extends Equatable {
  const ExploreArtistsState();
}

class ExploreArtistsInitialState extends ExploreArtistsState {
  @override
  List<Object> get props => [];
}

class ExploreArtistsLoadingState extends ExploreArtistsState {
  @override
  List<Object> get props => [];
}

class ExploreArtistsSuccessState extends ExploreArtistsState {
  final List<UserDataModel> artistsList;

  const ExploreArtistsSuccessState({
    required this.artistsList,
  });

  @override
  List<Object> get props => [artistsList];
}

class ExploreArtistsErrorState extends ExploreArtistsState {
  final String errorMsg;
  const ExploreArtistsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
