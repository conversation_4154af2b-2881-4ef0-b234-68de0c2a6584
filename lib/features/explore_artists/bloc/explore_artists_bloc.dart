import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../data/artist_filter_selection.dart';
import '../data/artist_filter_type.dart';

part 'explore_artists_state.dart';

class ExploreArtistsBloc extends Cubit<ExploreArtistsState> {
  @override
  ExploreArtistsBloc() : super(ExploreArtistsInitialState());

  ArtistFilterSelection filters = ArtistFilterSelection();
  String? searchQuery;
  Map<String, dynamic>? locationQuery;
  bool queryFavorite = false;
  String selectedTab = _getDefaultSelectedTab();

  static String _getDefaultSelectedTab() {
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    return userData?.role == UserType.voice ? 'ancillary' : 'voice';
  }

  void updateFilter(ArtistFilterType type, int selectedId) {
    switch (type) {
      case ArtistFilterType.gender:
        filters = filters.copyWith(genderIds: _updateList(filters.genderIds, selectedId));
        break;
      case ArtistFilterType.projectType:
        filters = filters.copyWith(projectTypeIds: _updateList(filters.projectTypeIds, selectedId));
        break;
      case ArtistFilterType.ageRange:
        filters = filters.copyWith(ageRangeIds: _updateList(filters.ageRangeIds, selectedId));
        break;
      case ArtistFilterType.language:
        filters = filters.copyWith(languageIds: _updateList(filters.languageIds, selectedId));
        break;
      case ArtistFilterType.accent:
        filters = filters.copyWith(accentIds: _updateList(filters.accentIds, selectedId));
        break;
      case ArtistFilterType.experience:
        filters = filters.copyWith(experienceIds: _updateList(filters.experienceIds, selectedId));
        break;
      case ArtistFilterType.services:
        filters = filters.copyWith(servicesIds: _updateList(filters.servicesIds, selectedId));
        break;
    }
    fetchArtists();
  }

  List<int> _updateList(List<int>? currentList, int id) {
    List<int> newList = List.from(currentList ?? []);
    if (newList.contains(id)) {
      newList.remove(id);
    } else {
      newList.add(id);
    }
    return newList;
  }

  void updateSearchQuery(String? query) {
    searchQuery = query;
    fetchArtists();
  }

  void updateLocationQuery(Map<String, dynamic>? query) {
    locationQuery = query;
    fetchArtists();
  }

  void resetFilters() {
    if (filters.isAnyFilterApplied || queryFavorite) {
      filters = ArtistFilterSelection();
      changeFavoriteQuery(queryFavorite: false);
      fetchArtists();
    }
  }

  void initFetchArtists({bool queryFavorite = false}) {
    searchQuery = null;
    locationQuery = null;
    filters = ArtistFilterSelection();
    this.queryFavorite = queryFavorite;
    selectedTab = _getDefaultSelectedTab();
    fetchArtists();
  }

  void changeFavoriteQuery({bool queryFavorite = false}) {
    this.queryFavorite = queryFavorite;
    fetchArtists();
  }

  void changeSelectedTab(String selectedTab, {bool queryFavorite = false}) {
    this.selectedTab = selectedTab;
    this.queryFavorite = queryFavorite;
    fetchArtists();
  }

  Future<void> fetchArtists() async {
    if (state is! ExploreArtistsInitialState) {
      emit(ExploreArtistsLoadingState());
    }

    try {
      final response = await ApiService.instance.getArtists(
        searchName: searchQuery,
        location: locationQuery,
        filters: filters,
        isFavorite: queryFavorite,
        selectedTab: selectedTab,
      );

      if (response.success) {
        final List<UserDataModel> artistsList = (response.data as List)
            .map((e) => UserDataModel.fromJson(e))
            .toList();

        emit(ExploreArtistsSuccessState(artistsList: artistsList));
      } else {
        emit(ExploreArtistsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ExploreArtistsErrorState(AppStrings.genericErrorMsg));
    }
  }
}
