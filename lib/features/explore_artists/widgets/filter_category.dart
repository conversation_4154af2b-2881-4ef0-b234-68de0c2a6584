import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/explore_artists_bloc.dart';
import '../data/artist_filter_category_model.dart';
import '../data/artist_filter_type.dart';
import '../enums/artist_list_type.dart';

class FilterCategory extends StatefulWidget {
  final ArtistFilterCategoryModel category;
  final ArtistListType? selectedTab;

  const FilterCategory({
    super.key,
    required this.category,
    this.selectedTab,
  });

  @override
  State<FilterCategory> createState() => _FilterCategoryState();
}

class _FilterCategoryState extends State<FilterCategory> {
  Set<int> _currentlySelectedIds = {};

  Set<int> _getInitialSelectedIds() {
    final bloc = context.read<ExploreArtistsBloc>();
    switch (widget.category.filterType) {
      case ArtistFilterType.gender:
        return bloc.filters.genderIds?.toSet() ?? {};
      case ArtistFilterType.projectType:
        return bloc.filters.projectTypeIds?.toSet() ?? {};
      case ArtistFilterType.ageRange:
        return bloc.filters.ageRangeIds?.toSet() ?? {};
      case ArtistFilterType.language:
        return bloc.filters.languageIds?.toSet() ?? {};
      case ArtistFilterType.accent:
        return bloc.filters.accentIds?.toSet() ?? {};
      case ArtistFilterType.experience:
        return bloc.filters.experienceIds?.toSet() ?? {};
      case ArtistFilterType.services:
        return bloc.filters.servicesIds?.toSet() ?? {};
      default:
        return {};
    }
  }

  @override
  Widget build(BuildContext context) {
    if ((widget.selectedTab == ArtistListType.ancillaryArtists || widget.selectedTab == ArtistListType.ancillary || widget.selectedTab == ArtistListType.allAncillaryForVoice) && 
        widget.category.filterType != ArtistFilterType.services) {
      return SizedBox.shrink();
    }

    if (widget.category.items.isEmpty) return SizedBox.shrink();
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    _currentlySelectedIds = _getInitialSelectedIds();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          child: TextTitle18And14(
            widget.category.filterType.toString(),
            fontSize: Responsive.isDesktop(context) ? 18.sp : 16.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        Column(
          children: widget.category.items.asMap().entries.map((entry) {
            final item = entry.value;

            bool isSelected = _currentlySelectedIds.contains(item.id);

            return Theme(
              data: Theme.of(context).copyWith(
                checkboxTheme: CheckboxThemeData(
                  fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colorScheme.checkBoxLemonGreen;
                    }
                    return states.contains(WidgetState.hovered)
                        ? colorScheme.lightGreyB2B2B2.withOpacity(0.1)
                        : Colors.transparent;
                  }),
                  checkColor: WidgetStateProperty.all<Color>(colorScheme.white),
                  side: WidgetStateBorderSide.resolveWith((states) => BorderSide(
                    width: 1.2,
                    color: states.contains(WidgetState.selected)
                        ? colorScheme.checkBoxLemonGreen
                        : colorScheme.lightGreyB2B2B2,
                  )),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 11.w),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 24.h,
                      height: 24.h,
                      child: Checkbox(
                        value: isSelected,
                        onChanged: (bool? newValue) {
                          _handleCheckboxChanged(context, item.id);
                        },
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    12.pw,
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          _handleCheckboxChanged(context, item.id);
                        },
                        child: TextTitle14(item.name ?? '', style: TextStyle(fontSize: 15.sp),),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _handleCheckboxChanged(BuildContext context, int itemId) {
    final bloc = context.read<ExploreArtistsBloc>();

    setState(() {
      if (_currentlySelectedIds.contains(itemId)) {
        _currentlySelectedIds.remove(itemId);
      } else {
        _currentlySelectedIds.add(itemId);
      }
    });
    bloc.updateFilter(widget.category.filterType, itemId);
  }
}
 