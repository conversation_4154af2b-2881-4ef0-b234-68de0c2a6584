import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';

class EmptyVoiceResult extends StatelessWidget {
  final bool isSearchLocationBased;

  const EmptyVoiceResult({
    super.key,
    this.isSearchLocationBased = true,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Responsive.isDesktop(context) ? 175.ph : 104.ph,
          SvgPicture.asset(AppImages.emptySearchResultVoice),
          28.ph,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 50.w),
            child: TextTitle18And14(
              AppStrings.noArtistsFound(isSearchLocationBased: isSearchLocationBased),
              fontWeight: Responsive.isDesktop(context)
                  ? FontWeight.w700
                  : FontWeight.w500,
              fontSize: Responsive.isDesktop(context) ? 18.sp : 16.sp,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
