import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../widgets/common_circle_icon.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/favorite_artist_bloc.dart';
import '../bloc/favorite_artist_state.dart';

class ArtistTile extends StatefulWidget {
  final UserDataModel user;
  final Function()? onTap;

  const ArtistTile({
    super.key,
    required this.user,
    this.onTap,
  });

  @override
  State<ArtistTile> createState() => _ArtistTileState();
}

class _ArtistTileState extends State<ArtistTile> {
  late int userId;
  bool _isFavorite = false;
  bool defaultFavorite = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    userId = widget.user.id ?? -1;
    _isFavorite = defaultFavorite = widget.user.isFavorite ?? false;
  }

  @override
  void didUpdateWidget(ArtistTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.user.isFavorite != widget.user.isFavorite) {
      _isFavorite = widget.user.isFavorite ?? false;
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _handleFavoriteToggle(BuildContext context) {
    _debounceTimer?.cancel();
    if (userId != -1) {
      setState(() {
        _isFavorite = !_isFavorite;
      });

      widget.user.copyWith(isFavorite: _isFavorite);
      
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        if (defaultFavorite == _isFavorite) return;
        context.read<FavoriteArtistBloc>().apiFavoriteArtist( widget.user, _isFavorite);
      });
    }
  }

  Widget _buildFavoriteButton() {
    return BlocConsumer<FavoriteArtistBloc, FavoriteArtistState>(
      listener: (context, state) {
        if (state is FavoriteArtistSuccessState) {
          setState(() {
            defaultFavorite = _isFavorite;
          });
        }
        if (state is FavoriteArtistErrorState) {
          if (state.artistId == userId) {
            setState(() {
              _isFavorite = !_isFavorite;
            });
            
            CustomToast.show(context: context, message: state.errorMsg, isSuccess: false);
          }
        }
      },
      builder: (context, state) {
        final bool isLoading = state is FavoriteArtistLoadingState && state.artistId == userId;
        
        return CommonCircleIcon(
          iconPath: _isFavorite ? AppImages.filledHeartIcon : AppImages.heartIcon,
          onTap: isLoading ? null : () => _handleFavoriteToggle(context),
        );
      },
    );
  }

  Widget _buildProfileImage(String imageUrl, ColorScheme colorScheme, UserDataModel user) {
    // Check if it's a Google image and we're on web
    bool isGoogleImage = imageUrl.contains('googleusercontent.com');
    bool isWeb = kIsWeb;

    // For Google images on web, use a reliable CORS proxy
    String finalImageUrl = imageUrl;
    if (isWeb && isGoogleImage) {
      // Use a public CORS proxy service
      finalImageUrl = 'https://api.codetabs.com/v1/proxy?quest=${Uri.encodeComponent(imageUrl)}';
      print('🌐 Web Google Image - Original: $imageUrl');
      print('🔄 Web Google Image - Proxied: $finalImageUrl');

      // Alternative proxies (uncomment to try different ones):
      // finalImageUrl = 'https://corsproxy.io/?${Uri.encodeComponent(imageUrl)}';
      // finalImageUrl = 'https://cors.bridged.cc/${Uri.encodeComponent(imageUrl)}';
      // finalImageUrl = 'https://proxy.cors.sh/${Uri.encodeComponent(imageUrl)}';
    }

    // Try using CachedNetworkImage which might handle CORS better
    return CachedNetworkImage(
      imageUrl: finalImageUrl,
      imageBuilder: (context, imageProvider) => Container(
        width: 44.h,
        height: 44.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: imageProvider,
            fit: BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) => Container(
        width: 44.h,
        height: 44.h,
        color: colorScheme.lightGreyD9D9D9,
        child: Center(
          child: SizedBox(
            width: 20.h,
            height: 20.h,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: colorScheme.primaryGrey,
            ),
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        // Log the error for debugging
        print('❌ Image Load Error: $error');
        print('🔗 Failed URL: $url');

        // Fallback to user initials if image fails to load
        return Container(
          width: 44.h,
          height: 44.h,
          color: colorScheme.lightGreyD9D9D9,
          child: Center(
            child: TextTitle14(
              (user.firstName ?? '').isNotEmpty ? user.firstName![0] : '',
              color: colorScheme.primaryGrey,
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (context) => FavoriteArtistBloc(),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.white,
          border: Border.all(
            color: colorScheme.lightShadowF3F3F3,
            width: 1.2,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color(0x00101828).withOpacity(0.05),
              blurRadius: 9,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: Responsive.isDesktop(context) ? 20.w : 12.w,
          vertical: Responsive.isDesktop(context) ? 20.h : 12.h,
        ),
        child: InkWell(
           onTap: widget.onTap,
          child: Row(
            children: [
              Container(
                width: 44.h,
                height: 44.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all( color: colorScheme.lightGreyD9D9D9,
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(22),
                  child: widget.user.profilePic != null &&
                          widget.user.profilePic!.isNotEmpty
                      ? _buildProfileImage( Uri.parse(widget.user.profilePic!).isAbsolute ? widget.user.profilePic! : '${EnvironmentConfig.imageBaseUrl}${widget.user.profilePic!}', colorScheme, widget.user)
                      : Container( color: colorScheme.lightGreyD9D9D9,
                          child: Center(
                            child: TextTitle14((widget.user.firstName ?? '').isNotEmpty ? widget.user.firstName![0] : '',
                              color: colorScheme.primaryGrey,
                            ),
                          ),
                        ),
                ),
              ),
              14.pw,
              TextTitle18And14("${widget.user.firstName ?? ''} ${widget.user.lastName ?? ''}",
                color: colorScheme.primaryGrey,
                fontWeight: FontWeight.w700,
              ),
              const Spacer(),
              _buildFavoriteButton(),
            ],
          ),
        ),
      ),
    );
  }
}
