import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../widgets/common_circle_icon.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/favorite_artist_bloc.dart';
import '../bloc/favorite_artist_state.dart';

class ArtistTile extends StatefulWidget {
  final UserDataModel user;
  final Function()? onTap;

  const ArtistTile({
    super.key,
    required this.user,
    this.onTap,
  });

  @override
  State<ArtistTile> createState() => _ArtistTileState();
}

class _ArtistTileState extends State<ArtistTile> {
  late int userId;
  bool _isFavorite = false;
  bool defaultFavorite = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    userId = widget.user.id ?? -1;
    _isFavorite = defaultFavorite = widget.user.isFavorite ?? false;
  }

  @override
  void didUpdateWidget(ArtistTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.user.isFavorite != widget.user.isFavorite) {
      _isFavorite = widget.user.isFavorite ?? false;
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _handleFavoriteToggle(BuildContext context) {
    _debounceTimer?.cancel();
    if (userId != -1) {
      setState(() {
        _isFavorite = !_isFavorite;
      });

      widget.user.copyWith(isFavorite: _isFavorite);
      
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        if (defaultFavorite == _isFavorite) return;
        context.read<FavoriteArtistBloc>().apiFavoriteArtist( widget.user, _isFavorite);
      });
    }
  }

  Widget _buildFavoriteButton() {
    return BlocConsumer<FavoriteArtistBloc, FavoriteArtistState>(
      listener: (context, state) {
        if (state is FavoriteArtistSuccessState) {
          setState(() {
            defaultFavorite = _isFavorite;
          });
        }
        if (state is FavoriteArtistErrorState) {
          if (state.artistId == userId) {
            setState(() {
              _isFavorite = !_isFavorite;
            });
            
            CustomToast.show(context: context, message: state.errorMsg, isSuccess: false);
          }
        }
      },
      builder: (context, state) {
        final bool isLoading = state is FavoriteArtistLoadingState && state.artistId == userId;
        
        return CommonCircleIcon(
          iconPath: _isFavorite ? AppImages.filledHeartIcon : AppImages.heartIcon,
          onTap: isLoading ? null : () => _handleFavoriteToggle(context),
        );
      },
    );
  }

  Widget _buildProfileImage(String imageUrl, ColorScheme colorScheme) {
    // Check if URL is absolute, if not prepend base URL
    final String finalImageUrl = Uri.parse(imageUrl).isAbsolute
        ? imageUrl
        : "${EnvironmentConfig.imageBaseUrl}$imageUrl";

    return Image.network(
      finalImageUrl,
      fit: BoxFit.cover,
      width: 44.h,
      height: 44.h,
      // Add caching and retry mechanism
      cacheWidth: 96, // Optimize for 2x device pixel ratio
      cacheHeight: 96,
      errorBuilder: (context, error, stackTrace) {
        // Return fallback widget for any error (including rate limits)
        return Container(
          color: colorScheme.lightGreyD9D9D9,
          child: Center(
            child: Icon(
              Icons.person,
              color: colorScheme.primaryGrey,
              size: 24.h,
            ),
          ),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: colorScheme.lightGreyD9D9D9,
          child: Center(
            child: SizedBox(
              width: 24.h,
              height: 24.h,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          ),
        );
      },
      // Add headers to potentially avoid rate limiting
      headers: const {
        'Cache-Control': 'max-age=3600',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (context) => FavoriteArtistBloc(),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.white,
          border: Border.all(
            color: colorScheme.lightShadowF3F3F3,
            width: 1.2,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color(0x00101828).withOpacity(0.05),
              blurRadius: 9,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: Responsive.isDesktop(context) ? 20.w : 12.w,
          vertical: Responsive.isDesktop(context) ? 20.h : 12.h,
        ),
        child: InkWell(
           onTap: widget.onTap,
          child: Row(
            children: [
              Container(
                width: 44.h,
                height: 44.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all( color: colorScheme.lightGreyD9D9D9,
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(22),
                  child: widget.user.profilePic != null &&
                          widget.user.profilePic!.isNotEmpty
                      ? _buildProfileImage(widget.user.profilePic!, colorScheme)
                      : Container( color: colorScheme.lightGreyD9D9D9,
                          child: Center(
                            child: TextTitle14((widget.user.firstName ?? '').isNotEmpty ? widget.user.firstName![0] : '',
                              color: colorScheme.primaryGrey,
                            ),
                          ),
                        ),
                ),
              ),
              14.pw,
              TextTitle18And14("${widget.user.firstName ?? ''} ${widget.user.lastName ?? ''}",
                color: colorScheme.primaryGrey,
                fontWeight: FontWeight.w700,
              ),
              const Spacer(),
              _buildFavoriteButton(),
            ],
          ),
        ),
      ),
    );
  }
}
