import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/explore_artists_bloc.dart';
import '../data/artist_filter_category_model.dart';
import '../data/artist_filter_type.dart';
import '../enums/artist_list_type.dart';
import 'filter_category.dart';

class FilterWidget extends StatelessWidget {
  final DropDownResponseModel dropDownResponseModel;
  final ScrollController? scrollController;
  final ArtistListType? selectedTab;

  const FilterWidget({
    super.key,
    required this.dropDownResponseModel,
    this.scrollController,
    this.selectedTab,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    final List<ArtistFilterCategoryModel> filterCategories = [
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.gender,
        items: dropDownResponseModel.gender ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.projectType,
        items: dropDownResponseModel.projectType ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.ageRange,
        items: dropDownResponseModel.ageRange ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.language,
        items: dropDownResponseModel.voiceLanguage ?? [],
      ),
      ArtistFilterCategoryModel(
        filterType: ArtistFilterType.accent,
        items: dropDownResponseModel.accent ?? [],
      ),
      if (selectedTab == ArtistListType.ancillaryArtists || selectedTab == ArtistListType.ancillary || selectedTab == ArtistListType.allAncillaryForVoice)
        ArtistFilterCategoryModel(
          filterType: ArtistFilterType.services,
          items: dropDownResponseModel.services ?? [],
        ),
      ArtistFilterCategoryModel(filterType: ArtistFilterType.experience, items: dropDownResponseModel.experience ?? [])
    ];
    return SingleChildScrollView(
      controller: scrollController,
      child: Container(
        width: Responsive.isDesktop(context) ? 239.w : null,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(color: Colors.grey.shade300, blurRadius: 4),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            28.ph,
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 28.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text24And20SemiBold(AppStrings.filter.toUpperCase()),
                  InkWell(
                    onTap: () {
                      context.read<ExploreArtistsBloc>().resetFilters();
                      if (!Responsive.isDesktop(context)) context.pop(); // Close bottom sheet
                    },
                    child: TextTitle18And14(
                      AppStrings.reset,
                      color: colorScheme.hyperlinkBlueColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 16.w),
              child: TextTitle18And14( AppStrings.favourites.substring(0, 9),
              fontSize: Responsive.isDesktop(context) ? 18.sp : 16.sp,
              fontWeight: FontWeight.w700),
            ),
            BlocBuilder<ExploreArtistsBloc, ExploreArtistsState>(
              builder: (context, state) {
                final isFavorite = context.read<ExploreArtistsBloc>().queryFavorite;
                return Padding(
                  padding: EdgeInsets.only( left: 16.w, right: 28.w, top: 8.h, bottom: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                       InkWell(
                          onTap: () {
                            context.read<ExploreArtistsBloc>().changeFavoriteQuery(queryFavorite: !isFavorite);
                            },
                             child: Container(
                              width: 24.w,
                              height: 24.h,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: colorScheme.lightGreyB2B2B2,
                                  width: 1.w,
                                ),
                              ),
                              child: isFavorite
                                  ? Center(
                                      child: Container(
                                        width: 16.w,
                                        height: 16.h,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: colorScheme.checkBoxLemonGreen,
                                        ),
                                      ),
                                    )
                                  : null,
                             ),
                           ),
                      10.pw,
                      GestureDetector(
                        onTap: () {
                          context.read<ExploreArtistsBloc>().changeFavoriteQuery(queryFavorite: !isFavorite);
                        },
                        child: TextTitle14(
                          AppStrings.showFavouritesOnly,
                          style: TextStyle(fontSize: 15.sp),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            ...filterCategories.map((category) => FilterCategory(category: category, selectedTab: selectedTab)),
          ],
        ),
      ),
    );
  }
}
