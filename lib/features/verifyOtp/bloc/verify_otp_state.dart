import 'package:equatable/equatable.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';

abstract class VerifyOtpState extends Equatable {}

class VerifyOtpInitState extends VerifyOtpState {
  @override
  List<Object> get props => [];
}

class VerifyOtpLoadingState extends VerifyOtpState {
  @override
  List<Object> get props => [];
}

class VerifyOtpSuccessState extends VerifyOtpState {
  final UserDataModel? userDataModel;

  VerifyOtpSuccessState({this.userDataModel});
  @override
  List<Object> get props => [];
}

class VerifyOtpErrorState extends VerifyOtpState {
  final String errorMsg;
  VerifyOtpErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
