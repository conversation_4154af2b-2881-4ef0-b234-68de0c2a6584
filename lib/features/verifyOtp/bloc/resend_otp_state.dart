import 'package:equatable/equatable.dart';

abstract class ResendOtpState extends Equatable {}

class ResendOtpInitState extends ResendOtpState {
  @override
  List<Object> get props => [];
}

class ResendOtpLoadingState extends ResendOtpState {
  @override
  List<Object> get props => [];
}

class ResendOtpSuccessState extends ResendOtpState {
  final String? message;

  ResendOtpSuccessState({this.message});
  @override
  List<Object> get props => [];
}

class ResendOtpErrorState extends ResendOtpState {
  final String errorMsg;
  ResendOtpErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
