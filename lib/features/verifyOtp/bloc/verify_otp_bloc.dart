import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_state.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/data/auth_response_model.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

import '../../../core/services/hive/hive_keys.dart';

class VerifyOtpBloc extends Cubit<VerifyOtpState> {
  @override
  VerifyOtpBloc() : super(VerifyOtpInitState());

  void verifyOtp({
    required int otp,
    String? email,
    required String type,
    String? phoneNumber,
    String? playerId,
  }) async {
    emit(VerifyOtpLoadingState());
    try {
      final response = await ApiService.instance.verifyOtp(
          otp: otp, type: type, phoneNumber: phoneNumber, email: email, playerId: playerId);
      if (response.success) {
          AuthResponseModel authResponseModel =
              AuthResponseModel.fromJson(response.data);
        // Getting Token when doing Sign Up and Login(Email)
        if (email != null && email != "ND") {
          await FirebaseAuth.instance.signInWithEmailAndPassword(email: "<EMAIL>", password: "Hello_TVD@2025");
          ApiService.instance.setPartnerGatewayToken(authResponseModel.token ?? "");
          await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.userToken, authResponseModel.token ?? "");
        }
        final userDataResponse = await ApiService.instance.getUserData();
        if (userDataResponse.success) {
          UserDataModel userDataModel = UserDataModel.fromJson(userDataResponse.data);
          await HiveStorageHelper.saveData<UserDataModel>(HiveBoxName.user, HiveKeys.userData, userDataModel);
          emit(VerifyOtpSuccessState(userDataModel: userDataModel));
        } else {
          emit(VerifyOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
        }
      } else {
        emit(VerifyOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(VerifyOtpErrorState(AppStrings.genericErrorMsg));
    }
  }
}
