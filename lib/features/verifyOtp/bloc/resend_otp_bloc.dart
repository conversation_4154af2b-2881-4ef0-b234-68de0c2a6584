import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_state.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class ResendOtpBloc extends Cubit<ResendOtpState> {
  ResendOtpBloc() : super(ResendOtpInitState());

  Future<void> resendOtp({
    String? email,
    String? phoneNumber,
  }) async {
    emit(ResendOtpLoadingState());
    final response = await ApiService.instance.resendOtp(phoneNumber: phoneNumber, email: email);
    try {
      if (response.success) {
        emit(ResendOtpSuccessState(message: response.message));
      } else {
        emit(ResendOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ResendOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
