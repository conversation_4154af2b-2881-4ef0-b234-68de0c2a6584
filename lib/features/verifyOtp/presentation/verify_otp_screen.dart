import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_state.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_state.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../../../core/services/hive/hive_keys.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String? email;
  final String? phoneNumber;
  const OtpVerificationScreen({super.key, this.email, this.phoneNumber});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController _otpController;
  Timer? _resendTimer;
  int _timeLeft = 30; // Start with 30 seconds countdown
  bool _canResend = false; // Initially disabled

  @override
  void initState() {
      _otpController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      startResendTimer(); // Start the countdown when screen loads
      
    });
    super.initState();
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    super.dispose();
  }

  void startResendTimer() {

    setState(() {
      _timeLeft = 30;
      _canResend = false; // Disable button for first 30 seconds
    });

    if (_resendTimer?.isActive ?? false) {
      _resendTimer?.cancel();
    }

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
        } else {
          _canResend = true; // Enable the button after 30 seconds
          timer.cancel();
        }
      });
    });
  }

  String get resendText {
    if (_canResend) {
      return AppStrings.resendCode;
    }
    return 'Resend code in ${_timeLeft}s'; // Countdown display
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Row(
          children: [
            if (Responsive.isDesktop(context))
              const Expanded(child: TVDBrandingScreen()),
            Expanded(
              child: Padding(
                padding: !Responsive.isDesktop(context)
                    ? EdgeInsets.zero
                    : EdgeInsets.all(44.0.h),
                child: Card(
                  color: Theme.of(context).colorScheme.white,
                  elevation: !Responsive.isDesktop(context) ? 0 : 8,
                  shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal:
                            !Responsive.isDesktop(context) ? 16.h : 52.h,
                        vertical: !Responsive.isDesktop(context) ? 24.h : 48.h),
                    child: Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: !Responsive.isDesktop(context)
                            ? CrossAxisAlignment.start
                            : CrossAxisAlignment.center,
                        children: [
                          if (!Responsive.isDesktop(context)) 25.ph,
                          if (!Responsive.isDesktop(context))
                            CustomBackButton(onTap: () {
                              context.pop();
                            }),
                          if (!Responsive.isDesktop(context)) 25.ph,
                          const TextDisplayLarge36And26(AppStrings.verifyOTP),
                          16.ph,
                          TextTitle14(AppStrings.pleaseEnter6DigitOtp(
                              widget.email != "ND" ? "email" : "phone number")),
                          !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal:
                                      Responsive.isDesktop(context) ? 50 : 0),
                              child: PinCodeTextField(
                                appContext: context,
                                length: 6,
                                controller: _otpController,
                                showCursor: true,
                                cursorColor:
                                    Theme.of(context).colorScheme.primary,
                                keyboardType: TextInputType.number,
                                errorTextSpace: 20,
                                hintCharacter: '-',
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp("[0-9]")),
                                ],
                                textStyle: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primaryGrey,
                                        fontWeight: FontWeight.w600),
                                pinTheme: PinTheme(
                                  shape: PinCodeFieldShape.box,
                                  fieldHeight: Responsive.isDesktop(context)
                                      ? 56.h
                                      : 44.h,
                                  fieldWidth: Responsive.isDesktop(context)
                                      ? 56.h
                                      : 44.h,
                                  borderRadius: Responsive.isDesktop(context)
                                      ? BorderRadius.circular(12.r)
                                      : BorderRadius.circular(8.r),
                                  selectedColor:
                                      Theme.of(context).colorScheme.primary,
                                  disabledColor: Theme.of(context)
                                      .colorScheme
                                      .lightGreyD9D9D9,
                                  inactiveColor: Theme.of(context)
                                      .colorScheme
                                      .lightGreyD9D9D9,
                                  errorBorderColor:
                                      Theme.of(context).colorScheme.red,
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ),
                          !Responsive.isDesktop(context) ? 16.ph : 0.ph,
                          Align(
                            alignment: Alignment.centerRight,
                            child: BlocListener<ResendOtpBloc, ResendOtpState>(
                              listener: (context, state) {
                                if (!mounted) return;

                                if (state is ResendOtpLoadingState) {
                                  Dialogs.showOnlyLoader(context);
                                }
                                if (state is ResendOtpSuccessState) {
                                  context.pop();
                                  startResendTimer();
                                  CustomToast.show(
                                      context: context,
                                      message: state.message ?? (widget.email != "ND"
                                          ? AppStrings.otpSentOnEmail
                                          : AppStrings.otpSentOnPhone),
                                      isSuccess: true);
                                }
                                if (state is ResendOtpErrorState) {
                                  context.pop();
                                  CustomToast.show(
                                      context: context,
                                      message: state.errorMsg,
                                      isSuccess: true);
                                }
                              },
                              child: InkWell(
                                onTap: _canResend
                                    ? () {
                                        context.read<ResendOtpBloc>().resendOtp(
                                            phoneNumber:
                                                widget.phoneNumber != "ND"
                                                    ? widget.phoneNumber
                                                    : null,
                                            email: widget.email != "ND"
                                                ? widget.email!.toLowerCase()
                                                : null);
                                      }
                                    : null,
                                child: Padding(
                                    padding: EdgeInsets.symmetric(
                                  horizontal:
                                      Responsive.isDesktop(context) ? 50 : 0),
                                  child: TextTitle14(
                                    resendText,
                                    color: _canResend
                                        ? Theme.of(context)
                                            .colorScheme
                                            .hyperlinkBlueColor
                                        : Theme.of(context)
                                            .colorScheme
                                            .darkgrey494949,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          !Responsive.isDesktop(context)
                              ? 24.ph
                              : const Spacer(),
                          BlocListener<VerifyOtpBloc, VerifyOtpState>(
                            listener: (context, state) {
                              if (state is VerifyOtpLoadingState) {
                                Dialogs.showOnlyLoader(context);
                              }
                              if (state is VerifyOtpSuccessState) {
                                context.pop();
                                if (!(state.userDataModel?.isPhoneVerified ??
                                    true)) {
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.enterInformation,
                                      pathParameters: {
                                        Params.email: widget.email ?? ""
                                      }, forceGo: true);
                                  return;
                                }
                                bool isClient = (state.userDataModel?.role == UserType.client);
                                bool isVoice = (state.userDataModel?.role == UserType.voice);
                                bool isAncillary = (state.userDataModel?.role == UserType.ancillaryService);
                                if (state.userDataModel?.intermediateStep == 0) {
                                      NavigationServiceImpl.getInstance()!.doNavigation(
                                        context,
                                        routeName: RouteName.createProfile,
                                        pathParameters: {Params.type: getUserTypeString(state.userDataModel?.role)},
                                        forceGo: true);
                                  return;
                                }
                                if (isClient || isAncillary && state.userDataModel?.intermediateStep == 1) {
                                  HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, forceGo: true);
                                  return;
                                }
                                if (isVoice && state.userDataModel?.intermediateStep == 1) {
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.vocalCharacteristics, forceGo: true);
                                  return;
                                }
                                if (isVoice && state.userDataModel?.intermediateStep == 2) {
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.uploadSamples, forceGo: true);
                                  return;
                                }
                                if (isVoice && state.userDataModel?.intermediateStep == 3) {
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.projectType, forceGo: true);
                                  return;
                                }
                                if (isVoice && (state.userDataModel?.intermediateStep == 4)) {
                                  HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, forceGo: true);
                                  return;
                                }
                              }
                              if (state is VerifyOtpErrorState) {
                                context.pop();
                                CustomToast.show(
                                    context: context,
                                    message: state.errorMsg,
                                    isSuccess: false);
                              }
                            },
                            child: PrimaryButton(
                                onPressed: () {
                                        if (formKey.currentState!.validate()) {
                                          context
                                              .read<VerifyOtpBloc>()
                                              .verifyOtp(
                                                  otp: int.parse(_otpController
                                                      .text
                                                      .trim()),
                                                  type: widget.email != "ND"
                                                      ? Params.email
                                                      : Params.phoneNumber,
                                                  phoneNumber:
                                                      widget.phoneNumber != "ND"
                                                          ? widget.phoneNumber
                                                          : null,
                                                  email: widget.email != "ND"
                                                      ? widget.email!.toLowerCase()
                                                      : null,
                                                  playerId: HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.playerId));
                                        }
                                      },
                                buttonText: AppStrings.continueBtnText),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getUserTypeString(UserType? role) {
    if (role == UserType.client) {
      return UserType.client.getString();
    } else if (role == UserType.voice) {
      return UserType.voice.getString();
    } else if (role == UserType.ancillaryService) {
      return UserType.ancillaryService.getString();
    } else {
      return UserType.client.getString();
    }
  }
}
