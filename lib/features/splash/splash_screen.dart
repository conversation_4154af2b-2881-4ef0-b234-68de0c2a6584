import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/dashboard/presentation/pages/dashboard.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

import '../../core/services/hive/hive_keys.dart';
import '../dashboard/bloc/dashboard_cubit.dart';
import '../explore_artists/bloc/explore_artists_bloc.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late AnimationController _blurController;
  late Animation<double> _blurAnimation;
  String? token;
  bool? isUserLoggedIn;
  bool _isAnimationDone = false;
  bool _isUserDataReady = false;
  bool isDashboard = false;

  @override
  void initState() {
    getUserData();
    // Animation for logo fade-in
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _controller.addStatusListener(_animationListener);

    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInCirc);

    // Animation for blur effect
    _blurController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _blurAnimation = Tween<double>(begin: 10.0, end: 0.0).animate(
      CurvedAnimation(parent: _blurController, curve: Curves.easeInOut),
    );

    _controller.forward();
    _blurController.forward();
    super.initState();
  }

  void _animationListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      Timer(const Duration(seconds: 2), () {
        _isAnimationDone = true;
        final state = context.read<UserBloc>().state;
        if (state is UserSuccessState) {
          _isUserDataReady = true;
          _tryNavigate(userDataModel: state.userDataModel);
        } else {
          _tryNavigate();
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.removeStatusListener(_animationListener);
    _controller.dispose();
    _blurController.dispose();
    super.dispose();
  }

  getUserData() async {
    token = HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.userToken);
    isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
    if (token != null && token!.isNotEmpty) {
      if (mounted) {
        context.read<UserBloc>().getUserData();
      }
    } else {
      _isUserDataReady = true;
    }
  }

  void _tryNavigate({UserDataModel? userDataModel}) {
    if (_isAnimationDone && (_isUserDataReady || token == null)) {
      doNavigate(userDataModel: userDataModel);
    }
  }

  Future<void> doNavigate({UserDataModel? userDataModel}) async {
    if (token == null || token!.isEmpty) {
      NavigationServiceImpl.getInstance()!
          .doNavigation(context, routeName: RouteName.login, forceGo: true);
      return;
    }

    if (!(userDataModel?.isPhoneVerified ?? true)) {
      NavigationServiceImpl.getInstance()!.doNavigation(context,
          routeName: RouteName.enterInformation,
          pathParameters: {
            Params.email: userDataModel?.email ?? "",
          },
          forceGo: true);
      return;
    }
    bool isClient = (userDataModel?.role == UserType.client);
    bool isVoice = (userDataModel?.role == UserType.voice);
    bool isAncillary = userDataModel?.role == UserType.ancillaryService;
    if (userDataModel?.intermediateStep == 0) {
      NavigationServiceImpl.getInstance()!.doNavigation(context,
          routeName: RouteName.createProfile,
          pathParameters: {
            Params.type: isClient
                ? UserType.client.getString()
                : isAncillary ? UserType.ancillaryService.getString() : UserType.voice.getString(),
          },
          forceGo: true);
      return;
    }
    if (isClient && userDataModel?.intermediateStep == 1) {
      HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
      isDashboard = true;
      setState(() {});
      return;
    }
    if (isVoice && userDataModel?.intermediateStep == 1) {
      NavigationServiceImpl.getInstance()!.doNavigation(context,
          routeName: RouteName.vocalCharacteristics, forceGo: true);
      return;
    }
    if (isVoice && userDataModel?.intermediateStep == 2) {
      NavigationServiceImpl.getInstance()!.doNavigation(context,
          routeName: RouteName.uploadSamples, forceGo: true);
      return;
    }
    if (isVoice && userDataModel?.intermediateStep == 3) {
      NavigationServiceImpl.getInstance()!
          .doNavigation(context, routeName: RouteName.projectType, forceGo: true);
      return;
    }
    if (isVoice && userDataModel?.intermediateStep == 4) {
      HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
      isDashboard = true;
      setState(() {});
      return;
    }

    NavigationServiceImpl.getInstance()!
        .doNavigation(context, routeName: RouteName.login, forceGo: true);
    return;
  }

  @override
  Widget build(BuildContext context) {
    UserDataModel? userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
     if (isDashboard) {
      return MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => DashboardCubit(hasClientTab: (userData?.role == UserType.client || userData?.role == UserType.ancillaryService))),
          BlocProvider(create: (_) => ExploreArtistsBloc()),
        ],
        child: const Dashboard(),
      );
    }
    return Scaffold(
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserSuccessState) {
            _isUserDataReady = true;
            _tryNavigate(userDataModel: state.userDataModel);
          } else if (state is UserErrorState) {
            _isUserDataReady = true;
            _tryNavigate();
          }
        },
        child: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.5,
                  colors: [Color(0xFFF8FF99), Color(0xFFFCFFD3)],
                  stops: [0.2, 1.0],
                ),
              ),
            ),
            FadeTransition(
              opacity: _animation,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          height: 64.h,
                          width: 64.h,
                          child: Image.asset("assets/images/tvd_logo_img.png"),
                        ),
                        SvgPicture.asset("assets/images/dot_first.svg"),
                        SvgPicture.asset("assets/images/dot_second.svg"),
                        SvgPicture.asset("assets/images/dot_third.svg"),
                        SvgPicture.asset("assets/images/dot_four.svg"),
                        SvgPicture.asset("assets/images/dot_five.svg"),
                      ],
                    ),
                    28.ph,
                    Text(AppStrings.theVoiceDirectory,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 24.sp,
                            )),
                  ],
                ),
              ),
            ),
            AnimatedBuilder(
              animation: _blurAnimation,
              builder: (context, child) {
                return BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: _blurAnimation.value,
                    sigmaY: _blurAnimation.value,
                  ),
                  child: SizedBox(
                    height: 1.sh,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
