import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../widgets/buttons/back_button_arrow.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../features/voice/profile/presentation/error_screen.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/buttons/back_button.dart';
import '../../../widgets/loading_dialog.dart';
import '../../landing_page/data/enums/landing_page_item_type.dart';
import '../../landing_page/widgets/landing_page_app_bar.dart';
import '../bloc/public_voice_profile_cubit.dart';
import 'widgets/profile_basic_details_widget.dart';
import 'widgets/profile_image_widget.dart';
import 'widgets/profile_project_preference_widget.dart';
import 'widgets/profile_reviews_widget.dart';
import 'widgets/profile_vocal_details_widget.dart';
import 'widgets/profile_vocal_samples_widget.dart';

class PublicVoiceProfileScreen extends StatefulWidget {
  final int? id;
  const PublicVoiceProfileScreen({super.key, this.id});

  @override
  State<PublicVoiceProfileScreen> createState() => _PublicVoiceProfileScreenState();
}

class _PublicVoiceProfileScreenState extends State<PublicVoiceProfileScreen> {
  @override
  void initState() {
    super.initState();
    context.read<PublicVoiceProfileCubit>().getVoiceProfileData(id: widget.id);
  }

  void onSectionTap(LandingPageItemType type) {
    switch (type) {
      case LandingPageItemType.about:
        NavigationServiceImpl.getInstance()?.doNavigation(
          context,
          routeName: RouteName.landingPage,
          useGo: true,
          extra: LandingPageItemType.about.toString(),
        );
        break;
      case LandingPageItemType.exploreVoices:
        break;
      case LandingPageItemType.howItWorks:
        NavigationServiceImpl.getInstance()?.doNavigation(
          context,
          routeName: RouteName.landingPage,
          useGo: true,
          extra: LandingPageItemType.howItWorks.toString(),
        );
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<PublicVoiceProfileCubit, PublicVoiceProfileState>(
          builder: (context, state) {
            if (state is PublicVoiceProfileLoadingState) {
              return Column(
                children: [
                  if (Responsive.isDesktop(context)) ...[
                    LandingPageAppBar(
                      onSectionTap: onSectionTap,
                    ),
                  ],
                  const Expanded(child: Center(child: Loader())),
                ],
              );
            }
        
            if (state is PublicVoiceProfileErrorState) {
              return ErrorScreen(
                onRetry: () {
                  context.read<PublicVoiceProfileCubit>().getVoiceProfileData(id: widget.id);
                },
                errorMessage: state.errorMsg,
                imageWidget: SvgPicture.asset(
                  AppImages.snapMomentIcon,
                  height: 200.h,
                  width: 100.w,
                ),
              );
            }
        
            if (state is PublicVoiceProfileSuccessState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (Responsive.isDesktop(context)) ...[
                    LandingPageAppBar(
                      onSectionTap: onSectionTap,
                    ),
                  ],
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        horizontal: Responsive.isDesktop(context) ? 80.h : 16.h,
                        vertical: Responsive.isDesktop(context) ? 40.h : 24.h,
                      ),
                      child: Column(
                        crossAxisAlignment: Responsive.isDesktop(context)
                            ? CrossAxisAlignment.start
                            : CrossAxisAlignment.center,
                        children: [
                          if (Responsive.isDesktop(context)) ...[
                            const CustomBackButtonArrow(),
                            24.ph,
                          ] else ...[
                            Align(
                              alignment: Alignment.centerLeft,
                              child: const CustomBackButton(),
                            ),
                            24.ph,
                          ],
                          ProfileImageWidget(userData: state.userDataModel),
                          Responsive.isDesktop(context) ? 46.ph : 16.ph,
                          ProfileBasicDetailsWidget(userData: state.userDataModel),
                          Responsive.isDesktop(context) ? 28.ph : 20.ph,
                          ProfileVocalDetailsWidget(userData: state.userDataModel),
                          Responsive.isDesktop(context) ? 28.ph : 20.ph,
                          ProfileProjectPreferenceWidget(userData: state.userDataModel),
                          Responsive.isDesktop(context) ? 28.ph : 20.ph,
                          ProfileVocalSamplesWidget(
                            userData: state.userDataModel,
                            state: state,
                            onPlayAudio: (index) {
                              context.read<PublicVoiceProfileCubit>().playAudio(index);
                            },
                          ),
                          Responsive.isDesktop(context) ? 28.ph : 20.ph,
                          ProfileReviewsWidget(userData: state.userDataModel),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }
}
