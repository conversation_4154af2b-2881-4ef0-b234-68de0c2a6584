import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/common_shadow_container.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';
import '../../bloc/public_voice_profile_cubit.dart';

class ProfileVocalSamplesWidget extends StatelessWidget {
  final UserDataModel userData;
  final PublicVoiceProfileSuccessState state;
  final Function(int) onPlayAudio;

  const ProfileVocalSamplesWidget({
    super.key,
    required this.userData,
    required this.state,
    required this.onPlayAudio,
  });

  @override
  Widget build(BuildContext context) {
    return CommonShadowContainer(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 28.h : 12.h),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text24And20SemiBold(AppStrings.vocalSamples),
          Responsive.isDesktop(context) ? 28.ph : 12.ph,
          _ProfileAudioSampleWidget(
            userData: userData,
            state: state,
            onPlayAudio: (index) {
              onPlayAudio.call(index);
            },
          ),
        ],
      ),
    );
  }
}

class _ProfileAudioSampleWidget extends StatelessWidget {
  final UserDataModel userData;
  final PublicVoiceProfileSuccessState state;
  final Function(int) onPlayAudio;

  const _ProfileAudioSampleWidget({
    required this.userData,
    required this.state,
    required this.onPlayAudio,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (userData.voiceAudioUrls == null || userData.voiceAudioUrls!.isEmpty) {
      return const Center(child: Text(AppStrings.noAudioSamplesAvailable));
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: userData.voiceAudioUrls!.length,
      itemBuilder: (context, index) {
        final isPlaying = state.playerStates[index] == PlayerState.playing;
        final duration = state.durations[index];
        final position = state.positions[index];
        return Padding(
          padding: EdgeInsets.only(
            bottom: index == userData.voiceAudioUrls!.length - 1
                ? 0
                : Responsive.isDesktop(context)
                    ? 28.h
                    : 12.h,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 9.h),
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: colorScheme.lightGreyD9D9D9),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: SvgPicture.asset(
                          isPlaying ? AppImages.pauseIcon : AppImages.play,
                        ),
                        onPressed: () {
                          onPlayAudio.call(index);
                        },
                      ),
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: SliderTheme(
                                data: SliderThemeData(
                                  thumbShape: RoundSliderThumbShape(
                                    enabledThumbRadius: 6.r,
                                  ),
                                ),
                                child: Slider(
                                  thumbColor: colorScheme.white,
                                  activeColor: colorScheme.primary,
                                  inactiveColor: colorScheme.lightGreyD9D9D9,
                                  value: position.inSeconds.toDouble(),
                                  max: duration.inSeconds.toDouble(),
                                  onChanged: (value) async {
                                    final newPosition = Duration(seconds: value.toInt());
                                    await state.audioPlayers[index].seek(newPosition);
                                  },
                                ),
                              ),
                            ),
                            TextTitle14(
                              isPlaying
                                  ? "${(duration - position).inMinutes}:${((duration - position).inSeconds % 60).toString().padLeft(2, '0')}"
                                  : "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}