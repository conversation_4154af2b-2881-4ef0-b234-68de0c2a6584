import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/common_card_widget.dart';
import '../../../../utils/common_shadow_container.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';

class ProfileBasicDetailsWidget extends StatelessWidget {
  final UserDataModel userData;

  const ProfileBasicDetailsWidget({
    super.key,
    required this.userData,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return CommonShadowContainer(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 28.h : 12.h),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text24And20SemiBold(AppStrings.profileInformation),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.bio ?? ''),
          4.ph,
          const TextTitle14(AppStrings.bio_),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.gender?.name ?? ''),
          4.ph,
          const TextTitle14(AppStrings.gender_),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(
            "${userData.city}, ${userData.state}, ${userData.country}",
          ),
          4.ph,
          const TextTitle14(AppStrings.address),
          if (userData.tags != null && userData.tags!.isNotEmpty) ...[
            Responsive.isDesktop(context) ? 28.ph : 24.ph,
            CommonCardWidget<Object>(
              items: userData.tags ?? [],
              itemBuilder: (context, item) {
                return TextTitle14(
                  item.toString(),
                  color: colorScheme.hyperlinkBlueColor,
                );
              },
            ),
            4.ph,
            const TextTitle14(AppStrings.keywords),
          ]
        ],
      ),
    );
  }
}
