import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../utils/environment_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';

class ProfileImageWidget extends StatelessWidget {
  final UserDataModel userData;

  const ProfileImageWidget({super.key, required this.userData});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Builder(
      builder: (context) {
        if (Responsive.isDesktop(context)) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                      CircleAvatar(
                        radius: 60.r,
                        backgroundColor: colorScheme.blackE8E8E8,
                        backgroundImage: userData.profilePic?.isNotEmpty == true
                            ? Uri.parse(userData.profilePic ?? '').isAbsolute
                                ? NetworkImage(userData.profilePic ?? '')
                                : NetworkImage("${EnvironmentConfig.imageBaseUrl}${userData.profilePic}")
                            : null,
                        child: userData.profilePic?.isNotEmpty == true
                            ? null
                            : SvgPicture.asset(AppImages.userIcon),
                      ),
                      Positioned(
                        bottom: -10.h,
                        child: _ProfileRatingChip(userData: userData),
                      ),
                    ],
                  ),
                  20.pw,
                  Expanded(
                    child: TextDisplayLarge24And16(
                      '${userData.firstName} ${userData.lastName}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      height: 2,
                      fontSizeWeb: 20.sp,
                    ),
                  ),
                ],
              ),
            ],
          );
        }
        return Column(
          children: [
            Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                CircleAvatar(
                  radius: 60.r,
                  backgroundColor: colorScheme.blackE8E8E8,
                  backgroundImage: userData.profilePic?.isNotEmpty == true
                      ? Uri.parse(userData.profilePic ?? '').isAbsolute
                          ? NetworkImage(userData.profilePic ?? '')
                          : NetworkImage("${EnvironmentConfig.imageBaseUrl}${userData.profilePic}")
                      : null,
                ),
                Positioned(
                  bottom: -10.h,
                  child: _ProfileRatingChip(userData: userData),
                ),
              ],
            ),
            16.ph,
            TextDisplayLarge24And16(
              '${userData.firstName} ${userData.lastName}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            12.ph,
          ],
        );
      },
    );
  }
}

class _ProfileRatingChip extends StatelessWidget {
  final UserDataModel userData;
  const _ProfileRatingChip({required this.userData});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Builder(builder: (context) {
      if (userData.reviews == null ||
          userData.reviews?.averageRating == null ||
          userData.reviews?.averageRating == 0) {
        return const SizedBox.shrink();
      }
      return Container(
        decoration: BoxDecoration(
          color: colorScheme.lightGreen6EAA21,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: colorScheme.white,
            width: 2.r,
            strokeAlign: BorderSide.strokeAlignOutside,
          ),
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              AppImages.starFilled,
              width: 12.r,
              height: 12.r,
              color: colorScheme.white,
            ),
            4.pw,
            Text(
              userData.reviews!.averageRating.toString(),
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.white,
              ),
            ),
          ],
        ),
      );
    });
  }
}
