import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/common_shadow_container.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';

class ProfileReviewsWidget extends StatelessWidget {
  final UserDataModel userData;

  const ProfileReviewsWidget({super.key, required this.userData});

  @override
  Widget build(BuildContext context) {
    return CommonShadowContainer(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 28.h : 12.h),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text24And20SemiBold(AppStrings.reviews.toUpperCase()),
          Responsive.isMobile(context) ? 12.ph : 28.ph,
          Builder(
            builder: (context) {
              if (userData.reviews?.totalReviewers == 0 || userData.reviews?.totalReviewers == null) {
                return Center(
                  child: const Text24And20SemiBold(AppStrings.noReviewsYet),
                );
              }
              return _ProfileRatingWidget(userData: userData);
            },
          ),
          Responsive.isDesktop(context) ? 28.ph : 12.ph,
        ],
      ),
    );
  }
}

class _ProfileRatingWidget extends StatelessWidget {
  final UserDataModel userData;

  const _ProfileRatingWidget({required this.userData});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Builder(
      builder: (context) {
        if (Responsive.isDesktop(context)) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: "${userData.reviews?.averageRating ?? 0}/",
                          style: textTheme.displayLarge?.copyWith(
                            color: colorScheme.textfieldTitleColor,
                          ),
                        ),
                        TextSpan(
                          text: "5",
                          style: textTheme.displayLarge?.copyWith(
                            color: colorScheme.hintTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  12.ph,
                  TextTitle14(
                    "${userData.reviews?.totalReviewers ?? 0} ${(userData.reviews?.totalReviewers ?? 0) == 1 ? 'user' : 'users'} rated",
                    color: colorScheme.hyperlinkBlueColor,
                  ),
                ],
              ),
              Spacer(flex: 2),
              Expanded(
                flex: 5,
                child: _ReviewRatingBars(userData: userData),
              ),
            ],
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: "${userData.reviews?.averageRating ?? 0}/",
                    style: textTheme.displayLarge?.copyWith(
                      color: colorScheme.textfieldTitleColor,
                    ),
                  ),
                  TextSpan(
                    text: "5",
                    style: textTheme.displayLarge?.copyWith(
                      color: colorScheme.hintTextColor,
                    ),
                  ),
                ],
              ),
            ),
            12.ph,
            TextTitle14(
              "${userData.reviews?.totalReviewers ?? 0} ${(userData.reviews?.totalReviewers ?? 0) == 1 ? 'user' : 'users'} rated",
              color: colorScheme.hyperlinkBlueColor,
            ),
            16.ph,
            _ReviewRatingBars(userData: userData),
          ],
        );
      },
    );
  }
}

class _ReviewRatingBars extends StatelessWidget {
  final UserDataModel userData;

  const _ReviewRatingBars({required this.userData});

  int _getRatingCount(List<RatingCount>? ratingCounts, double rating) {
    if (ratingCounts == null) return 0;
    return ratingCounts.firstWhere(
              (element) => element.rating == rating,
              orElse: () => RatingCount(rating: rating, count: 0),
            ).count ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _ReviewRatingBar(
          rating: 5,
          count: _getRatingCount(userData.reviews?.ratingCounts, 5),
          totalCount: userData.reviews?.totalReviewers ?? 0,
        ),
        12.ph,
        _ReviewRatingBar(
          rating: 4,
          count: _getRatingCount(userData.reviews?.ratingCounts, 4),
          totalCount: userData.reviews?.totalReviewers ?? 0,
        ),
        12.ph,
        _ReviewRatingBar(
          rating: 3,
          count: _getRatingCount(userData.reviews?.ratingCounts, 3),
          totalCount: userData.reviews?.totalReviewers ?? 0,
        ),
        12.ph,
        _ReviewRatingBar(
          rating: 2,
          count: _getRatingCount(userData.reviews?.ratingCounts, 2),
          totalCount: userData.reviews?.totalReviewers ?? 0,
        ),
        12.ph,
        _ReviewRatingBar(
          rating: 1,
          count: _getRatingCount(userData.reviews?.ratingCounts, 1),
          totalCount: userData.reviews?.totalReviewers ?? 0,
        ),
      ],
    );
  }
}

class _ReviewRatingBar extends StatelessWidget {
  final int rating;
  final int count;
  final int totalCount;

  const _ReviewRatingBar({
    required this.rating,
    required this.count,
    required this.totalCount,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    Color ratingColor;
    switch (rating) {
      case 5:
        ratingColor = colorScheme.green13B25D;
        break;
      case 4:
        ratingColor = colorScheme.green13B25D;
        break;
      case 3:
        ratingColor = colorScheme.yellowFFC500;
        break;
      case 2:
        ratingColor = colorScheme.redFF6257;
        break;
      case 1:
        ratingColor = colorScheme.redFF6257;
        break;
      default:
        ratingColor = colorScheme.primary;
    }

    return Row(
      children: [
        TextBodySmall12(rating.toString(), color: colorScheme.hintTextColor),
        4.pw,
        SvgPicture.asset(
          AppImages.starFilled,
          color: colorScheme.hintTextColor,
          height: 10.h,
          width: 10.w,
        ),
        4.pw,
        Expanded(
          child: Stack(
            children: [
              Container(
                height: 7.h,
                decoration: BoxDecoration(
                  color: colorScheme.hintTextColor,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              FractionallySizedBox(
                widthFactor: count / totalCount,
                child: Container(
                  height: 7.h,
                  decoration: BoxDecoration(
                    color: ratingColor,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ],
          ),
        ),
        10.pw,
        TextTitle14(
          count.toString(),
          textAlign: TextAlign.end,
          color: colorScheme.textfieldTitleColor,
        ),
      ],
    );
  }
}