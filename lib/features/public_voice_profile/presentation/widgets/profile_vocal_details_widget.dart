import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../utils/common_shadow_container.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/extensions/string_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';

class ProfileVocalDetailsWidget extends StatelessWidget {
  final UserDataModel userData;

  const ProfileVocalDetailsWidget({super.key, required this.userData});

  @override
  Widget build(BuildContext context) {
    return CommonShadowContainer(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 28.h : 12.h),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text24And20SemiBold(AppStrings.vocalCharacterstics),
          Responsive.isDesktop(context) ? 28.ph : 12.ph,
          TextTitle18And20(userData.voiceGender?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.voiceGender.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.voiceType?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.voiceType.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.voiceCharacter?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.voiceCharacter.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.experience?.name ?? 'No experience'),
          4.ph,
          TextTitle14(AppStrings.experience.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.ageRange?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.voiceAge.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.voiceLanguage?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.language.removeLastChar()),
          Responsive.isDesktop(context) ? 28.ph : 24.ph,
          TextTitle18And20(userData.voiceAccent?.map((e) => e.name).join(", ") ?? ''),
          4.ph,
          TextTitle14(AppStrings.accent.removeLastChar()),
        ],
      ),
    );
  }
}
