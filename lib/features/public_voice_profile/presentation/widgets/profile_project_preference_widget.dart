import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/common_card_widget.dart';
import '../../../../utils/common_shadow_container.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/user_data/data/user_data_model.dart';

class ProfileProjectPreferenceWidget extends StatelessWidget {
  final UserDataModel userData;

  const ProfileProjectPreferenceWidget({
    super.key,
    required this.userData,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return CommonShadowContainer(
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 28.h : 12.h),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text24And20SemiBold(AppStrings.projectPreference),
          Responsive.isDesktop(context) ? 28.ph : 12.ph,
          CommonCardWidget<NameIdModel>(
            items: userData.projectType ?? [],
            itemBuilder: (context, item) {
              return TextTitle14(
                item.name.toString(),
                color: colorScheme.hyperlinkBlueColor,
              );
            },
          ),
        ],
      ),
    );
  }
}
