part of 'public_voice_profile_cubit.dart';

sealed class PublicVoiceProfileState extends Equatable {
  const PublicVoiceProfileState();
}

class PublicVoiceProfileInitState extends PublicVoiceProfileState {
  const PublicVoiceProfileInitState();

  @override
  List<Object> get props => [];
}

class PublicVoiceProfileLoadingState extends PublicVoiceProfileState {
  const PublicVoiceProfileLoadingState();

  @override
  List<Object> get props => [];
}

class PublicVoiceProfileSuccessState extends PublicVoiceProfileState {
  final UserDataModel userDataModel;
  final List<AudioPlayer> audioPlayers;
  final List<Duration> durations;
  final List<Duration> positions;
  final List<PlayerState> playerStates;

  const PublicVoiceProfileSuccessState(
    this.userDataModel,
    this.audioPlayers,
    this.durations,
    this.positions,
    this.playerStates,
  );

  @override
  List<Object> get props => [
        userDataModel,
        audioPlayers,
        durations,
        positions,
        playerStates,
      ];
}

class PublicVoiceProfileErrorState extends PublicVoiceProfileState {
  final String errorMsg;

  const PublicVoiceProfileErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
