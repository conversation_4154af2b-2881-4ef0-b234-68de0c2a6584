import 'package:audioplayers/audioplayers.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../utils/environment_config.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';

part 'public_voice_profile_state.dart';

class PublicVoiceProfileCubit extends Cubit<PublicVoiceProfileState> {
  PublicVoiceProfileCubit() : super(PublicVoiceProfileInitState());

  Future<void> getVoiceProfileData({int? id}) async {
    emit(PublicVoiceProfileLoadingState());
    try {
      final response = await ApiService.instance.getPublicVoiceProfileData(id: id);
      if (response.success) {
        final userDataModel = UserDataModel.fromJson(response.data);
        final audioPlayers = <AudioPlayer>[];
        final durations = <Duration>[];
        final positions = <Duration>[];
        final playerStates = <PlayerState>[];

        if (userDataModel.voiceAudioUrls != null) {
          for (final url in userDataModel.voiceAudioUrls!) {
            final player = AudioPlayer();
            audioPlayers.add(player);
            durations.add(Duration.zero);
            positions.add(Duration.zero);
            playerStates.add(PlayerState.stopped);

            final audioUrl = Uri.parse(url.audioUrl ?? '').isAbsolute
                ? url.audioUrl ?? ''
                : '${EnvironmentConfig.imageBaseUrl}${url.audioUrl ?? ''}';

            player.setSourceUrl(audioUrl);

            player.onDurationChanged.listen((d) {
              _updateDurations(player, d);
            });

            player.onPositionChanged.listen((p) {
              _updatePositions(player, p);
            });

            player.onPlayerStateChanged.listen((s) {
              _updatePlayerStates(player, s);
            });

            player.onPlayerComplete.listen((_) {
              _updatePositions(player, Duration.zero);
              _updatePlayerStates(player, PlayerState.completed);
            });
          }
        }

        emit(PublicVoiceProfileSuccessState(userDataModel, audioPlayers, durations, positions, playerStates));
      } else {
        emit(PublicVoiceProfileErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(PublicVoiceProfileErrorState(e.toString()));
    }
  }

  void _updateDurations(AudioPlayer player, Duration d) {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      final index = currentState.audioPlayers.indexOf(player);
      if (index != -1) {
        final updatedDurations = List<Duration>.from(currentState.durations);
        updatedDurations[index] = d;

        emit(PublicVoiceProfileSuccessState(
          currentState.userDataModel,
          currentState.audioPlayers,
          updatedDurations,
          currentState.positions,
          currentState.playerStates,
        ));
      }
    }
  }

  void _updatePositions(AudioPlayer player, Duration duration) {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      final index = currentState.audioPlayers.indexOf(player);
      if (index != -1) {
        final updatedPositions = List<Duration>.from(currentState.positions);
        updatedPositions[index] = duration;

        emit(PublicVoiceProfileSuccessState(
          currentState.userDataModel,
          currentState.audioPlayers,
          currentState.durations,
          updatedPositions,
          currentState.playerStates,
        ));
      }
    }
  }

  void _updatePlayerStates(AudioPlayer player, PlayerState playerState) {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      final index = currentState.audioPlayers.indexOf(player);
      if (index != -1) {
        final updatedStates = List<PlayerState>.from(currentState.playerStates);
        updatedStates[index] = playerState;

        emit(PublicVoiceProfileSuccessState(
          currentState.userDataModel,
          currentState.audioPlayers,
          currentState.durations,
          currentState.positions,
          updatedStates,
        ));
      }
    }
  }

  // Play/Pause Logic
  Future<void> playAudio(int index) async {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      final player = currentState.audioPlayers[index];

      if (currentState.playerStates[index] == PlayerState.playing) {
        await player.pause();
      } else {
        for (int i = 0; i < currentState.audioPlayers.length; i++) {
          if (i != index) {
            await currentState.audioPlayers[i].pause();
          }
        }
        await player.resume();
      }
    }
  }

  Future<void> seekAudio(int index, Duration position) async {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      await currentState.audioPlayers[index].seek(position);
    }
  }

  @override
  Future<void> close() {
    if (state is PublicVoiceProfileSuccessState) {
      final currentState = state as PublicVoiceProfileSuccessState;
      for (final player in currentState.audioPlayers) {
        player.dispose();
      }
    }
    return super.close();
  }
}
