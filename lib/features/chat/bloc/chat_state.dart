part of 'chat_bloc.dart';

class ChatState extends Equatable {
  final bool? isJobChat;
  final String? selectedChat;
  final Convo? selectedConvo;
  final bool isLoadingChat;
  final bool isFromChatList;
  final String jobName;
  final bool isSendingFile;
  final String errorMessage;

  const ChatState({
    this.isJobChat,
    this.isFromChatList = false,
    this.selectedChat,
    this.selectedConvo,
    this.isLoadingChat = false,
    this.jobName = "",
    this.isSendingFile = false,
    this.errorMessage = "",
  });

  ChatState copyWith({
  bool? isJobChat,
  String? selectedChat,
  Convo? selectedConvo,
  bool isLoadingChat = false,
  bool isFromChatList = false,
  String jobName = "",
  bool isSendingFile = false,
  String errorMessage = "",
  }) {
    return ChatState(
      isJobChat: isJobChat ?? this.isJobChat,
      selectedChat: selectedChat,
      selectedConvo: selectedConvo,
      isLoadingChat: isLoadingChat,
      isFromChatList: isFromChatList,
      jobName: jobName,
      isSendingFile: isSendingFile,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    isJobChat,
    selectedChat,
    selectedConvo,
    isLoadingChat,
    isFromChatList,
    jobName,
    isSendingFile,
    errorMessage,
  ];
}
