import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../utils/common_models/media_info_model.dart';
import '../data/convo_model.dart';
import '../data/firebase_repo.dart';
import '../data/message_model.dart';
part 'chat_state.dart';

class ChatBloc extends Cubit<ChatState> {
  ChatBloc() : super(ChatState());

  void updateChatState({
    bool? isJobChat,
    String? selectedChat,
    Convo? selectedConvo,
    bool? isLoadingChat,
    bool? isFromChatList,
    String? loadingChatForshiftId,
    String? jobName,
    bool? isSendingFile,
    String? errorMessage,
  }) {
    emit(state.copyWith(
      isJobChat: isJobChat ?? state.isJobChat,
      selectedChat: selectedChat,
      selectedConvo: selectedConvo,
      isLoadingChat: isLoadingChat ?? state.isLoadingChat,
      isFromChatList: isFromChatList ?? state.isFromChatList,
      jobName: jobName ?? state.jobName,
      isSendingFile: isSendingFile ?? state.isSendingFile,
      errorMessage: errorMessage ?? state.errorMessage,
    ));
  }

  void sendFile(MediaInfoModel file, {required MessageType messageType, required String groupId, required String idFrom, required String idTo, required String myProfileImg, required String myName, required String profileImg, required String name, required String jobId, required String jobName}) async {
    emit(state.copyWith(isSendingFile: true, selectedConvo: state.selectedConvo));
    bool isSent = await MessagesRepository().sendFile(isJobChat: state.isJobChat, file, groupId: groupId, idFrom: idFrom, idTo: idTo, myProfileImg: myProfileImg, myName: myName, profileImg: profileImg, name: name, jobId: jobId, jobName: jobName, type: messageType);
    if (isSent) {
      emit(state.copyWith(isSendingFile: false, selectedConvo: state.selectedConvo));
    } else {
      emit(state.copyWith(isSendingFile: false, errorMessage: "Failed to send file", selectedConvo: state.selectedConvo));
    }
  }
}
