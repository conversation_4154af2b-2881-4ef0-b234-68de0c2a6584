class BlockUserModel {
  final String blockedUserId;
  final String blockedBy;

  BlockUserModel({required this.blockedUserId, required this.blockedBy});

  factory BlockUserModel.fromJson(Map<String, dynamic> json) {
    return BlockUserModel(
      blockedUserId: json['blockedUserId'],
      blockedBy: json['blockedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'blockedUserId': blockedUserId,
      'blockedBy': blockedBy,
    };
  }
}