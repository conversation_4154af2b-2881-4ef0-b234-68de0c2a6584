class Message {
  Message({
    this.toId,
    this.content,
    this.read,
    this.type,
    this.fromId,
    this.timestamp,
    this.mediaName,
  });

  String? toId;
  String? content;
  bool? read;
  String? fromId;
  MessageType? type;
  String? timestamp;
  String? mediaName;

  Message.fromJson(Map<String, dynamic> json) {
    toId = json['toId'];
    content = json['content'];
    read = json['read'];
    type = getMessageType(json['type']);
    fromId = json['fromId'];
    timestamp = json['timestamp'];
    mediaName = json['mediaName'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['toId'] = toId;
    data['content'] = content;
    data['read'] = read;
    data['type'] = type?.name;
    data['fromId'] = fromId;
    data['timestamp'] = timestamp;
    data['mediaName'] = mediaName;
    return data;
  }
}

enum MessageType {text, image, video, audio, file}

MessageType getMessageType(String type) {
  switch (type) {
    case 'text':
      return MessageType.text;
    case 'image':
      return MessageType.image;
    case 'video':
      return MessageType.video;
    case 'audio':
      return MessageType.audio;
    case 'file':
      return MessageType.file;
    default:
      return MessageType.text;
  }
}