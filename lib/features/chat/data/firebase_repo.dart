import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import '../../../core/routes/route_names.dart';
import '../../../utils/common_models/media_info_model.dart';
import '../../../utils/common_models/send_notification_req_model.dart';
import '../../../utils/environment_config.dart';
import 'block_user_model.dart';
import 'convo_model.dart';
import 'message_model.dart';

class MessagesRepository {
  final CollectionReference _chatCollectionReference =
      FirebaseFirestore.instance.collection(EnvironmentConfig.firebaseChatsCollection);

  final StreamController<List<Message>> _chatController =
      StreamController<List<Message>>.broadcast();

  final List<List<Message>> _allPagedResults = <List<Message>>[];

  static const int chatLimit = 10;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;

  Stream<List<Message>> listenToChatsRealTime(String groupId) {
    _requestMessages(groupId);
    return _chatController.stream;
  }

  void _requestMessages(String groupId) {
    var pagechatQuery = _chatCollectionReference
        .doc(groupId)
        .collection(groupId)
        .withConverter<Message>(
          fromFirestore: (snapshots, _) => Message.fromJson(snapshots.data()!),
          toFirestore: (chatItem, _) => chatItem.toJson(),
        )
        .orderBy('timestamp', descending: true)
        .limit(chatLimit);

    if (_lastDocument != null) {
      pagechatQuery = pagechatQuery.startAfterDocument(_lastDocument!);
    }

    if (!_hasMoreData) return;

    var currentRequestIndex = _allPagedResults.length;

    pagechatQuery.snapshots().listen(
      (snapshot) {
        if (snapshot.docs.isNotEmpty) {
          var generalChats =
              snapshot.docs.map((snapshot) => snapshot.data()).toList();

          var pageExists = currentRequestIndex < _allPagedResults.length;

          if (pageExists) {
            _allPagedResults[currentRequestIndex] = generalChats;
          } else {
            _allPagedResults.add(generalChats);
          }

          var allChats = _allPagedResults.fold<List<Message>>(<Message>[],
              (initialValue, pageItems) => initialValue..addAll(pageItems));

          _chatController.add(allChats);

          if (currentRequestIndex == _allPagedResults.length - 1) {
            _lastDocument = snapshot.docs.last;
          }

          _hasMoreData = generalChats.length == chatLimit;
        } else {
          if (_lastDocument == null) {
            _chatController.add([]);
          }
        }
      },
    );
  }

  void requestMoreData(String groupId) => _requestMessages(groupId);

  Future<void> sendMessage({
    bool? isJobChat,
    required String content,
    required String timestamp,
    required MessageType type,
    required String groupId,
    required String idFrom,
    required String idTo,
    required String myProfileImg,
    required String myName,
    required String profileImg,
    required String name,
    String? jobId,
    String? jobName,
    required String? mediaName,
  }) async {
    final DocumentReference convoDoc = _chatCollectionReference.doc(groupId);

    final DocumentReference messageDoc =
        convoDoc.collection(groupId).doc(timestamp);

    FirebaseFirestore.instance.runTransaction((Transaction transaction) async {
      transaction.set(
        messageDoc,
        Message(
          content: content,
          fromId: idFrom,
          toId: idTo,
          timestamp: timestamp,
          read: false,
          type: type,
          mediaName: mediaName,
        ).toJson(),
      );
    });

    convoDoc.set(Convo(
      isJobChat: isJobChat,
      lastMessage: Message(
        content: content,
        fromId: idFrom,
        toId: idTo,
        timestamp: timestamp,
        read: false,
        type: type,
        mediaName: mediaName,
      ),
      jobId: jobId,
      jobName: jobName,
      id: groupId,
      userIds: [idFrom, idTo],
      users: [
        Users(userId: idFrom, profileImg: myProfileImg, name: myName),
        Users(userId: idTo, profileImg: profileImg, name: name)
      ],
    ).toJson());

    ApiService.instance.sendNotification(
      sendNoticationReqModel: SendNoticationReqModel(
        payload: ExtraData(
          routeName: RouteName.chat,
          routePath: RoutePath.chat,
          module: AppStrings.chat,
          extraData: Payload(
            isJobChat: isJobChat,
            groupId: groupId,
            idTo: idTo,
            idFrom: idFrom,
            name: name,
            profileImg: profileImg,
            myProfileImg: myProfileImg,
            myName: myName,
            jobId: jobId,
            jobName: jobName,
          ),
        ),
        recipientUserIds: [int.tryParse(idTo) ?? 0],
        message: "$myName: ${type == MessageType.text ? content : type.name}",
        title: "New Message",
      ),
    );
    // final DocumentReference userChatListRef = firebaseFirestore
    //     .collection("dev_users")
    //     .doc(widget.idFrom)
    //     .collection("myChatList")
    //     .doc(widget.idTo);

    // final DocumentReference otherUserChatListRef = firebaseFirestore
    //     .collection("dev_users")
    //     .doc(widget.idTo)
    //     .collection("myChatList")
    //     .doc(widget.idFrom);

    // userChatListRef.set(<String, dynamic>{
    //   'lastMessage': <String, dynamic>{
    //     'fromId': widget.idFrom,
    //     'toId': widget.idTo,
    //     'timestamp': timestamp,
    //     'content': text,
    //     'read': false,
    //     'type': type.name,
    //   },
    //   "jobName": widget.jobName,
    //   "id": widget.groupID,
    //   "name": widget.name != null && widget.name != ""
    //       ? widget.name!.toUpperCase()
    //       : "",
    //   'userIds': <String>[widget.idFrom!, widget.idTo!],
    //   'users': <Map<String, String>>[
    //     {
    //       "userId": widget.idFrom!,
    //       "profileImg": widget.myProfileImg ?? "",
    //       "name": widget.myName ?? ""
    //     },
    //     {
    //       "userId": widget.idTo!,
    //       "profileImg": widget.profileImg ?? "",
    //       "name": widget.name ?? ""
    //     }
    //   ]
    // });

    // otherUserChatListRef.set(<String, dynamic>{
    //   'lastMessage': <String, dynamic>{
    //     'fromId': widget.idFrom,
    //     'toId': widget.idTo,
    //     'timestamp': timestamp,
    //     'content': text,
    //     'read': false,
    //     'type': type.name,
    //   },
    //   "jobName": widget.jobName,
    //   "id": widget.groupID,
    //   "name": widget.myName != null && widget.myName != ""
    //       ? widget.myName!.toUpperCase()
    //       : "",
    //   'userIds': <String>[widget.idFrom!, widget.idTo!],
    //   'users': <Map<String, String>>[
    //     {
    //       "userId": widget.idFrom!,
    //       "profileImg": widget.myProfileImg ?? "",
    //       "name": widget.myName ?? ""
    //     },
    //     {
    //       "userId": widget.idTo!,
    //       "profileImg": widget.profileImg ?? "",
    //       "name": widget.name ?? ""
    //     }
    //   ]
    // });
  }

  void updateMessageRead(String? groupId, String? timestamp) {
    if (groupId == null || timestamp == null) return;
    final DocumentReference documentReference = _chatCollectionReference
        .doc(groupId)
        .collection(groupId)
        .doc(timestamp);
    documentReference.update({'read': true});
  }

  Future<bool> sendFile(
    MediaInfoModel file,
      {required String groupId,
      required MessageType type,
      required String idFrom,
      required String idTo,
      required String myProfileImg,
      required String myName,
      required String profileImg,
      required String name,
      String? jobId,
      String? jobName,
      bool? isJobChat}) async {
    log("Sending file: ${file.name}");
    try {
      Reference ref = FirebaseStorage.instance
          .ref()
          .child(EnvironmentConfig.firestoreChatFilesCollection) // Root folder for chat files
          .child(groupId)
          .child('${DateTime.now().millisecondsSinceEpoch}_${file.name}');
      late UploadTask uploadTask;
      if (kIsWeb) {
        uploadTask = ref.putData(file.bytes);
      } else {
        uploadTask = ref.putFile(File(file.path ?? ""));
      }
      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();
      log("File sent: $downloadUrl");
      sendMessage(
        isJobChat: isJobChat ?? true,
        content: downloadUrl,
        timestamp: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        groupId: groupId,
        idFrom: idFrom,
        idTo: idTo,
        myProfileImg: myProfileImg,
        myName: myName,
        profileImg: profileImg,
        name: name,
        jobId: jobId,
        jobName: jobName,
        mediaName: file.name,
      );
      return true;
    } catch (e) {
      log("Error sending file: $e");
      return false;
    }
  }
}

class ChatListRepo {
  final CollectionReference _chatCollectionReference =
      FirebaseFirestore.instance.collection(EnvironmentConfig.firebaseChatsCollection);

  final StreamController<List<Convo>> _chatController =
      StreamController<List<Convo>>.broadcast();

  final List<List<Convo>> _allPagedResults = <List<Convo>>[];

  static const int chatLimit = 10;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;

  Stream<List<Convo>> listenToChatsRealTime(String userId, {bool isJobChat = true}) {
    _requestChats(userId, isJobChat: isJobChat);
    return _chatController.stream;
  }

  void _requestChats(String userId, {bool isJobChat = true}) {
    try {
      var pagechatQuery = _chatCollectionReference
          .orderBy('lastMessage.timestamp', descending: true)
          .where('userIds', arrayContains: userId)
          .where('isJobChat', isEqualTo: isJobChat)
          .withConverter<Convo>(
            fromFirestore: (snapshots, _) => Convo.fromJson(snapshots.data()!),
            toFirestore: (chatItem, _) => chatItem.toJson(),
          )
          .limit(chatLimit);

      if (_lastDocument != null) {
        pagechatQuery = pagechatQuery.startAfterDocument(_lastDocument!);
      }

      if (!_hasMoreData) return;

      var currentRequestIndex = _allPagedResults.length;

      pagechatQuery.snapshots().listen(
        (snapshot) {
          if (snapshot.docs.isNotEmpty) {
            var generalChats =
                snapshot.docs.map((snapshot) => snapshot.data()).toList();

            var pageExists = currentRequestIndex < _allPagedResults.length;

            if (pageExists) {
              _allPagedResults[currentRequestIndex] = generalChats;
            } else {
              _allPagedResults.add(generalChats);
            }

            var allChats = _allPagedResults.fold<List<Convo>>(<Convo>[],
                (initialValue, pageItems) => initialValue..addAll(pageItems));

            _chatController.add(allChats);

            if (currentRequestIndex == _allPagedResults.length - 1) {
              _lastDocument = snapshot.docs.last;
            }

            _hasMoreData = generalChats.length == chatLimit;
          } else {
            if (_lastDocument == null) {
              _chatController.add([]);
            }
          }
        },
      );
    } catch (e) {
      log(e.toString());
    }
  }

  void requestMoreData(String userId, {bool isJobChat = true}) => _requestChats(userId, isJobChat: isJobChat);

  Future<bool> blockUnblockUserInChat({
    required bool isBlocked,
    required String groupId,
    required BlockUserModel blockUserModel,
  }) async {
    if(!isBlocked) {
    FirebaseFirestore.instance.collection(EnvironmentConfig.blockUserCollection).doc(groupId).set(blockUserModel.toJson());
    } else {
      FirebaseFirestore.instance.collection(EnvironmentConfig.blockUserCollection).doc(groupId).delete();
    }
    return true;
  }

  Future<BlockUserModel?> getIsUserBlockedInChat(String groupId) async {
    final querySnapshot = await FirebaseFirestore.instance.collection(EnvironmentConfig.blockUserCollection).doc(groupId).get();
    if(querySnapshot.exists) {
      return BlockUserModel.fromJson(querySnapshot.data()!);
    }
    return null;
  }
}
