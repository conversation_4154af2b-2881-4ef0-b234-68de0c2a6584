import 'message_model.dart';

class Convo {
  Message? lastMessage;
  String? id;
  String? jobId;
  String? jobName;
  bool? isJobChat;
  List<String>? userIds;
  List<Users>? users;

  Convo({this.lastMessage, this.id, this.userIds, this.users, this.jobName, this.jobId, this.isJobChat});

  Convo.fromJson(Map<String, dynamic> json) {
    jobName = json['jobName'];
    jobId = json['jobId'];
    isJobChat = json['isJobChat'];
    lastMessage = json['lastMessage'] != null
        ? Message.fromJson(json['lastMessage'])
        : null;
    id = json['id'];
    userIds = json['userIds'].cast<String>();
    if (json['users'] != null) {
      users = <Users>[];
      json['users'].forEach((v) {
        users!.add(Users.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (lastMessage != null) {
      data['lastMessage'] = lastMessage!.toJson();
    }
    data['id'] = id;
    data['jobId'] = jobId;
    data['userIds'] = userIds;
    data['jobName'] = jobName;
    data['isJobChat'] = isJobChat;
    if (users != null) {
      data['users'] = users!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Users {
  String? userId;
  String? profileImg;
  String? name;

  Users({this.userId, this.profileImg, this.name});

  Users.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    profileImg = json['profileImg'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['profileImg'] = profileImg;
    data['name'] = name;
    return data;
  }
}
