import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/chat/data/convo_model.dart';
import 'package:the_voice_directory_flutter/features/chat/data/message_model.dart';
import 'package:the_voice_directory_flutter/features/chat/presentation/pages/messages_screen.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/get_profile_image.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../../core/api/api_params.dart';
import '../../../../core/navigation/navigation_service_impl.dart';
import '../../../../core/routes/route_names.dart';
import '../../../../core/services/hive/hive_keys.dart';
import '../../../../utils/environment_config.dart';
import '../../../common/user_data/data/user_data_model.dart';
import '../../bloc/chat_bloc.dart';
import '../../data/firebase_repo.dart';
import '../widgets/no_data_widget.dart';

class JobChatsScreen extends StatefulWidget {
  const JobChatsScreen({super.key});

  @override
  State<JobChatsScreen> createState() => _JobChatsScreenState();
}

class _JobChatsScreenState extends State<JobChatsScreen> {
  late String myUserId;
  late String myName;
  String? myProfilePic;
  final ScrollController listScrollController = ScrollController();

  final _chatListRepo = ChatListRepo();

  @override
  void initState() {
    super.initState();
    final userData = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    myUserId = userData?.id.toString() ?? "";
    myName = "${userData?.firstName} ${userData?.lastName}";
    myProfilePic = Uri.parse(userData?.profilePic ?? '').isAbsolute
        ? userData?.profilePic
        : "${EnvironmentConfig.imageBaseUrl}${userData?.profilePic}";
    listScrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (listScrollController.offset >=
            listScrollController.position.maxScrollExtent &&
        !listScrollController.position.outOfRange) {
      _chatListRepo.requestMoreData(myUserId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Convo>>(
        stream: _chatListRepo.listenToChatsRealTime(myUserId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting &&
              !snapshot.hasData) {
            return const Center(
              child: CupertinoActivityIndicator(),
            );
          }
          if (snapshot.hasData) {
            List<Convo> conversations = snapshot.data!;
            if (conversations.isNotEmpty) {
              return ConversationsScreen(
                conversations: conversations,
              );
            } else {
              return NoChatWidget();
            }
          } else if (snapshot.hasError) {
            return const Center(child: Text("Some error while loading chats"));
          } else {
            return NoChatWidget();
          }
        });
  }
}

class ConversationsScreen extends StatelessWidget {
  final List<Convo> conversations;
  const ConversationsScreen({super.key, required this.conversations});

  @override
  Widget build(BuildContext context) {
    final userData = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    return ListView.builder(
        itemCount: conversations.length,
        itemBuilder: (context, index) {
          return ChatCard(convo: conversations[index], userData: userData);
        });
  }
}

class ChatCard extends StatelessWidget {
  final Convo convo;
  final UserDataModel? userData;
  const ChatCard({super.key, required this.convo, required this.userData});

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    ColorScheme colorScheme = themeData.colorScheme;
    final CollectionReference chatCollectionReference = FirebaseFirestore
        .instance
        .collection(EnvironmentConfig.firebaseChatsCollection);
    final groupID = convo.id ?? "";
    final Users? otherUser = convo.users
        ?.where((user) => user.userId != userData?.id.toString())
        .first;
    final otherUserId = otherUser?.userId ?? "";
    final otherUserProfileImg = otherUser?.profileImg ?? "";
    final otherUserName = otherUser?.name ?? "";
    final jobName = convo.jobName ?? "";
    final jobId = convo.jobId ?? '';
    final myUserId = userData?.id?.toString();
    final myUserProfileImg = ProfileImage.getProfileImage(userData?.profilePic);
    final myUserName =
        '${userData?.firstName ?? ''} ${userData?.lastName ?? ''}';
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return InkWell(
          splashColor: Colors.white,
          hoverColor: Colors.white,
          onTap: () {
            context.read<ChatBloc>().updateChatState(
                selectedConvo: convo,
                isFromChatList: true,
                isJobChat: true,
                selectedChat: groupID);
            if (!Responsive.isDesktop(context)) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MessagesScreen(
                    groupID: groupID,
                    idFrom: myUserId,
                    jobId: jobId,
                    idTo: otherUserId,
                    myName: myUserName,
                    myProfileImg: myUserProfileImg,
                    profileImg: otherUserProfileImg,
                    name: otherUserName,
                    jobName: jobName,
                    needBackBtn: true,
                  ),
                ),
              );
            }
          },
          child: StreamBuilder<Object>(
              stream: chatCollectionReference
                  .doc(groupID)
                  .collection(groupID)
                  .where("read", isEqualTo: false)
                  .where("toId", isEqualTo: myUserId)
                  .withConverter<Message>(
                    fromFirestore: (snapshots, _) =>
                        Message.fromJson(snapshots.data()!),
                    toFirestore: (message, _) => message.toJson(),
                  )
                  .limit(10)
                  .snapshots(),
              builder: (context, snapshot) {
                int unread = 0;
                if (snapshot.hasData) {
                  QuerySnapshot<Message> data =
                      snapshot.requireData as QuerySnapshot<Message>;
                  if (data.docs.isNotEmpty) {
                    unread = data.docs.length;
                  }
                }
                return ColoredBox(
                  color: Responsive.isDesktop(context) && state.selectedConvo?.id == convo.id
                      ? colorScheme.lightGreyF5F5F5
                      : Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  height: !Responsive.isDesktop(context)
                                      ? 32.h
                                      : 44.h,
                                  width: !Responsive.isDesktop(context)
                                      ? 32.h
                                      : 44.h,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(otherUserProfileImg),
                                      fit: BoxFit.cover,
                                    ),
                                    borderRadius: BorderRadius.circular(100),
                                    border: Border.all(
                                      color: colorScheme.lightGreyD9D9D9,
                                    ),
                                  ),
                                ),
                                8.pw,
                                Expanded(
                                  child: Text24And20SemiBold(
                                    otherUserName,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    fontSize: !Responsive.isDesktop(context)
                                        ? 16.sp
                                        : 18.sp,
                                  ),
                                ),
                                if (unread > 0)
                                  CircleAvatar(
                                    radius: !Responsive.isDesktop(context)
                                        ? 12.h
                                        : 16.h,
                                    backgroundColor: colorScheme.primary,
                                    child: TextTitle14(
                                      unread > 9 ? "9+" : unread.toString(),
                                    ),
                                  ),
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 8.ph : 16.ph,
                            Row(
                              children: [
                                if (convo.lastMessage?.type !=
                                    MessageType.text) ...[
                                  Icon(
                                      convo.lastMessage?.type ==
                                              MessageType.image
                                          ? Icons.photo_outlined
                                          : convo.lastMessage?.type ==
                                                  MessageType.audio
                                              ? Icons.music_note
                                              : convo.lastMessage?.type ==
                                                      MessageType.file
                                                  ? Icons.file_present
                                                  : convo.lastMessage?.type ==
                                                          MessageType.video
                                                      ? Icons
                                                          .video_file_outlined
                                                      : Icons
                                                          .text_snippet_outlined,
                                      color: colorScheme.primaryGrey,
                                      size: !Responsive.isDesktop(context)
                                          ? 16.h
                                          : 20.h),
                                  4.pw,
                                ],
                                Expanded(
                                  child: TextTitle18And14(
                                    convo.lastMessage?.type == MessageType.text
                                        ? convo.lastMessage?.content ?? ""
                                        : (convo.lastMessage?.type?.name ?? ""),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 12.ph : 16.ph,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Container(
                                    padding: EdgeInsets.all(8.h),
                                    decoration: BoxDecoration(
                                      color: colorScheme.hyperlinkBlueColor
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: TextBodySmall12(
                                      convo.jobName ?? "",
                                      color: colorScheme.hyperlinkBlueColor,
                                      fontSize: !Responsive.isDesktop(context)
                                          ? 12.sp
                                          : 14.sp,
                                    ),
                                  ),
                                ),
                                if (!Responsive.isDesktop(context)) 16.pw,
                                !Responsive.isDesktop(context)
                                    ? InkWell(
                                        onTap: () {
                                          NavigationServiceImpl.getInstance()!
                                              .doNavigation(
                                            context,
                                            routeName: RouteName.jobDetail,
                                            pathParameters: {
                                              Params.id: convo.jobId ?? "",
                                              Params.showApplicantTab: "false"
                                            },
                                          );
                                        },
                                        child: TextBodySmall12(
                                          "View job",
                                          color: colorScheme.hyperlinkBlueColor,
                                        ),
                                      )
                                    : const SizedBox(),
                              ],
                            ),
                            if (Responsive.isDesktop(context)) ...[
                              16.ph,
                              InkWell(
                                onTap: () {
                                  NavigationServiceImpl.getInstance()!
                                      .doNavigation(
                                    context,
                                    routeName: RouteName.jobDetail,
                                    pathParameters: {
                                      Params.id: convo.jobId ?? "",
                                      Params.showApplicantTab: "false"
                                    },
                                  );
                                },
                                child: Align(
                                  alignment: Alignment.center,
                                  child: Text24And20SemiBold(
                                    "View job",
                                    color: colorScheme.hyperlinkBlueColor,
                                    fontSize: 18.sp,
                                  ),
                                ),
                              )
                            ]
                          ],
                        ),
                      ),
                      Divider(
                        color: colorScheme.blackE8E8E8,
                        height: 1.h,
                      )
                    ],
                  ),
                );
              }),
        );
      },
    );
  }
}
