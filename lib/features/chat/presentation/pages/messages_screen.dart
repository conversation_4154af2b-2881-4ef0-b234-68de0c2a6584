import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/chat/bloc/chat_bloc.dart';
import 'package:the_voice_directory_flutter/utils/date_time_methods.dart';
import 'package:the_voice_directory_flutter/utils/environment_config.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import '../../../../core/api/api_params.dart';
import '../../../../core/navigation/navigation_service_impl.dart';
import '../../../../core/routes/route_names.dart';
import '../../../../utils/common_models/media_info_model.dart';
import '../../../../utils/custom_toast.dart';
import '../../../../utils/pick_image.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/back_button.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../report/bloc/report_bloc.dart';
import '../../../report/bloc/report_state.dart';
import '../../data/block_user_model.dart';
import '../../data/firebase_repo.dart';
import '../../data/message_model.dart';
import '../widgets/block_report_popupmenu_widget.dart';
import '../widgets/message_widget.dart';
import '../widgets/no_data_widget.dart';
import '../widgets/uploaded_file_widget.dart';

class MessagesScreen extends StatefulWidget {
  final String? jobName;
  final String? groupID;
  final String? jobId;
  final String? name;
  final String? idFrom;
  final String? idTo;
  final String? profileImg;
  final String? myProfileImg;
  final String? myName;
  final bool? needBackBtn;
  const MessagesScreen(
      {super.key,
      this.groupID,
      this.name,
      this.idFrom,
      this.idTo,
      this.profileImg,
      this.myProfileImg,
      this.myName,
      this.needBackBtn,
      this.jobName,
      this.jobId});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  late bool needBackBtn;
  late FocusNode messageFocusNode;
  bool isTyping = false;
  MediaInfoModel? _selectedImage;
  MediaInfoModel? _selectedFile;
  final TextEditingController textEditingController = TextEditingController();
  final ScrollController listScrollController = ScrollController();
  FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  final _fireStoreRepository = MessagesRepository();
  String blockGroupId = "";

  Widget buildMessages() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ColoredBox(
            color: Responsive.isDesktop(context)
                ? Theme.of(context).colorScheme.lightGreyF5F5F5
                : Theme.of(context).colorScheme.onPrimary,
            child: SizedBox(
              height: Responsive.isDesktop(context) ? 84.h : 64.h,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        needBackBtn ? CustomBackButton() : const SizedBox(),
                        Container(
                          height: !Responsive.isDesktop(context) ? 32.h : 44.h,
                          width: !Responsive.isDesktop(context) ? 32.h : 44.h,
                          margin: EdgeInsets.only(left: 12.w),
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(widget.profileImg ?? ""),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.circular(44),
                            border: Border.all(
                              color:
                                  Theme.of(context).colorScheme.lightGreyD9D9D9,
                            ),
                          ),
                        ),
                        12.pw,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text24And20SemiBold(
                              widget.name ?? "",
                              fontSize: !Responsive.isDesktop(context)
                                  ? 16.sp
                                  : 18.sp,
                            ),
                            if (widget.jobName != null &&
                                widget.jobId != null &&
                                widget.jobName != "ND" &&
                                widget.jobId != "ND")
                              InkWell(
                                onTap: () {
                                  NavigationServiceImpl.getInstance()!
                                      .doNavigation(
                                    context,
                                    routeName: RouteName.jobDetail,
                                    pathParameters: {
                                      Params.id: widget.jobId ?? "",
                                      Params.showApplicantTab: "false"
                                    },
                                  );
                                },
                                child: TextTitle18And14(
                                  widget.jobName ?? "",
                                  decoration: TextDecoration.underline,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .hyperlinkBlueColor,
                                  decorationColor: Theme.of(context)
                                      .colorScheme
                                      .hyperlinkBlueColor,
                                ),
                              )
                          ],
                        ),
                      ],
                    ),
                    BlocListener<ReportBloc, ReportState>(
                      listener: (_, state) {
                        if (state is ReportLoadingState) {
                          Dialogs.showOnlyLoader(context);
                        }
                        if (state is ReportSuccessState) {
                          context.pop();
                          CustomToast.show(
                              context: context,
                              isSuccess: true,
                              message: "User reported successfully");
                        }
                        if (state is ReportErrorState) {
                          context.pop();
                          CustomToast.show(
                              context: context, message: state.errorMsg);
                        }
                      },
                      child: StreamBuilder(
                        stream: FirebaseFirestore.instance
                            .collection(EnvironmentConfig.blockUserCollection)
                            .doc(blockGroupId)
                            .snapshots(),
                        builder: (context, blocUserState) {
                          if (blocUserState.hasData && blocUserState.requireData.exists) {
                            DocumentSnapshot<Map<String, dynamic>> data = blocUserState.requireData;
                            BlockUserModel blocUserModel = BlockUserModel.fromJson(data.data()!);
                            if (blocUserModel.blockedBy == widget.idFrom) {
                              return BlockReportPopupmenuWidget(
                                idTo: widget.idTo ?? "",
                                idFrom: widget.idFrom ?? "",
                                groupId: blockGroupId,
                                blocUserModel: blocUserModel,
                              );
                            }
                            return const SizedBox.shrink();
                          }
                          return BlockReportPopupmenuWidget(
                            idTo: widget.idTo ?? "",
                            idFrom: widget.idFrom ?? "",
                            groupId: blockGroupId,
                          );
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).colorScheme.blackE8E8E8,
                    width: 1.w,
                  ),
                  left: Responsive.isDesktop(context)
                      ? BorderSide(
                          color: Theme.of(context).colorScheme.blackE8E8E8,
                          width: 0.5.w,
                        )
                      : BorderSide.none,
                ),
              ),
              child: StreamBuilder<List<Message>>(
                  stream: _fireStoreRepository.listenToChatsRealTime(widget.groupID!),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting &&
                        !snapshot.hasData) {
                      return const Center(
                        child: CupertinoActivityIndicator(),
                      );
                    }
                    if (snapshot.hasData) {
                      List<Message> data = snapshot.requireData;
                      if (data.isNotEmpty) {
                        return Column(
                          children: [
                            Expanded(
                              child: ListView.builder(
                                // The itemBuilder now returns a Column potentially containing a separator and the message
                                itemBuilder: (BuildContext context, int index) {
                                  final currentMessage = data[index];
                                  final currentTimestamp =
                                      DateTimeMethods.parseTimestamp(
                                          currentMessage.timestamp);

                                  // Determine if we need to show a date separator
                                  bool showDateSeparator = false;
                                  if (currentTimestamp != null) {
                                    if (index == data.length - 1) {
                                      // Always show separator for the very first message displayed (oldest)
                                      showDateSeparator = true;
                                    } else {
                                      // Compare with the *next* message in the original list (which is the *previous* one chronologically because of reverse=true)
                                      final previousMessage = data[index + 1];
                                      final previousTimestamp =
                                          DateTimeMethods.parseTimestamp(
                                              previousMessage.timestamp);
                                      if (previousTimestamp != null &&
                                          !DateTimeMethods.isSameDay(
                                              currentTimestamp,
                                              previousTimestamp)) {
                                        showDateSeparator = true;
                                      }
                                    }
                                  }

                                  // Build the list of widgets for this item
                                  List<Widget> itemWidgets = [];

                                  // Add separator if needed
                                  if (showDateSeparator &&
                                      currentTimestamp != null) {
                                    itemWidgets.add(
                                        _buildDateSeparator(currentTimestamp));
                                  }

                                  // Add the actual message bubble
                                  itemWidgets.add(buildMessageItem(
                                      index,
                                      currentMessage,
                                      currentTimestamp)); // Pass timestamp for bubble
                                  // Return a Column containing the separator (if any) and the message
                                  return Column(
                                    children: itemWidgets,
                                  );
                                },
                                itemCount: data.length,
                                reverse: true,
                                shrinkWrap: true,
                                controller: listScrollController,
                              ),
                            ),
                            buildSendingFile()
                          ],
                        );
                      } else {
                        return const NoChatWidget(
                          image: AppImages.noChatImage,
                          text: AppStrings.sayHiToStartConversation,
                        );
                      }
                    } else if (snapshot.hasError) {
                      return const Text("Error");
                    } else {
                      return const NoChatWidget(
                        image: AppImages.noChatImage,
                        text: AppStrings.sayHiToStartConversation,
                      );
                    }
                  }),
            ),
          ),
        ],
      ),
    );
  }

  // Widget to build the date separator
  Widget _buildDateSeparator(DateTime date) {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        margin: const EdgeInsets.symmetric(vertical: 10), // Add some margin
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.lightBlueECEFFF,
          borderRadius: BorderRadius.circular(12.r),
          border:
              Border.all(color: Theme.of(context).colorScheme.lightBlueD2D9FF),
        ),
        child: TextBodySmall12(
          DateTimeMethods.formatDateSeparator(date),
          fontSize: 14.sp,
        ),
      ),
    );
  }

  Widget buildSendingFile() {
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        if (state.isSendingFile) {
          return Align(
            alignment: Alignment.centerRight,
            child: Container(
              height: 196.h,
              width: 220.w,
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.w),
              margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.lightBlueDFE9FF,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                  bottomLeft: Radius.circular(12.r),
                ),
              ),
              child: Center(
                child: SizedBox(
                  height: 32.h,
                  width: 32.h,
                  child: const CircularProgressIndicator.adaptive(),
                ),
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  // Renamed buildItem to buildMessageItem for clarity
  // Now accepts the parsed DateTime for convenience
  Widget buildMessageItem(int index, Message message, DateTime? createdAt) {
    if (!(message.read ?? false) &&
        message.toId != null &&
        message.toId == widget.idFrom /* My User Id*/) {
      _fireStoreRepository.updateMessageRead(widget.groupID, message.timestamp);
    }

    // --- My Message (Right) ---
    if (message.fromId == widget.idFrom /* my user ID */) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          Flexible(
            child: Container(
              padding: EdgeInsets.all(8.w),
              margin: EdgeInsets.only(
                top: 8.w,
                bottom: 8.w,
                left: !Responsive.isDesktop(context) ? 32.w : 100.w,
                right: 20.w,
              ),
              decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .lightBlueDFE9FF, // Use your theme color
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                    bottomLeft: Radius.circular(12.r),
                  )),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  MessageWidget(
                    message: message,
                    isSender: true,
                  ),
                  8.ph,
                  if (createdAt != null)
                    TextBodySmall12(
                      DateFormat.Hm().format(createdAt),
                      fontSize: !Responsive.isDesktop(context) ? 12.sp : 14.sp,
                    ),
                ],
              ),
            ),
          ),
        ],
      );
    }
    // --- Peer Message (Left) ---
    else {
      return Row(
        // Use Row for alignment
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Flexible(
            // Ensures bubble doesn't overflow horizontally
            child: Container(
              padding: EdgeInsets.all(8.w),
              margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
              decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .white, // Use your theme color
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                    bottomRight: Radius.circular(12.r),
                  ),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.lightGreyE8ECF4,
                  )),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  MessageWidget(message: message),
                  8.ph,
                  if (createdAt != null)
                    TextBodySmall12(
                      DateFormat.Hm().format(createdAt),
                      fontSize: !Responsive.isDesktop(context) ? 12.sp : 14.sp,
                    ),
                ],
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget buildInput() {
    return StreamBuilder(
    stream: FirebaseFirestore.instance.collection(EnvironmentConfig.blockUserCollection).doc(blockGroupId).snapshots(), 
    builder: (context, snapshot) {
      if(snapshot.hasData && snapshot.requireData.exists){
        DocumentSnapshot<Map<String, dynamic>> data = snapshot.requireData;
        BlockUserModel blocUserModel = BlockUserModel.fromJson(data.data()!);
        if(data.data() != null){
          return Container(
            alignment: Alignment.center,
            width: double.infinity,
            color: Theme.of(context).colorScheme.lightGreyA9ADB3,
            padding: EdgeInsets.all(16.w),
            child: TextTitle18And14(
              blocUserModel.blockedBy == widget.idFrom
                  ? "You have blocked ${widget.name}"
                  : "You have been blocked by ${widget.name}",
            ),
          );
        }
        return Container();
      } 
        return Padding(
          padding: EdgeInsets.all(16.w),
          child: _selectedImage != null || _selectedFile != null
              ? Container(
                  padding: EdgeInsets.all(12.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                        color: Theme.of(context).colorScheme.lightGreyD9D9D9,
                        width: 1.2.w),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            if (_selectedImage != null)
                              Image.memory(
                                _selectedImage!.bytes,
                                height: 120.h,
                                width: 120.h,
                                fit: BoxFit.cover,
                              ),
                            if (_selectedFile != null)
                              UploadedFileWidget(file: _selectedFile!),
                            Positioned(
                              top: -4,
                              right: 4,
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _selectedImage = null;
                                    _selectedFile = null;
                                  });
                                },
                                child: Container(
                                  padding: EdgeInsets.all(2.h),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .darkgrey494949,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    color: Theme.of(context).colorScheme.white,
                                    size: 16.h,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          if (_selectedFile != null) {
                            sendFile(_selectedFile!,
                                messageType: getMessageType(_selectedFile!));
                          } else if (_selectedImage != null) {
                            sendFile(_selectedImage!,
                                messageType: MessageType.image);
                          }
                        },
                        child: SvgPicture.asset(
                          AppImages.sendMessageIc,
                          height: 24.h,
                          width: 24.w,
                        ),
                      ),
                    ],
                  ),
                )
              : AppTextFormField(
                  focusNode: messageFocusNode,
                  maxLines: 3,
                  minLines: 1,
                  hintText: AppStrings.typeYourMessageHere,
                  controller: textEditingController,
                  // enabled: _chatImage != null ? false : true,
                  textAlignVertical: TextAlignVertical.center,
                  textCapitalization: TextCapitalization.sentences,
                  fillColor: Theme.of(context).colorScheme.white,
                  filled: true,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (value) {
                    checkAndSendMessage();
                    messageFocusNode.requestFocus();
                  },
                  suffixIcon: !isTyping
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            InkWell(
                              onTap: () async {
                                ImagePickerService imagePickerService =
                                    ImagePickerService();
                                final pickedFile = await imagePickerService
                                    .pickAnyFile(context);
                                if (pickedFile != null) {
                                  setState(() {
                                    _selectedFile = pickedFile;
                                  });
                                }
                              },
                              child: SvgPicture.asset(
                                AppImages.attachmentIc,
                                height: 24.h,
                                width: 24.w,
                              ),
                            ),
                            12.pw,
                            InkWell(
                              onTap: () async {
                                ImagePickerService imagePickerService =
                                    ImagePickerService();
                                final pickedImage = await imagePickerService
                                    .pickImageWithOptions(
                                  context: context,
                                  // bottomSheetBackgroundColor: Colors.grey[200], // Optional: customize color
                                );
                                if (pickedImage != null) {
                                  setState(() {
                                    _selectedImage = pickedImage;
                                  });
                                }
                              },
                              child: SvgPicture.asset(
                                AppImages.addImageIc,
                                height: 24.h,
                                width: 24.w,
                              ),
                            ),
                            12.pw
                          ],
                        )
                      : InkWell(
                          onTap: () {
                            checkAndSendMessage();
                          },
                          child: Padding(
                            padding: EdgeInsets.only(right: 12.w),
                            child: SvgPicture.asset(
                              AppImages.sendMessageIc,
                              height: 24.h,
                              width: 24.w,
                            ),
                          ),
                        ),
                  onChanged: (value) {
                    if (value.isNotEmpty && !isTyping) {
                      setState(() {
                        isTyping = true;
                      });
                    }
                    if (value.isEmpty) {
                      setState(() {
                        isTyping = false;
                      });
                    }
                  },
                  // focusNode: focusNode,
                ),
        );
    },);
  }

  void checkAndSendMessage() {
    if (textEditingController.text.trim() != "") {
      String text = textEditingController.text.trim();
      _fireStoreRepository.sendMessage(
        content: text,
        isJobChat: _chatState.state.isJobChat,
        timestamp: DateTime.now().millisecondsSinceEpoch.toString(),
        type: MessageType.text,
        mediaName: null,
        jobId: widget.jobId,
        jobName: widget.jobName,
        groupId: widget.groupID!,
        idFrom: widget.idFrom!,
        idTo: widget.idTo!,
        myProfileImg: widget.myProfileImg!,
        myName: widget.myName!,
        profileImg: widget.profileImg!,
        name: widget.name!,
      );
      textEditingController.clear();
    } else {
      textEditingController.clear();
    }
    setState(() {
      isTyping = false;
    });
  }

  Future<void> sendFile(MediaInfoModel file,
      {required MessageType messageType}) async {
    _chatState.sendFile(file,
        messageType: messageType,
        groupId: widget.groupID ?? "",
        idFrom: widget.idFrom ?? "",
        idTo: widget.idTo ?? "",
        myProfileImg: widget.myProfileImg ?? "",
        myName: widget.myName ?? "",
        profileImg: widget.profileImg ?? "",
        name: widget.name ?? "",
        jobId: widget.jobId ?? "",
        jobName: widget.jobName ?? "");
    setState(() {
      _selectedImage = null;
      _selectedFile = null;
    });
  }

  MessageType getMessageType(MediaInfoModel file) {
    final ext = file.ext.toLowerCase();
    if (ext == "pdf" ||
        ext == "doc" ||
        ext == "docx" ||
        ext == "txt" ||
        ext == "csv") {
      return MessageType.file;
    } else if (ext == "jpg" || ext == "jpeg" || ext == "png") {
      return MessageType.image;
    } else if (ext == "mp3" || ext == "wav") {
      return MessageType.audio;
    } else if (ext == "mp4" ||
        ext == "mov" ||
        ext == "avi" ||
        ext == "mkv" ||
        ext == "webm") {
      return MessageType.video;
    } else {
      return MessageType.text;
    }
  }
  late ChatBloc _chatState;

  @override
  void initState() {
    super.initState();
    _chatState = context.read<ChatBloc>();
    messageFocusNode = FocusNode();
    int? idFrom = int.tryParse(widget.idFrom ?? "0");
    int? idTo = int.tryParse(widget.idTo ?? "0");
    if(idFrom != null && idTo != null && idFrom > idTo){
      blockGroupId = "${idTo}_$idFrom";
    } else{
      blockGroupId = "${idFrom}_$idTo";
    }
    listScrollController.addListener(_scrollListener);
    needBackBtn = widget.needBackBtn ?? true;
  }

  @override
  void dispose() {
    messageFocusNode.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (listScrollController.offset >=
            listScrollController.position.maxScrollExtent &&
        !listScrollController.position.outOfRange) {
      _fireStoreRepository.requestMoreData(widget.groupID!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: ColoredBox(
          color: Responsive.isDesktop(context)
              ? Theme.of(context).colorScheme.lightGreyF5F5F5
              : Theme.of(context).colorScheme.onPrimary,
          child: BlocBuilder<ChatBloc, ChatState>(
            builder: (context, chatState) {
              return Column(
                children: <Widget>[
                  Expanded(
                    child: Responsive.isDesktop(context)
                        ? chatState.selectedChat == "" &&
                                chatState.isFromChatList
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      AppImages.startChatImage,
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.4,
                                    ),
                                    const Text(
                                        "Tap on a chat to see the conversation"),
                                  ],
                                ),
                              )
                            : Column(
                                children: [
                                  buildMessages(),
                                  buildInput(),
                                ],
                              )
                        : Column(
                            children: [
                              buildMessages(),
                              buildInput(),
                            ],
                          ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
