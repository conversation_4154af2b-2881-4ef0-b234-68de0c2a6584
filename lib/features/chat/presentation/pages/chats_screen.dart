import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/chat/presentation/pages/job_chats_screen.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import '../../../../utils/environment_config.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/common_app_bar.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/chat_bloc.dart';
import '../../data/firebase_repo.dart';
import '../widgets/no_data_widget.dart';
import 'ancillary_chats_screen.dart';
import 'messages_screen.dart';

class ChatsScreen extends StatefulWidget {
  const ChatsScreen({super.key});

  @override
  State<ChatsScreen> createState() => _ChatsScreenState();
}

class _ChatsScreenState extends State<ChatsScreen>
    with TickerProviderStateMixin {
  late String myUserId;
  late String myName;
  String? myProfilePic;
  UserDataModel? userData;
  final ScrollController listScrollController = ScrollController();
  late TabController _tabController;
  final _chatListRepo = ChatListRepo();

  @override
  void initState() {
    super.initState();
    userData = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    myUserId = userData?.id.toString() ?? "";
    myName = "${userData?.firstName} ${userData?.lastName}";
    myProfilePic = Uri.parse(userData?.profilePic ?? '').isAbsolute
        ? userData?.profilePic
        : "${EnvironmentConfig.imageBaseUrl}${userData?.profilePic}";
    listScrollController.addListener(_scrollListener);
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  void _scrollListener() {
    if (listScrollController.offset >=
            listScrollController.position.maxScrollExtent &&
        !listScrollController.position.outOfRange) {
      _chatListRepo.requestMoreData(myUserId);
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textStyle = theme.textTheme;
    return Scaffold(
      appBar: Responsive.isDesktop(context)
          ? null
          : const CommonAppBar(title: AppStrings.chats),
      body: Padding(
          padding: !Responsive.isDesktop(context)
            ? EdgeInsets.zero
            : EdgeInsets.symmetric(
                horizontal: 80.h,
                  vertical: 24.h,
              ),
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  decoration: Responsive.isDesktop(context) ? BoxDecoration(
                  color: colorScheme.onPrimary,
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.blackE8E8E8,
                        blurRadius: 10,
                        spreadRadius: 1,
                      ),
                    ],
                  ) : null,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: Responsive.isDesktop(context) ? 8.w : 0, vertical: 12.w),
                        child: SizedBox(
                          height: Responsive.isDesktop(context) ? 56.h : 44.h,
                          child: TabBar(
                            splashFactory: NoSplash.splashFactory,
                            splashBorderRadius: BorderRadius.circular(4.r),
                            padding: EdgeInsets.symmetric(
                              horizontal: !Responsive.isDesktop(context) ? 16.w : 0,
                            ),
                            dividerColor: Colors.transparent,
                            dividerHeight: 0,
                            labelColor: colorScheme.primaryGrey,
                            indicatorSize: TabBarIndicatorSize.tab,
                            indicator: BoxDecoration(
                              border: Border(
                                left: _tabController.index == 0
                                    ? BorderSide(
                                        color: colorScheme.primary,
                                        width: 1.w,
                                      )
                                    : BorderSide.none,
                                right: _tabController.index == 1
                                    ? BorderSide(
                                        color: colorScheme.primary,
                                        width: 1.w,
                                      )
                                    : BorderSide.none,
                              ),
                              color: colorScheme.primary.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            labelStyle: textStyle.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontSize:
                                  Responsive.isDesktop(context) ? 20.sp : 16.sp,
                              fontWeight: FontWeight.w500,
                              overflow: TextOverflow.ellipsis,
                            ),
                            tabs: [
                              Tab(
                                text: "Jobs",
                              ),
                              Tab(
                                text: userData?.role == UserType.ancillaryService ? "Messages" : "Ancillaries",
                              ),
                            ],
                            onTap: (index) {
                              context.read<ChatBloc>().updateChatState(isJobChat: index == 0,);
                            },
                            controller: _tabController,
                          ),
                        ),
                      ),
                      Expanded(
                          child: Container(
                        decoration: Responsive.isDesktop(context) ? BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              color: colorScheme.blackE8E8E8,
                              width: 1.w,
                            ),
                            right: BorderSide(
                              color: colorScheme.blackE8E8E8,
                              width: 0.5.w,
                            ),
                          ),
                        ) : null,
                        child: TabBarView(
                          controller: _tabController,
                          children: const [
                            JobChatsScreen(),
                            AncillaryChatsScreen(),
                          ],
                        ),
                      ))
                    ],
                  ),
                ),
              ),
              !Responsive.isDesktop(context)
                  ? const SizedBox()
                  : Expanded(
                      flex: 2,
                      child: BlocBuilder<ChatBloc, ChatState>(
                          builder: (context, chatState) {
                        if (chatState.selectedConvo != null &&
                            chatState.selectedConvo!.id != null) {
                          String otherUserProfileImg = "";
                          String otherUserName = "";
                          String otherUserId = "";
                          String jobName = chatState.selectedConvo?.jobName ?? "";
                          for (var i = 0; i < chatState.selectedConvo!.users!.length; i++) {
                            if (chatState.selectedConvo!.users![i].userId != myUserId) {
                              otherUserProfileImg = chatState.selectedConvo!.users![i].profileImg!;
                              otherUserName = chatState.selectedConvo!.users![i].name!;
                              otherUserId = chatState.selectedConvo!.users![i].userId!;
                              break;
                            }
                          }
                          String groupID = chatState.selectedConvo!.id!;
                          return MessagesScreen(
                            key: ValueKey(groupID),
                            needBackBtn: false,
                            jobId: chatState.selectedConvo?.jobId,
                            groupID: groupID,
                            idFrom: myUserId,
                            idTo: otherUserId,
                            jobName: jobName,
                            myName: myName,
                            myProfileImg: myProfilePic,
                            name: otherUserName,
                            profileImg: otherUserProfileImg,
                          );
                          // In case, user tap on searched result
                        } else {
                          return ColoredBox(
                            color: Responsive.isDesktop(context)
                                ? Theme.of(context).colorScheme.lightGreyF5F5F5
                                : Theme.of(context).colorScheme.onPrimary,
                            child: const NoChatWidget(
                              image: AppImages.noChatImage,
                              text: AppStrings.tapOnChatToSeeConversation,
                            ),
                          );
                        }
                      })),
            ],
          )
      ),
    );
  }
}
