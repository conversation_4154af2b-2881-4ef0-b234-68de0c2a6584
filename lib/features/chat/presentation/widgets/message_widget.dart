import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/common_audio_player.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/video_player_widget.dart';
import 'package:widget_zoom/widget_zoom.dart';
import '../../../../utils/download_file_manager.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../data/message_model.dart';

class MessageWidget extends StatelessWidget {
  final Message message;
  final bool isSender;
  const MessageWidget(
      {super.key, required this.message, this.isSender = false});

  @override
  Widget build(BuildContext context) {
    return _buildMessageWidget(context, message);
  }

  Widget _buildMessageWidget(BuildContext context, Message message) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (message.type) {
      case MessageType.text:
        return TextTitle18And14(
          message.content ?? "",
          fontWeight: FontWeight.w400,
          fontFamily: 'NotoSans-Regular',
        );
      case MessageType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: WidgetZoom(
            heroAnimationTag: 'image_${message.content}',
            zoomWidget: Image.network(
              message.content ?? "",
              fit: BoxFit.cover,
              height: 220,
              width: 220,
            ),
          ),
        );
      case MessageType.file:
        return Container(
          padding: EdgeInsets.all(8.h),
          decoration: BoxDecoration(
            color: colorScheme.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.file_present,
                  size: 24.h, color: colorScheme.secondary),
              8.pw,
              Flexible(
                child: TextTitle18And14(
                  message.mediaName ?? "",
                ),
              ),
              if (!isSender) ...[
                16.pw,
                InkWell(
                  onTap: () async {
                    await DownloadFileManager.downloadFile(context,
                        message.content ?? "", message.mediaName ?? "");
                  },
                  child: Icon(
                    Icons.download_for_offline_outlined,
                    size: 24.h,
                    color: colorScheme.secondary,
                  ),
                ),
              ]
            ],
          ),
        );
      case MessageType.audio:
        return CommonAudioPlayer(
            audioUrl: message.content ?? "", colorScheme: colorScheme);
      case MessageType.video:
        return VideoPlayerWidget(
          videoUrl: message.content ?? "",
          showVideoName: false,
          height: 220,
          width: 220,
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
