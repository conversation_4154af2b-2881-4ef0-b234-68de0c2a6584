import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../report/bloc/report_bloc.dart';
import '../../data/block_user_model.dart';
import '../../data/firebase_repo.dart';

class BlockReportPopupmenuWidget extends StatelessWidget {
  final String idTo;
  final String idFrom;
  final String groupId;
  final BlockUserModel? blocUserModel;
  const BlockReportPopupmenuWidget(
      {super.key,
      required this.idTo,
      required this.idFrom,
      required this.groupId,
      this.blocUserModel});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
        icon: Icon(
          Icons.more_vert,
          color: Theme.of(context).colorScheme.black,
        ),
        color: Theme.of(context).colorScheme.white,
        offset: Offset(0, 40.h),
        popUpAnimationStyle: AnimationStyle(
          curve: Curves.easeInCirc,
          duration: Duration(milliseconds: 200),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        padding: EdgeInsets.zero,
        itemBuilder: (context) => [
              PopupMenuItem(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                ),
                onTap: () {
                  TextEditingController commentController =
                      TextEditingController();
                  Dialogs.showCommonDialogWithComment(
                    context: context,
                    title: AppStrings.reportUser,
                    message: AppStrings.areYouSureToReport,
                    primaryButtonText: AppStrings.report,
                    commentHint: AppStrings.pleaseEnterReasonForReporting,
                    onPrimaryButtonTap: () {
                      context.read<ReportBloc>().report(int.parse(idTo), commentController.text.trim());
                      context.pop();
                    },
                    commentController: commentController,
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.report_gmailerrorred,
                        color: Theme.of(context).colorScheme.black),
                    8.pw,
                    TextTitle18And14("Report"),
                  ],
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.zero,
                height: 1,
                child: Divider(
                  color: Theme.of(context).colorScheme.lightGreyD9D9D9,
                  height: 1,
                ),
              ),
              PopupMenuItem(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                ),
                onTap: () async {
                  bool isBlocked = blocUserModel != null && blocUserModel!.blockedBy == idFrom;
                  Dialogs.showCommonDialog(
                    context: context,
                    title: isBlocked ? AppStrings.unblockUser : AppStrings.blockUser,
                    message: isBlocked ? AppStrings.areYouSureYouWantToUnblock : AppStrings.areYouSureYouWantToBlock,
                    primaryButtonText: isBlocked ? AppStrings.yesUnblock : AppStrings.yesBlock,
                    primaryButtonColor: const Color(0xFF525252),
                    onPrimaryButtonTap: () {
                      ChatListRepo().blockUnblockUserInChat(
                      isBlocked: isBlocked,
                      groupId: groupId,
                      blockUserModel: BlockUserModel(
                        blockedUserId: idTo,
                        blockedBy: idFrom,
                      ));
                      context.pop();
                    },
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.block,
                      color: blocUserModel != null && blocUserModel!.blockedBy == idFrom
                          ? Theme.of(context).colorScheme.black
                          : Theme.of(context).colorScheme.error,
                    ),
                    8.pw,
                    TextTitle18And14(
                      blocUserModel != null && blocUserModel!.blockedBy == idFrom ? "Unblock" : "Block",
                      color: blocUserModel != null && blocUserModel!.blockedBy == idFrom
                          ? Theme.of(context).colorScheme.black
                          : Theme.of(context).colorScheme.error,
                    ),
                  ],
                ),
              ),
            ]);
  }
}
