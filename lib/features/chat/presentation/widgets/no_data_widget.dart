import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class NoChatWidget extends StatelessWidget {
  final String image;
  final String text;
  const NoChatWidget({
    super.key,
    this.image = AppImages.startChatImage,
    this.text = AppStrings.noChatAvailable,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            image,
            height: 0.3.sh,
          ),
          24.ph,
          TextTitle18And14(
            text,
          ),
        ],
      ),
    );
  }
}
