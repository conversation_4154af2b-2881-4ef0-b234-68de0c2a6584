import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../../utils/common_models/media_info_model.dart';

class UploadedFileWidget extends StatefulWidget {
  final MediaInfoModel file;
  const UploadedFileWidget({super.key, required this.file});

  @override
  State<UploadedFileWidget> createState() => _UploadedFileWidgetState();
}

class _UploadedFileWidgetState extends State<UploadedFileWidget> {
  bool get isImage => ['jpg', 'jpeg', 'png'].contains(widget.file.ext.toLowerCase());

  bool get isVideo =>
      ['mp4', 'mov', 'avi', 'mkv', 'webm'].contains(widget.file.ext.toLowerCase());

  bool get isAudio => ['mp3', 'wav'].contains(widget.file.ext.toLowerCase());

  bool get isPdf => ['pdf'].contains(widget.file.ext.toLowerCase());

  bool get isDoc => ['doc', 'docx'].contains(widget.file.ext.toLowerCase());

  bool get isText => ['txt', 'csv'].contains(widget.file.ext.toLowerCase());

  @override
  Widget build(BuildContext context) {
    return getFileWidget;
  }

  Widget get getFileWidget {
    if (isImage) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Image.memory(
          widget.file.bytes,
          height: 120.h,
          width: 120.h,
          fit: BoxFit.cover,
        ),
      );
    } else if (isVideo) {
      return getDocWidget(
        icon: Icons.video_file_outlined,
        color: Theme.of(context).colorScheme.secondary,
      );
    } else if (isAudio) {
      return getDocWidget(
        icon: Icons.music_note_outlined,
        color: Theme.of(context).colorScheme.secondary,
      );
    } else if (isPdf) {
      return getDocWidget(
        icon: Icons.picture_as_pdf_outlined,
        color: Theme.of(context).colorScheme.error,
      );
    } else if (isDoc) {
      return getDocWidget(
        icon: Icons.description_outlined,
        color: Theme.of(context).colorScheme.secondary,
      );
    } else if (isText) {
      return getDocWidget(
        icon: Icons.text_snippet_outlined,
        color: Theme.of(context).colorScheme.secondary,
      );
    }
    return getDocWidget(
      icon: Icons.file_present,
      color: Theme.of(context).colorScheme.secondary,
    );
  }

  Widget getDocWidget({IconData? icon, Color? color}) {
    return Container(
      padding: EdgeInsets.all(8.h),
      margin: EdgeInsets.only(right: 16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 24.h, color: color),
          8.pw,
          Flexible(
            child: TextTitle18And14(
              widget.file.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
