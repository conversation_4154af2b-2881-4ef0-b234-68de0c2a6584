import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/report/bloc/report_state.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class ReportBloc extends Cubit<ReportState> {
  @override
  ReportBloc() : super(ReportInitState());
  void report(int id, String comment) async {
    emit(ReportLoadingState());
    try {
      final response = await ApiService.instance.report(id, comment);
      if (response.success) {
        emit(ReportSuccessState());
      } else {
        emit(ReportErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ReportErrorState(AppStrings.genericErrorMsg));
    }
  }
}