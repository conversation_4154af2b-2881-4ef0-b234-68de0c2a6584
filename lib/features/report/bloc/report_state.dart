import 'package:equatable/equatable.dart';

abstract class ReportState extends Equatable {}

class ReportInitState extends ReportState {
  @override
  List<Object> get props => [];
}

class ReportLoadingState extends ReportState {
  @override
  List<Object> get props => [];
}

class ReportSuccessState extends ReportState {
  @override
  List<Object> get props => [];
}

class ReportErrorState extends ReportState {
  final String errorMsg;
  ReportErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
