import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../post_job/presentation/widgets/animated_rating_star.dart';
import '../bloc/review_bloc.dart';
import '../bloc/review_state.dart';

class ReviewsListScreen extends StatefulWidget {
  final int id;
  const ReviewsListScreen({super.key, required this.id});

  @override
  State<ReviewsListScreen> createState() => _ReviewsListScreenState();
}

class _ReviewsListScreenState extends State<ReviewsListScreen> {
  @override
  void initState() {
    super.initState();
    context.read<ReviewBloc>().getReviewList(widget.id);
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<ReviewBloc, ReviewState>(
          builder: (context, state) {
            if (state is ReviewLoadingState) {
              return const Center(child: CircularProgressIndicator());
            }
            if (state is ReviewErrorState) {
              return Center(child: Text(state.errorMsg));
            }
            if (state is ReviewSuccessState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        left: !Responsive.isDesktop(context) ? 16.h : 80.h,
                        right: !Responsive.isDesktop(context) ? 16.h : 80.h,
                        top: !Responsive.isDesktop(context) ? 16.h : 42.h),
                    child: !Responsive.isDesktop(context)
                        ? Row(
                      //crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBackButton(),
                        24.ph,
                        Expanded(
                          child: TextDisplayLarge24And16(
                            AppStrings.reviews,
                            color: colorScheme.black050505,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        SizedBox(width: 32.w)
                      ],
                    ) : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBackButtonArrow(),
                        24.ph,
                        TextDisplayLarge24And16(
                          AppStrings.reviews,
                          color: colorScheme.black050505,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: state.reviewList.first.data?.isEmpty ?? true
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                           children: [
                              SvgPicture.asset(AppImages.noJobsData),
                              16.ph,
                              TextTitle18And20(
                              AppStrings.noReviewsYet,
                               color: colorScheme.textfieldTitleColor,
                                ) ],
                          ),
                        )
                      : SingleChildScrollView(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                  horizontal: !Responsive.isDesktop(context)
                                      ? 16.h
                                      : 80.h),
                            child: Column(
                              children: [
                                24.ph,
                                _buildReviewsList(state, colorScheme),
                              ],
                            ),
                          ),
                        ),
                  ),
                ],
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget _buildReviewsList(ReviewSuccessState state,ColorScheme colorScheme) {
    return ListView.separated(
      padding: EdgeInsets.only(bottom: 30.h,top: 10.h),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: state.reviewList.first.data?.length ?? 0,
      separatorBuilder: (context, index) => 24.ph,
      itemBuilder: (context, index) {
        return CommonShadowContainer(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 18.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              AnimatedRatingStar(
                                initialRating: state.reviewList.first.data?[index].rating ?? 0,
                                iconSize: 23,
                                gap: 4,
                                ignoreGestures: true,
                                filledColor: colorScheme.yellowFFC500,
                                unfilledColor: colorScheme.hintTextColor,
                              ),
                              8.pw,
                              TextTitle18And14((state.reviewList.first.data?[index].rating ?? 0).toString(),
                                color: colorScheme.black050505, fontWeight: FontWeight.w700,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SvgPicture.asset(AppImages.tagIcon,
                        height: 30.h, width: 45.w),
                  ],
                ),
                24.ph,
                ReadMoreText((state.reviewList.first.data?[index].feedback ?? 0).toString(),
                  color: colorScheme.black050505,
                ),
                20.ph,
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextTitle18And14((state.reviewList.first.data?[index].fullName ?? 0).toString(),
                          color: colorScheme.black050505, fontWeight: FontWeight.w700,
                        ),
                        8.ph,
                        TextTitle14(
                          state.reviewList.first.data?[index].createdAt != null
                            ? DateFormat('d MMM, yyyy').format(state.reviewList.first.data![index].createdAt!)
                            : 'No Date'
                        )
                      ],
                    ),
                    Spacer(),
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: colorScheme.blackE8E8E8,
                      backgroundImage: state.reviewList.first.data?[index].profilePic?.isNotEmpty == true
                          ? Uri.parse(state.reviewList.first.data![index].profilePic!).isAbsolute
                              ? NetworkImage(state.reviewList.first.data![index].profilePic!)
                              : NetworkImage("${EnvironmentConfig.imageBaseUrl}${state.reviewList.first.data![index].profilePic}")
                          : null,
                      child: state.reviewList.first.data?[index].profilePic?.isNotEmpty == true
                          ? null
                          : SvgPicture.asset("assets/images/user_icon.svg", height: 48.h, width: 48.w),
                    ),
                  ],
                ),
                15.ph,
              ],
            ),
          ),
        );
      },
    );
  }
}

class ReadMoreText extends StatefulWidget {
  final String text;
  final Color? color;
  const ReadMoreText(this.text, {super.key, this.color});

  @override
  State<ReadMoreText> createState() => _ReadMoreTextState();
}
class _ReadMoreTextState extends State<ReadMoreText> {
  bool isExpanded = false;
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final shortText = widget.text.length > 300 ? '${widget.text.substring(0, 300)}...' : widget.text;

    return Row(
      children: [
        Expanded(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: isExpanded ? widget.text : shortText,
                  style: TextStyle(
                      fontSize: !Responsive.isDesktop(context) ? 14.sp : 18.sp,
                    color: widget.color,
                      fontWeight: !Responsive.isDesktop(context)
                          ? FontWeight.w400
                          : FontWeight.w500
                  ),
                ),
                if (widget.text.length > 300) TextSpan(text: " "),
                if (widget.text.length > 300)
                  TextSpan(
                    text: isExpanded ? AppStrings.readLess : AppStrings.readMore,
                    style: TextStyle(
                        fontSize:
                            !Responsive.isDesktop(context) ? 14.sp : 18.sp,
                        color: colorScheme.hyperlinkBlueColor,
                        decoration: TextDecoration.underline),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        setState(() {
                          isExpanded = !isExpanded;
                        });
                      },
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
