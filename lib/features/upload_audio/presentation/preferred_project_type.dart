import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/project_type_submit_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/project_type_submit_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';
import 'package:the_voice_directory_flutter/widgets/welcome_to_tvd_screen.dart';

import '../../../core/services/hive/hive_keys.dart';

class PreferredProjectTypeScreen extends StatefulWidget {
  const PreferredProjectTypeScreen({super.key});

  @override
  State<PreferredProjectTypeScreen> createState() =>
      _PreferredProjectTypeScreenState();
}

class _PreferredProjectTypeScreenState
    extends State<PreferredProjectTypeScreen> {
  List<DropdownData> selectedProjectType = [];
  String? userName;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
      userName = userDataModel?.firstName;
      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Row(
          children: [
            if (Responsive.isDesktop(context))
              const Expanded(child: TVDBrandingScreen()),
            Expanded(
              child: Padding(
                padding: !Responsive.isDesktop(context)
                    ? EdgeInsets.zero
                    : EdgeInsets.all(44.0.h),
                child: Card(
                  color: Theme.of(context).colorScheme.white,
                  elevation: !Responsive.isDesktop(context) ? 0 : 8,
                  shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: !Responsive.isDesktop(context) ? 16.h : 52.h,
                      vertical: !Responsive.isDesktop(context) ? 24.h : 48.h,
                    ),
                    child: BlocBuilder<ProjectTypeSubmitBloc,
                        ProjectTypeSubmitState>(
                      builder: (context, state) {
                        if (state is ProjectTypeSubmitSuccessState &&
                            Responsive.isDesktop(context)) {
                          return WelcomeToTVDScreen(
                            title: AppStrings.welcomeToTVD(userName ?? ""),
                            message: AppStrings.welcomeToTVDDesVoice,
                            buttonText: AppStrings.continueTxt,
                            onButtonPressed: () {
                              HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                              NavigationServiceImpl.getInstance()!.doNavigation(
                                  context,
                                  useGo: true,
                                  routeName: RouteName.dashboard);
                            },
                          );
                        }
                        return Column(
                          children: [
                            if (!Responsive.isDesktop(context)) ...[
                              const Align(
                                  alignment: Alignment.centerLeft,
                                  child: CustomBackButton()),
                              16.ph,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const TextDisplayLarge36And26(
                                      AppStrings.preferredProjectType),
                                  RichText(
                                      text: TextSpan(
                                          text: '04',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall!
                                              .copyWith(fontSize: 16.sp),
                                          children: [
                                        TextSpan(
                                          text: '/05',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleSmall!
                                              .copyWith(
                                                  fontWeight: FontWeight.w400),
                                        )
                                      ])),
                                ],
                              ),
                            ],
                            if (Responsive.isDesktop(context)) ...[
                              Align(
                                  alignment: Alignment.centerRight,
                                child: RichText(
                                    text: TextSpan(
                                        text: '04',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(fontSize: 20.sp),
                                        children: [
                                      TextSpan(
                                        text: '/05',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(fontSize: 14.sp),
                                      )
                                    ])),
                              ),
                              const TextDisplayLarge36And26(
                                  AppStrings.preferredProjectType),
                            ],
                            !Responsive.isDesktop(context) ? 16.ph : 20.ph,
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: AppStrings.toFindJobsThatAlignWithYourPreferences,
                                    style: Theme.of(context).textTheme.titleSmall,
                                  ),
                                  TextSpan(
                                    text: AppStrings.chooseTheTypeOfProjectYouWantToWorkOn,
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                            BlocConsumer<StaticDataDropdownBloc, StaticDataDropdownState>(
                              listener: (context, state) {
                                if (state is StaticDataDropdownSuccessState) {
                                  selectedProjectType = List.from(state.dropDownResponseModel?.projectType ?? []);
                                  setState(() {});
                                }
                              },
                              builder: (context, state) {
                                List<DropdownData>? projectType = [];
                                if (state is StaticDataDropdownSuccessState) {
                                  projectType.addAll(state
                                          .dropDownResponseModel?.projectType ??
                                      []);
                                }
                                return CustomMultiselectDropdown(
                                  isLoading:
                                      state is StaticDataDropdownLoadingState,
                                  isError:
                                      state is StaticDataDropdownErrorState,
                                  hintText: AppStrings.projectType,
                                  titleText: AppStrings.selectProjectType,
                                  items: projectType,
                                  selectedValues: selectedProjectType,
                                  onSelectionChanged: (newSelection) {
                                    setState(() {
                                      selectedProjectType = newSelection;
                                    });
                                  },
                                );
                              },
                            ),
                            if (selectedProjectType.isNotEmpty) 16.ph,
                            Align(
                              alignment: Alignment.centerLeft,
                              child: ChipsWidget(
                                items: selectedProjectType,
                                onRemove: (item) {
                                  selectedProjectType.remove(item);
                                  setState(() {});
                                },
                              ),
                            ),
                            const Spacer(),
                            BlocListener<ProjectTypeSubmitBloc,
                                ProjectTypeSubmitState>(
                              listener: (context, state) {
                                if (state is ProjectTypeSubmitLoadingState) {
                                  Dialogs.showOnlyLoader(context);
                                }
                                if (state is ProjectTypeSubmitSuccessState) {
                                  // Show Success Message
                                  CustomToast.show(
                                      context: context,
                                      message: AppStrings.projectTypeSuccess,
                                      isSuccess: true);
                                  context.pop();
                                  if (!Responsive.isDesktop(context)) {
                                    showModalBottomSheet(
                                      context: context,
                                      isScrollControlled: true,
                                      isDismissible: false,
                                      backgroundColor: Colors.transparent,
                                      builder: (BuildContext context) {
                                        return Stack(
                                          alignment: Alignment.bottomCenter,
                                          children: [
                                            BackdropFilter(
                                              filter: ImageFilter.blur(
                                                  sigmaX: 10.0, sigmaY: 10.0),
                                              child: Container(
                                                color: Colors.black
                                                    .withOpacity(0.3),
                                              ),
                                            ),
                                            WelcomeToTVDScreen(
                                              title: AppStrings.welcomeToTVD(
                                                  userName ?? ""),
                                              message:
                                                  AppStrings.welcomeToTVDDesVoice,
                                              buttonText:
                                                  AppStrings.continueTxt,
                                              onButtonPressed: () {
                                                HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                                context.pop();
                                                NavigationServiceImpl
                                                        .getInstance()!
                                                    .doNavigation(context,
                                                        useGo: true,
                                                        routeName: RouteName
                                                            .dashboard);
                                              },
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  }
                                  return;
                                  // Navigate to Payment details page
                                  // NavigationServiceImpl.getInstance()!.doNavigation(
                                  //   context,
                                  //   routeName: RouteName.projectType,
                                  // );
                                }
                                if (state is ProjectTypeSubmitErrorState) {
                                  // Show Error Message
                                  context.pop();
                                  CustomToast.show(
                                      context: context,
                                      message: state.errorMsg,
                                      isSuccess: true);
                                }
                              },
                              child: PrimaryButton(
                                onPressed: () {
                                  if (selectedProjectType.isEmpty) {
                                    CustomToast.show(
                                      context: context,
                                      message:
                                          ValidationMsg.plsSelectProjectType,
                                    );
                                    return;
                                  }
                                  context
                                      .read<ProjectTypeSubmitBloc>()
                                      .submitProjectTypeSubmit(
                                          userInfoRequestModel:
                                              UserInfoRequestModel(
                                                  isProfileCompleted: true,
                                                  intermediateStep: 4,
                                                  projectType:
                                                      selectedProjectType
                                                          .map((project) =>
                                                              project.id)
                                                          .toList()));
                                },
                                buttonText: AppStrings.continues,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
