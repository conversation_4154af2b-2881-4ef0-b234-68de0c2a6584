import 'dart:typed_data';

import 'package:audioplayers/audioplayers.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_state.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_samples_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_samples_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../bloc/upload_audio_video_cubit.dart';

class UploadAudioScreen extends StatefulWidget {
  const UploadAudioScreen({super.key});

  @override
  State<UploadAudioScreen> createState() => _UploadAudioScreenState();
}

class _UploadAudioScreenState extends State<UploadAudioScreen> {

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocProvider(
      create: (_) => UploadAudioVideoCubit(),
      child: BlocConsumer<UploadAudioVideoCubit, UploadAudioVideoState>(
        listener: (context, state) {
          if (state.errorMessage != null && state.errorMessage!.isNotEmpty) {
            CustomToast.show(context: context, message: state.errorMessage!);
            context.read<UploadAudioVideoCubit>().resetError();
          }
        },
        builder: (context, state) {
          final cubit = context.read<UploadAudioVideoCubit>();
          return Scaffold(
            body: SafeArea(
              child: Row(
                children: [
                  if (Responsive.isDesktop(context))
                    const Expanded(child: TVDBrandingScreen()),
                  Expanded(
                    child: Padding(
                      padding: !Responsive.isDesktop(context)
                          ? EdgeInsets.zero
                          : EdgeInsets.all(44.0.h),
                      child: Card(
                        color: Theme.of(context).colorScheme.white,
                        elevation: !Responsive.isDesktop(context) ? 0 : 8,
                        shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal:
                                  !Responsive.isDesktop(context) ? 16.h : 52.h,
                              vertical:
                                  !Responsive.isDesktop(context) ? 24.h : 48.h),
                          child: Column(
                            crossAxisAlignment: !Responsive.isDesktop(context)
                                ? CrossAxisAlignment.start
                                : CrossAxisAlignment.center,
                            children: [
                              if (!Responsive.isDesktop(context)) ...[
                                const Align(
                                    alignment: Alignment.centerLeft,
                                    child: CustomBackButton()),
                                16.ph,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const TextDisplayLarge36And26(
                                        AppStrings.uploadAudio),
                                    RichText(
                                        text: TextSpan(
                                            text: '03',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodySmall!
                                                .copyWith(fontSize: 16.sp),
                                            children: [
                                          TextSpan(
                                            text: '/05',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(
                                                    fontWeight: FontWeight.w400),
                                          )
                                        ])),
                                  ],
                                ),
                              ],
                              if (Responsive.isDesktop(context)) ...[
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: RichText(
                                      text: TextSpan(
                                          text: '03',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .copyWith(fontSize: 20.sp),
                                          children: [
                                        TextSpan(
                                          text: '/05',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge!
                                              .copyWith(fontSize: 14.sp),
                                        )
                                      ])),
                                ),
                                const TextDisplayLarge36And26(AppStrings.uploadAudio),
                              ],
                              !Responsive.isDesktop(context) ? 16.ph : 20.ph,
                              const TextTitle14(AppStrings.uploadAudioFromFile),
                              !Responsive.isDesktop(context) ? 36.ph : 52.ph,
                              DottedBorder(
                                borderType: BorderType.RRect,
                                radius: Radius.circular(12.r),
                                padding: EdgeInsets.all(20.h),
                                color: colorScheme.hyperlinkBlueColor,
                                strokeWidth: 1.5,
                                dashPattern: const [10, 10],
                                child: GestureDetector(
                                  onTap: () async => cubit.browseFiles(context, maxFiles: 10),
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8.0.r),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(AppImages.export),
                                        12.ph,
                                        TextTitle14(
                                          AppStrings.browseFileToUploadFileType(),
                                        ),
                                        12.ph,
                                        PrimaryButton(
                                          width: 164.w,
                                          height: 44.h,
                                          backgroundColor: colorScheme.lightGreenFDFFDA,
                                          onPressed: () async {
                                            if (state.audioFiles.length < 10) {
                                              cubit.browseFiles(context, maxFiles: 10);
                                            } else {
                                              CustomToast.show(
                                                context: context,
                                                message: AppStrings.youCanUploadMaximum10Files,
                                              );
                                            }
                                          },
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(40.r),
                                            side: BorderSide(
                                              color: colorScheme.primary,
                                              width: 1.2.h,
                                            ),
                                          ),
                                          child: TextTitle14(
                                            AppStrings.browseFile,
                                            style: theme.textTheme.titleMedium!
                                                .copyWith(fontSize: 16.sp),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              !Responsive.isDesktop(context) ? 36.ph : 52.ph,
                              Expanded(
                                child: ListView.builder(
                                  itemCount: state.audioFiles.length,
                                  itemBuilder: (context, index) {
                                    final isPlaying = state.audioPlayers[index].state ==
                                        PlayerState.playing;
                                    final duration = state.durations[index];
                                    final position = state.positions[index];
                                    return Padding(
                                      padding: EdgeInsets.symmetric(vertical: 8.h),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 9.h),
                                              decoration: BoxDecoration(
                                                color: colorScheme.white,
                                                borderRadius:
                                                    BorderRadius.circular(12.r),
                                                border: Border.all(
                                                    color:
                                                        colorScheme.lightGreyD9D9D9),
                                              ),
                                              child: Row(
                                                children: [
                                                  IconButton(
                                                    icon: SvgPicture.asset(
                                                      isPlaying
                                                          ? AppImages.pauseIcon
                                                          : AppImages.play,
                                                    ),
                                                    onPressed: () => cubit.playAudio(index),
                                                  ),
                                                  Expanded(
                                                    child: Row(
                                                      children: [
                                                        Expanded(
                                                          child: SliderTheme(
                                                            data: SliderThemeData(
                                                              thumbShape:
                                                                  RoundSliderThumbShape(
                                                                      enabledThumbRadius:
                                                                          6.r),
                                                            ),
                                                            child: Slider(
                                                              thumbColor:
                                                                  colorScheme.white,
                                                              activeColor:
                                                                  colorScheme.primary,
                                                              inactiveColor:
                                                                  colorScheme
                                                                      .lightGreyD9D9D9,
                                                              value: position
                                                                  .inSeconds
                                                                  .toDouble(),
                                                              max: duration.inSeconds
                                                                  .toDouble(),
                                                              onChanged: (value) async {
                                                                final newPosition = Duration(seconds: value.toInt());
                                                                await state.audioPlayers[index].seek(newPosition);
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                        TextTitle14(
                                                          isPlaying
                                                              ? "${(duration - position).inMinutes}:${((duration - position).inSeconds % 60).toString().padLeft(2, '0')}"
                                                              : "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          IconButton(
                                            icon: SvgPicture.asset(AppImages.trash),
                                            onPressed: () => cubit.deleteAudio(index),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                              MultiBlocListener(
                                listeners: [
                                  BlocListener<UploadMediaBloc, UploadMediaState>(
                                    listener: (context, state) {
                                      if (state is UploadMediaLoadingState) {
                                        Dialogs.showOnlyLoader(context);
                                      }
                                      if (state is UploadMediaSuccessState) {
                                        context.pop();
                                        context
                                            .read<UploadSamplesBloc>()
                                            .submitUploadSamples(
                                                userInfoRequestModel:
                                                    UserInfoRequestModel(
                                                        intermediateStep: 3,
                                                        audioPath: state
                                                            .getPreSignedUrlModel
                                                            ?.presignedUrls
                                                            ?.voiceAudio
                                                            ?.map(
                                                                (audio) => audio.path)
                                                            .toList()));
                                      }
                                      if (state is UploadMediaErrorState) {
                                        // Show Error Message
                                        context.pop();
                                        CustomToast.show(
                                            context: context,
                                            message: state.errorMsg,
                                            isSuccess: true);
                                      }
                                    },
                                  ),
                                  BlocListener<UploadSamplesBloc, UploadSamplesState>(
                                    listener: (context, state) {
                                      if (state is UploadSamplesLoadingState) {
                                        Dialogs.showOnlyLoader(context);
                                      }
                                      if (state is UploadSamplesSuccessState) {
                                        // Show Success Message
                                        CustomToast.show(
                                            context: context,
                                            message: AppStrings.audioSampleSuccess,
                                            isSuccess: true);
                                        // Navigate to OTP Screen
                                        context.pop();
                                        NavigationServiceImpl.getInstance()!
                                            .doNavigation(
                                          context,
                                          routeName: RouteName.projectType,
                                        );
                                      }
                                      if (state is UploadSamplesErrorState) {
                                        // Show Error Message
                                        context.pop();
                                        CustomToast.show(
                                            context: context,
                                            message: state.errorMsg,
                                            isSuccess: true);
                                      }
                                    },
                                  ),
                                ],
                                child: PrimaryButton(
                                    onPressed: () async {
                                      if (state.audioFiles.isEmpty) {
                                        CustomToast.show(
                                          context: context,
                                          message: ValidationMsg.plsSelectAudio,
                                        );
                                        return;
                                      }
                                      for (var player in state.audioPlayers) {
                                        if (player.state == PlayerState.playing) {
                                          await player.stop();
                                        }
                                      }
                                      if (mounted) {
                                        context
                                            .read<UploadMediaBloc>()
                                            .getPreSignedUrl(
                                                contentType: "audio/mp3",
                                                preSignedUrlReqModel:
                                                    PreSignedUrlReqModel(
                                                        voiceAudio: state.audioFiles
                                                            .map((item) =>
                                                                MediaDetails(
                                                                  extn: item.ext,
                                                                  fileName: item
                                                                      .path
                                                                      ?.split(
                                                                          '/')
                                                                      .last,
                                                                  fileType: "audio",
                                                                ))
                                                            .toList()),
                                                voiceAudio: state.audioFiles
                                                    .map((item) => item.bytes)
                                                    .whereType<Uint8List>()
                                                    .toList());
                                      }
                                    },
                                    buttonText: AppStrings.continues),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
