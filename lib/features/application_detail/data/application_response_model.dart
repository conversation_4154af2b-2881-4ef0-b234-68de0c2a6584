import '../../jobs/enums/application_status.dart';
import '../../jobs/enums/job_status.dart';

class ApplicationResponseModel {
  int? id;
  int? voice;
  String? attachment;
  ApplicationStatus? applicationStatus;
  String? createdAt;
  String? updatedAt;
  String? proposal;
  String? jobTitle;
  String? revisionPolicy;
  double? quote;
  double? platformFee;
  int? job;
  ApplicantsDetails? applicantsDetails;
  bool? isJobAccepted;
  JobStatus? jobStatus;

  ApplicationResponseModel({
    this.id,
    this.voice,
    this.attachment,
    this.applicationStatus,
    this.createdAt,
    this.updatedAt,
    this.proposal,
    this.jobTitle,
    this.revisionPolicy,
    this.quote,
    this.platformFee,
    this.job,
    this.applicantsDetails,
    this.isJobAccepted,
    this.jobStatus,
  });

  ApplicationResponseModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    voice = json['voice'];
    attachment = json['attachment'];
    applicationStatus = ApplicationStatus.getApplicationStatusFromString(json['application_status'] as String?);
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    proposal = json['proposal'];
    jobTitle = json['job_title'];
    revisionPolicy = json['revision_policy'];
    // Handle potential double or int values
    quote = json['quote'] != null ? (json['quote'] as num).toDouble() : null;
    platformFee = json['platform_fee'] != null ? (json['platform_fee'] as num).toDouble() : null;
    job = json['job'];
    applicantsDetails = json['applicants_details'] != null
        ? ApplicantsDetails.fromJson(json['applicants_details'])
        : null;
    isJobAccepted = json['is_job_accepted'] as bool?;
    jobStatus = JobStatus.getJobStatusFromString(json['job_status'] as String?);
  }
}

class ApplicantsDetails {
  int? id;
  String? fullName;
  String? profilePic;

  ApplicantsDetails({this.id, this.fullName, this.profilePic});

  ApplicantsDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    fullName = json['full_name'];
    profilePic = json['profile_pic'];
  }
}
