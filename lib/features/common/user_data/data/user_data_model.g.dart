// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserDataModelAdapter extends TypeAdapter<UserDataModel> {
  @override
  final int typeId = 0;

  @override
  UserDataModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserDataModel(
      id: fields[0] as int?,
      phoneNumber: fields[1] as String?,
      profilePic: fields[2] as String?,
      email: fields[3] as String?,
      firstName: fields[4] as String?,
      lastName: fields[5] as String?,
      role: fields[6] as UserType?,
      gender: fields[7] as NameIdModel?,
      isEmailVerified: fields[8] as bool?,
      isPhoneVerified: fields[9] as bool?,
      intermediateStep: fields[10] as int?,
      countryCode: fields[11] as String?,
      countryAbbr: fields[12] as String?,
      countryName: fields[13] as String?,
      bio: fields[14] as String?,
      company: fields[15] as String?,
      streetAddress: fields[16] as String?,
      city: fields[17] as String?,
      state: fields[18] as String?,
      postalCode: fields[19] as String?,
      country: fields[20] as String?,
      location: fields[21] as String?,
      industry: fields[22] as NameIdModel?,
      voiceAudioUrls: (fields[31] as List?)?.cast<AudioURL>(),
      tags: (fields[32] as List?)?.cast<String>(),
      experience: fields[33] as NameIdModel?,
      isFavorite: fields[35] as bool?,
      clientGoogleCalender: fields[36] as String?,
      voiceGoogleCalender: fields[37] as String?,
      serverAuthCode: fields[38] as bool?,
      services: (fields[39] as List?)?.cast<NameIdModel>(),
    )
      ..nativeLanguage = fields[23] as NameIdModel?
      ..voiceType = (fields[24] as List?)?.cast<NameIdModel>()
      ..voiceCharacter = (fields[25] as List?)?.cast<NameIdModel>()
      ..voiceLanguage = (fields[26] as List?)?.cast<NameIdModel>()
      ..projectType = (fields[27] as List?)?.cast<NameIdModel>()
      ..ageRange = (fields[28] as List?)?.cast<AgeRange>()
      ..voiceGender = (fields[29] as List?)?.cast<NameIdModel>()
      ..voiceAccent = (fields[30] as List?)?.cast<NameIdModel>()
      ..reviews = fields[34] as ReviewModel?;
  }

  @override
  void write(BinaryWriter writer, UserDataModel obj) {
    writer
      ..writeByte(40)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.phoneNumber)
      ..writeByte(2)
      ..write(obj.profilePic)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.firstName)
      ..writeByte(5)
      ..write(obj.lastName)
      ..writeByte(6)
      ..write(obj.role)
      ..writeByte(7)
      ..write(obj.gender)
      ..writeByte(8)
      ..write(obj.isEmailVerified)
      ..writeByte(9)
      ..write(obj.isPhoneVerified)
      ..writeByte(10)
      ..write(obj.intermediateStep)
      ..writeByte(11)
      ..write(obj.countryCode)
      ..writeByte(12)
      ..write(obj.countryAbbr)
      ..writeByte(13)
      ..write(obj.countryName)
      ..writeByte(14)
      ..write(obj.bio)
      ..writeByte(15)
      ..write(obj.company)
      ..writeByte(16)
      ..write(obj.streetAddress)
      ..writeByte(17)
      ..write(obj.city)
      ..writeByte(18)
      ..write(obj.state)
      ..writeByte(19)
      ..write(obj.postalCode)
      ..writeByte(20)
      ..write(obj.country)
      ..writeByte(21)
      ..write(obj.location)
      ..writeByte(22)
      ..write(obj.industry)
      ..writeByte(23)
      ..write(obj.nativeLanguage)
      ..writeByte(24)
      ..write(obj.voiceType)
      ..writeByte(25)
      ..write(obj.voiceCharacter)
      ..writeByte(26)
      ..write(obj.voiceLanguage)
      ..writeByte(27)
      ..write(obj.projectType)
      ..writeByte(28)
      ..write(obj.ageRange)
      ..writeByte(29)
      ..write(obj.voiceGender)
      ..writeByte(30)
      ..write(obj.voiceAccent)
      ..writeByte(31)
      ..write(obj.voiceAudioUrls)
      ..writeByte(32)
      ..write(obj.tags)
      ..writeByte(33)
      ..write(obj.experience)
      ..writeByte(34)
      ..write(obj.reviews)
      ..writeByte(35)
      ..write(obj.isFavorite)
      ..writeByte(36)
      ..write(obj.clientGoogleCalender)
      ..writeByte(37)
      ..write(obj.voiceGoogleCalender)
      ..writeByte(38)
      ..write(obj.serverAuthCode)
      ..writeByte(39)
      ..write(obj.services);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDataModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NameIdModelAdapter extends TypeAdapter<NameIdModel> {
  @override
  final int typeId = 2;

  @override
  NameIdModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NameIdModel(
      id: fields[0] as int?,
      name: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, NameIdModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NameIdModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AudioURLAdapter extends TypeAdapter<AudioURL> {
  @override
  final int typeId = 3;

  @override
  AudioURL read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AudioURL(
      id: fields[0] as int?,
      audioUrl: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, AudioURL obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.audioUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioURLAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AgeRangeAdapter extends TypeAdapter<AgeRange> {
  @override
  final int typeId = 4;

  @override
  AgeRange read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AgeRange(
      id: fields[0] as int?,
      name: fields[1] as String?,
      lowestAge: fields[2] as int?,
      highestAge: fields[3] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, AgeRange obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.lowestAge)
      ..writeByte(3)
      ..write(obj.highestAge);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgeRangeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReviewModelAdapter extends TypeAdapter<ReviewModel> {
  @override
  final int typeId = 14;

  @override
  ReviewModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReviewModel(
      totalReviewers: fields[0] as int?,
      ratingCounts: (fields[1] as List?)?.cast<RatingCount>(),
      averageRating: fields[2] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, ReviewModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.totalReviewers)
      ..writeByte(1)
      ..write(obj.ratingCounts)
      ..writeByte(2)
      ..write(obj.averageRating);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReviewModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RatingCountAdapter extends TypeAdapter<RatingCount> {
  @override
  final int typeId = 15;

  @override
  RatingCount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RatingCount(
      rating: fields[0] as double?,
      count: fields[1] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, RatingCount obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.rating)
      ..writeByte(1)
      ..write(obj.count);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RatingCountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserTypeAdapter extends TypeAdapter<UserType> {
  @override
  final int typeId = 1;

  @override
  UserType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return UserType.client;
      case 1:
        return UserType.voice;
      case 2:
        return UserType.admin;
      case 3:
        return UserType.ancillaryService;
      case 4:
        return UserType.unknown;
      default:
        return UserType.client;
    }
  }

  @override
  void write(BinaryWriter writer, UserType obj) {
    switch (obj) {
      case UserType.client:
        writer.writeByte(0);
        break;
      case UserType.voice:
        writer.writeByte(1);
        break;
      case UserType.admin:
        writer.writeByte(2);
        break;
      case UserType.ancillaryService:
        writer.writeByte(3);
        break;
      case UserType.unknown:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
