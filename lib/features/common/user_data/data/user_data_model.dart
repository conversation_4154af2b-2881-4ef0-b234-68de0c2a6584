import 'package:hive/hive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

part 'user_data_model.g.dart';

@HiveType(typeId: 0) // Unique ID for UserDataModel
class UserDataModel extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String? phoneNumber;

  @HiveField(2)
  String? profilePic;

  @HiveField(3)
  String? email;

  @HiveField(4)
  String? firstName;

  @HiveField(5)
  String? lastName;

  @HiveField(6)
  UserType? role;

  @HiveField(7)
  NameIdModel? gender;

  @HiveField(8)
  bool? isEmailVerified;

  @HiveField(9)
  bool? isPhoneVerified;

  @HiveField(10)
  int? intermediateStep;

  @HiveField(11)
  String? countryCode;

  @HiveField(12)
  String? countryAbbr;

  @HiveField(13)
  String? countryName;

  @HiveField(14)
  String? bio;

  @HiveField(15)
  String? company;

  @HiveField(16)
  String? streetAddress;

  @HiveField(17)
  String? city;

  @HiveField(18)
  String? state;

  @HiveField(19)
  String? postalCode;

  @HiveField(20)
  String? country;

  @HiveField(21)
  String? location;

  @HiveField(22)
  NameIdModel? industry;
  @HiveField(23)
  NameIdModel? nativeLanguage;
    @HiveField(24)
  List<NameIdModel>? voiceType;
    @HiveField(25)
  List<NameIdModel>? voiceCharacter;
    @HiveField(26)
  List<NameIdModel>? voiceLanguage;
    @HiveField(27)
  List<NameIdModel>? projectType;
    @HiveField(28)
  List<AgeRange>? ageRange;
    @HiveField(29)
  List<NameIdModel>? voiceGender;
    @HiveField(30)
  List<NameIdModel>? voiceAccent;

  @HiveField(31)
  List<AudioURL>? voiceAudioUrls;

  @HiveField(32)
  List<String>? tags;

  @HiveField(33)
  NameIdModel? experience;

  @HiveField(34)
  ReviewModel? reviews;

  @HiveField(35)
  bool? isFavorite;

  @HiveField(36)
  String? clientGoogleCalender;

  @HiveField(37)
  String? voiceGoogleCalender;

  @HiveField(38)
  bool? serverAuthCode;

  @HiveField(39)
  List<NameIdModel>? services;
  
  UserDataModel({
    this.id,
    this.phoneNumber,
    this.profilePic,
    this.email,
    this.firstName,
    this.lastName,
    this.role,
    this.gender,
    this.isEmailVerified,
    this.isPhoneVerified,
    this.intermediateStep,
    this.countryCode,
    this.countryAbbr,
    this.countryName,
    this.bio,
    this.company,
    this.streetAddress,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.location,
    this.industry,
    this.voiceAudioUrls,
    this.tags,
    this.experience,
    this.isFavorite,
    this.clientGoogleCalender,
    this.voiceGoogleCalender,
    this.serverAuthCode,
    this.services
  });

  UserDataModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    phoneNumber = json['phone_number'];
    profilePic = json['profile_pic'];
    role = json['role'] != null ? getUserTypeFromString(json['role']) : null;
    if (json['voice_audio_urls'] != null) {
      voiceAudioUrls = <AudioURL>[];
      json['voice_audio_urls'].forEach((v) {
        voiceAudioUrls!.add(AudioURL.fromJson(v));
      });
    }
    email = json['email'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    gender =
        (json['gender'] != null ? NameIdModel.fromJson(json['gender']) : null);
    isEmailVerified = json['is_email_verified'];
    isPhoneVerified = json['is_phone_verified'];
    intermediateStep = json['intermediate_step'];
    countryCode = json['country_code'];
    countryAbbr = json['country_abbr'];
    countryName = json['country_name'];
    bio = json['bio'];
    company = json['company'];
    industry = (json['industry'] != null ? NameIdModel.fromJson(json['industry']) : null);
    streetAddress = json['street_address'];
    city = json['city'];
    state = json['state'];
    postalCode = json['postal_code'];
    country = json['country'];
    nativeLanguage = json['native_language'] != null
        ? NameIdModel.fromJson(json['native_language'])
        : null;
    if (json['voice_type'] != null) {
      voiceType = <NameIdModel>[];
      json['voice_type'].forEach((v) {
        voiceType!.add(NameIdModel.fromJson(v));
      });
    }
    if (json['voice_character'] != null) {
      voiceCharacter = <NameIdModel>[];
      json['voice_character'].forEach((v) {
        voiceCharacter!.add(NameIdModel.fromJson(v));
      });
    }
    if (json['voice_language'] != null) {
      voiceLanguage = <NameIdModel>[];
      json['voice_language'].forEach((v) {
        voiceLanguage!.add(NameIdModel.fromJson(v));
      });
    }
    if (json['project_type'] != null) {
      projectType = <NameIdModel>[];
      json['project_type'].forEach((v) {
        projectType!.add(NameIdModel.fromJson(v));
      });
    }
    if (json['age_range'] != null) {
      ageRange = <AgeRange>[];
      json['age_range'].forEach((v) {
        ageRange!.add(AgeRange.fromJson(v));
      });
    }
    if (json['voice_gender'] != null) {
      voiceGender = <NameIdModel>[];
      json['voice_gender'].forEach((v) {
        voiceGender!.add(NameIdModel.fromJson(v));
      });
      
    }
    if (json['voice_accent'] != null) {
      voiceAccent = <NameIdModel>[];
      json['voice_accent'].forEach((v) {
        voiceAccent!.add(NameIdModel.fromJson(v));
      });
    }
    tags = json['tags'] != null ? List<String>.from(json['tags']) : null;
    
    experience = (json['voice_experience'] != null ? NameIdModel.fromJson(json['voice_experience']) : null);
    reviews = json['reviews'] != null ? ReviewModel.fromJson(json['reviews']) : null;
    isFavorite = json['is_favorite'] ?? false;
    clientGoogleCalender = json['client_google_calender'];
    voiceGoogleCalender = json['voice_google_calender'];
    serverAuthCode = json['server_auth_code'] ?? false;
    if(json['services'] != null) {
      services = <NameIdModel>[];
      json['services'].forEach((v) {
        services!.add(NameIdModel.fromJson(v));
      });
    }
  }
  static UserType getUserTypeFromString(String statusString) {
    switch (statusString) {
      case AppStrings.client:
        return UserType.client;
      case AppStrings.voice:
        return UserType.voice;
      case AppStrings.admin:
        return UserType.admin;
      case AppStrings.ancillaryServices:
        return UserType.ancillaryService;
      default:
        return UserType.unknown;
    }
  }

  UserDataModel copyWith({bool? isFavorite}) {
    this.isFavorite = isFavorite ?? this.isFavorite;
    return this;
  }
}

@HiveType(typeId: 1) // Unique ID for UserType enum
enum UserType {
  @HiveField(0)
  client,

  @HiveField(1)
  voice,

  @HiveField(2)
  admin,

  @HiveField(3)
  ancillaryService,
  
  @HiveField(4)
  unknown,
}

extension UserTypeEnumExtn on UserType {
  String getString() {
    switch (this) {
      case UserType.client:
        return AppStrings.client;
      case UserType.voice:
        return AppStrings.voice;
      case UserType.admin:
        return AppStrings.admin;
      case UserType.ancillaryService:
        return AppStrings.ancillaryServices;
      case UserType.unknown:
        return AppStrings.unknown;
    }
  }
}

@HiveType(typeId: 2) // Unique ID for NameIdModel
class NameIdModel extends HiveObject {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String? name;

  NameIdModel({this.id, this.name});

  factory NameIdModel.fromJson(Map<String, dynamic> json) {
    return NameIdModel(
      id: json['id'],
      name: json['name'],
    );
  }
}

@HiveType(typeId: 3)
class AudioURL {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String? audioUrl;

  AudioURL({this.id, this.audioUrl});

  factory AudioURL.fromJson(Map<String, dynamic> json) {
    return AudioURL(
      id: json['id'],
      audioUrl: json['audio_url'],
    );
  }
}

@HiveType(typeId: 4)
class AgeRange {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String? name;

  @HiveField(2)
  int? lowestAge;

  @HiveField(3)
  int? highestAge;

  AgeRange({
    this.id,
    this.name,
    this.lowestAge,
    this.highestAge,
  });

  AgeRange.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    lowestAge = json['lowest_age'];
    highestAge = json['highest_age'];
  }

  @override
  bool operator ==(Object other) =>
      other is AgeRange &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

@HiveType(typeId: 14)
class ReviewModel {
  @HiveField(0)
  int? totalReviewers;

  @HiveField(1)
  List<RatingCount>? ratingCounts;

  @HiveField(2)
  double? averageRating;

  ReviewModel({
    this.totalReviewers,
    this.ratingCounts,
    this.averageRating,
  });

  ReviewModel.fromJson(Map<String, dynamic> json) {
    totalReviewers = json['total_reviewers'];
    if (json['rating_counts'] != null) {
      ratingCounts = <RatingCount>[];
      json['rating_counts'].forEach((v) {
        ratingCounts!.add(RatingCount.fromJson(v));
      });
    }
    averageRating = json['average_rating']?.toDouble();
  }
}

@HiveType(typeId: 15)
class RatingCount {
  @HiveField(0)
  double? rating;

  @HiveField(1)
  int? count;

  RatingCount({
    this.rating,
    this.count,
  });

  RatingCount.fromJson(Map<String, dynamic> json) {
    rating = json['rating'];
    count = json['count'];
  }
}
