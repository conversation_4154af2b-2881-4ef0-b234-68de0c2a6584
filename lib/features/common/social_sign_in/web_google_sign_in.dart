import 'web_google_sign_in_platform_interface.dart';

import 'web_google_sign_in_stub.dart'
    if (dart.library.html) 'web_google_sign_in_impl.dart';

class WebGoogleSignIn {
  static const String _clientId = '276514509704-58uvtig2k4ld4313l7rcqng4hjuksin9.apps.googleusercontent.com';

  // Get the platform-specific implementation instance
  static final WebGoogleSignInPlatformInterface _impl = getImplementation();

  static void startGoogleSignInFlow(void Function(String code, String accessToken) onAuthReceived) {
    _impl.startFlow(_clientId, onAuthReceived);
  }
}
