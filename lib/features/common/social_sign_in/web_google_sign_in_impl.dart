import 'dart:developer';
import 'dart:js' as js;
import 'package:flutter/foundation.dart';

import 'web_google_sign_in_platform_interface.dart';

class WebGoogleSignInImpl implements WebGoogleSignInPlatformInterface {
  @override
  void startFlow(String clientId, void Function(String code, String accessToken) onAuthReceived) {
    if (!kIsWeb) return;

    try {
      initGoogleCodeClient(clientId, (dynamic response) {
        if (response == null) return;
        try {
          final responseData = response is js.JsObject ? response : js.JsObject.fromBrowserObject(response);
          final code = responseData['code'];
          final error = responseData['error'];

          if (code != null && error == null) {
            onAuthReceived(code.toString(), '');
          } else if (error != null) {
            log('Google Sign-In error: ${responseData['error_description']}');
          }
        } catch (e) {
          log('Error processing Google Sign-In response', error: e);
        }
      });
      requestAuthCode();
    } catch (e) {
      log('Error initializing Google Sign-In', error: e);
    }
  }

  static void initGoogleCodeClient(String clientId, void Function(dynamic) callback) {
    js.context.callMethod('initGoogleCodeClient', [clientId, js.allowInterop(callback)]);
  }

  static void requestAuthCode() {
    js.context.callMethod('requestGoogleAuthCode');
  }
}

WebGoogleSignInPlatformInterface getImplementation() => WebGoogleSignInImpl();
