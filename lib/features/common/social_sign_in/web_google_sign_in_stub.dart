import 'dart:developer';
import 'web_google_sign_in_platform_interface.dart';

class WebGoogleSignInStub implements WebGoogleSignInPlatformInterface {
  @override
  void startFlow(
    String clientId,
    void Function(String code, String accessToken) onAuthReceived,
  ) {
    const message = 'Google Sign-In (web flow) is not supported on this platform.';
    log(message);
  }
}

WebGoogleSignInPlatformInterface getImplementation() => WebGoogleSignInStub();
