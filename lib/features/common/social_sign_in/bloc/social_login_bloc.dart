import 'dart:developer';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../../core/api/api_service.dart';
import '../../../../core/services/hive/hive_keys.dart';
import '../../../../core/services/hive/hive_storage_helper.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../verifyOtp/data/auth_response_model.dart';
import '../../user_data/data/user_data_model.dart';
import '../web_google_sign_in.dart';

part 'social_login_state.dart';

class SocialLoginBloc extends Cubit<SocialLoginState> {
  SocialLoginBloc() : super(SocialLoginInitial());

  Future<void> signInWithGoogle() async {
    emit(SocialLoginLoading());
    try {
      if (kIsWeb) {
        await _webGoogleSignIn();
      } else {
        await _nativeGoogleSignIn();
      }
    } catch (e) {
      log("Error during Google Sign-In: ${e.toString()}");
      emit(SocialLoginError(AppStrings.genericErrorMsg));
    }
  }

  Future<void> _webGoogleSignIn() async {
    WebGoogleSignIn.startGoogleSignInFlow((code, accessToken) async {      
      try {
        if (accessToken.isNotEmpty) {
          await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.googleAccessToken, accessToken);
        }

        final response = await ApiService.instance.loginWithGoogle(
          deviceName: 'web',
          serverAuthCode: code,
        );
        await _handleAuthResponse(response);
      } catch (e) {
        emit(SocialLoginError(AppStrings.genericErrorMsg));
      }
    });
  }

  Future<void> _nativeGoogleSignIn() async {
    final GoogleSignIn googleSignIn = GoogleSignIn(
      scopes: ['email', 'https://www.googleapis.com/auth/calendar.events'],
      clientId: Platform.isIOS
          ? '************-dllemph2pc38bjornh76kl0aped8tl35.apps.googleusercontent.com'
          : null,
      serverClientId: '************-58uvtig2k4ld4313l7rcqng4hjuksin9.apps.googleusercontent.com',
      forceCodeForRefreshToken: true,
    );

    await googleSignIn.signOut();
    final googleSignInAccount = await googleSignIn.signIn();

    if (googleSignInAccount == null) {
      emit(SocialLoginInitial());
      return;
    }

    final GoogleSignInAuthentication googleAuth =  await googleSignInAccount.authentication;

      // log("ID Token: ${googleAuth.idToken}");
      // log("Access Token: ${googleAuth.accessToken}");
      // log("Server Auth Code (Web/Android): ${googleSignInAccount.serverAuthCode}");
      // log("Google Sign-In Account: $googleSignInAccount");
    
    await HiveStorageHelper.saveData<String>( HiveBoxName.user,HiveKeys.googleAccessToken, googleAuth.accessToken ?? '');
    
    if (googleAuth.accessToken == null && googleAuth.idToken == null) {
      emit(SocialLoginError("Failed to get authentication tokens"));
      return;
    }

    final response = await ApiService.instance.loginWithGoogle(
      // accessToken: null,
      // idToken: googleAuth.idToken,
      deviceName: 'Android',
      serverAuthCode: googleSignInAccount.serverAuthCode,
    );

    await _handleAuthResponse(response);
  }

  Future<void> signInWithApple() async {
    emit(SocialLoginLoading());
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final response = await ApiService.instance.loginWithApple(
        token: credential.identityToken,
        firstName: credential.givenName,
        lastName: credential.familyName,
      );
      await _handleAuthResponse(response);
    } catch (e) {
      log("Error during Apple Sign-In: ${e.toString()}");
      emit(SocialLoginError(AppStrings.genericErrorMsg));
    }
  }

  Future<void> _handleAuthResponse(dynamic response) async {
    if (response.success) {
      AuthResponseModel authResponseModel = AuthResponseModel.fromJson(response.data);
          
      ApiService.instance.setPartnerGatewayToken(authResponseModel.token ?? '');
      
      await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.userToken, authResponseModel.token ?? '');
      
      final userDataResponse = await ApiService.instance.getUserData();
      
      if (userDataResponse.success) {
        UserDataModel userDataModel = UserDataModel.fromJson(userDataResponse.data);
            
        await HiveStorageHelper.saveData<UserDataModel>(HiveBoxName.user, HiveKeys.userData,userDataModel);
        
        emit(SocialLoginSuccess(userDataModel: userDataModel));
      } else {
        emit(SocialLoginError( response.message ?? AppStrings.genericErrorMsg));
      }
    } else {
      emit(SocialLoginError(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
