import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/image_model.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class ClientProfileScreen extends StatefulWidget {
  const ClientProfileScreen({super.key});

  @override
  State<ClientProfileScreen> createState() => _ClientProfileScreenState();
}

class _ClientProfileScreenState extends State<ClientProfileScreen> {
  ImageModel? _userImage;
  @override
  void initState() {
    context.read<UserBloc>().getUserData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    return Scaffold(
      body: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        if (state is UserLoadingState) {
          return const Center(child: CircularProgressIndicator());
        }
        if (state is UserErrorState) {
          return ErrorScreen(
            onRetry: () {
              context.read<UserBloc>().getUserData();
            },
          );
        }
        if (state is UserSuccessState) {
          bool isClient = (state.userDataModel.role == UserType.client);
          return SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal:
                              !Responsive.isDesktop(context) ? 0.h : 120.h,
                          vertical:
                              !Responsive.isDesktop(context) ? 0.h : 60.h),
                      child: Card(
                        color: Theme.of(context).colorScheme.white,
                        elevation: !Responsive.isDesktop(context) ? 0 : 8,
                        shadowColor:
                            Theme.of(context).colorScheme.lightGreyD9D9D9,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal:
                                  !Responsive.isDesktop(context) ? 16.h : 60.h,
                              vertical:
                                  !Responsive.isDesktop(context) ? 24.h : 48.h),
                          child: Column(
                            crossAxisAlignment: !Responsive.isDesktop(context)
                                ? CrossAxisAlignment.center
                                : CrossAxisAlignment.start,
                            children: [
                              const TextDisplayLarge36And26(
                                  AppStrings.myProfile),
                              !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                              Column(
                                  crossAxisAlignment:
                                      !Responsive.isDesktop(context)
                                          ? CrossAxisAlignment.center
                                          : CrossAxisAlignment.start,
                                  children: [
                                    !Responsive.isDesktop(context)
                                        ? Column(
                                            children: [
                                              SizedBox(
                                                width: 140.h,
                                                height: 140.h,
                                                child: Stack(
                                                  children: [
                                                    CircleAvatar(
                                                      radius: 60.r,
                                                      backgroundColor:
                                                          colorScheme
                                                              .blackE8E8E8,
                                                      backgroundImage:
                                                          _userImage != null
                                                              ? MemoryImage(
                                                                  _userImage!
                                                                      .bytes)
                                                              : null,
                                                      child: _userImage != null
                                                          ? null
                                                          : SvgPicture.asset(
                                                              "assets/images/user_icon.svg"),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              16.ph,
                                              TextDisplayLarge36And26(
                                                  "${state.userDataModel.firstName} ${state.userDataModel.lastName}"),
                                              12.ph,
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                      AppImages.callIcon),
                                                  10.pw,
                                                  TextTitle14(state
                                                          .userDataModel
                                                          .phoneNumber ??
                                                      ''),
                                                ],
                                              ),
                                              6.ph,
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                      AppImages.smsIcon),
                                                  10.pw,
                                                  TextTitle14(state
                                                          .userDataModel
                                                          .email ??
                                                      '')
                                                ],
                                              ),
                                              if (isClient) 12.ph,
                                              if (isClient)
                                                TextTitle18And14(
                                                  state.userDataModel.bio ?? "",
                                                  maxLines: 2,
                                                )
                                            ],
                                          )
                                        : Row(
                                            children: [
                                              SizedBox(
                                                width: 120.h,
                                                height: 120.h,
                                                child: Stack(
                                                  children: [
                                                    CircleAvatar(
                                                      radius: 60.r,
                                                      backgroundColor:
                                                          colorScheme
                                                              .blackE8E8E8,
                                                      backgroundImage:
                                                          _userImage != null
                                                              ? MemoryImage(
                                                                  _userImage!
                                                                      .bytes)
                                                              : null,
                                                      child: _userImage != null
                                                          ? null
                                                          : SvgPicture.asset(
                                                              "assets/images/user_icon.svg"),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              43.pw,
                                              Flexible(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    TextTitle18And20(
                                                        "${state.userDataModel.firstName} ${state.userDataModel.lastName}",
                                                        style: theme.textTheme
                                                            .bodySmall!
                                                            .copyWith(
                                                          color: colorScheme
                                                              .primaryGrey,
                                                          fontFamily:
                                                              'Noto Sans',
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          fontSize: 24,
                                                        )),
                                                    20.ph,
                                                    Row(
                                                      mainAxisAlignment: isClient
                                                          ? MainAxisAlignment
                                                              .start
                                                          : MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        SvgPicture.asset(
                                                            AppImages.callIcon),
                                                        10.pw,
                                                        TextTitle14(state
                                                                .userDataModel
                                                                .phoneNumber ??
                                                            ''),
                                                        12.pw,
                                                        Container(
                                                          width: 8,
                                                          height: 8,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: colorScheme
                                                                .lightGreyB2B2B2,
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                        ),
                                                        12.pw,
                                                        SvgPicture.asset(
                                                            AppImages.smsIcon),
                                                        10.pw,
                                                        TextTitle14(state
                                                                .userDataModel
                                                                .email ??
                                                            ''),
                                                      ],
                                                    ),
                                                    if (isClient) 20.ph,
                                                    if (isClient)
                                                      TextTitle18And14(state
                                                              .userDataModel
                                                              .bio ??
                                                          "")
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                    if (isClient)
                                      !Responsive.isDesktop(context)
                                          ? 24.ph
                                          : 40.ph,
                                    if (isClient)
                                      CommonShadowContainer(
                                        child: Padding(
                                          padding: EdgeInsets.all(
                                              !Responsive.isDesktop(context)
                                                  ? 12.h
                                                  : 28.h),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  TextTitle18And14(
                                                    AppStrings.details
                                                        .toUpperCase(),
                                                    style: theme
                                                        .textTheme.titleMedium!
                                                        .copyWith(
                                                      color: colorScheme.black,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                    ),
                                                  ),
                                                  InkWell(
                                                      onTap: () {
                                                        NavigationServiceImpl
                                                                .getInstance()!
                                                            .doNavigation(
                                                                context,
                                                                routeName: RouteName.editProfileDetails,
                                                               
                                                                );
                                                      },
                                                      child: TextTitle18And14(
                                                        AppStrings.edit,
                                                        color: colorScheme
                                                            .hyperlinkBlueColor,
                                                      )),
                                                ],
                                              ),
                                              !Responsive.isDesktop(context)
                                                  ? 12.ph
                                                  : 28.ph,
                                              TextTitle18And20(
                                                state.userDataModel.gender
                                                        ?.name ??
                                                    "",
                                              ),
                                              4.ph,
                                              const TextTitle14(
                                                  AppStrings.gender_),
                                              !Responsive.isDesktop(context)
                                                  ? 24.ph
                                                  : 28.ph,
                                              TextTitle18And20(
                                                  state.userDataModel.company ??
                                                      ""),
                                              4.ph,
                                              const TextTitle14(
                                                  AppStrings.company_),
                                              !Responsive.isDesktop(context)
                                                  ? 24.ph
                                                  : 28.ph,
                                              TextTitle18And20(
                                                state.userDataModel.industry
                                                        ?.name
                                                        .toString() ??
                                                    '',
                                              ),
                                              4.ph,
                                              const TextTitle14(
                                                  AppStrings.industry_),
                                              !Responsive.isDesktop(context)
                                                  ? 24.ph
                                                  : 28.ph,
                                            ],
                                          ),
                                        ),
                                      ),
                                  ]),
                              !Responsive.isDesktop(context) ? 24.ph : 54.ph,
                              PrimaryButton(
                                  onPressed: () {
                                    // EditProfileDetails
                                    NavigationServiceImpl.getInstance()!
                                        .doNavigation(context,
                                            routeName:
                                                RouteName.editProfileDetails);
                                  },
                                  buttonText: AppStrings.editProfile)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox();
      }),
    );
  }
}
