import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:collection/collection.dart';

import '../../../common/user_data/data/user_data_model.dart';

class EditVocalCharacteristicScreen extends StatefulWidget {
  const EditVocalCharacteristicScreen({super.key});

  @override
  State<EditVocalCharacteristicScreen> createState() =>
      _EditVocalCharacteristicScreenState();
}

class _EditVocalCharacteristicScreenState
    extends State<EditVocalCharacteristicScreen> {
  final formGlobalKey = GlobalKey<FormState>();
  List<DropdownData> selectedVoiceGender = [];
  List<DropdownData> selectedVoiceType = [];
  List<DropdownData> selectedVoiceChar = [];
  List<AgeRange> selectedVoiceAge = [];
  List<DropdownData> selectedLanguages = [];
  List<DropdownData> selectedAccent = [];

  List<DropdownData> previousSelectedVoiceGender = [];
  List<DropdownData> previousSelectedVoiceType = [];
  List<DropdownData> previousSelectedVoiceChar = [];
  List<AgeRange> previousSelectedVoiceAge = [];
  List<DropdownData> previousSelectedVoiceLang = [];
  List<DropdownData> previousSelectedVoiceAccent = [];
  DropdownData? selectedExperience;

  bool _initialDataLoaded = false;

  @override
  void initState() {
    super.initState();
    context.read<UserBloc>().getUserData();
    context.read<StaticDataDropdownBloc>().dropDownListApi();
  }

  @override
  void dispose() {
    super.dispose();
  }

  bool areListsNotEqualById(
      List<DropdownData> list1, List<DropdownData> list2) {
    if (list1.length != list2.length) return true;
    List<int?> ids1 = list1.map((e) => e.id).toList()..sort();
    List<int?> ids2 = list2.map((e) => e.id).toList()..sort();
    return !(const ListEquality().equals(ids1, ids2));
  }

  bool areAgeListsNotEqualById(List<AgeRange> list1, List<AgeRange> list2) {
    if (list1.length != list2.length) return true;
    List<int?> ids1 = list1.map((e) => e.id).toList()..sort();
    List<int?> ids2 = list2.map((e) => e.id).toList()..sort();
    return !(const ListEquality().equals(ids1, ids2));
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocBuilder<UserBloc, UserState>(builder: (context, userState) {
      if (userState is UserLoadingState) {
          return const Center(child: Loader());
      }
      if (userState is UserErrorState) {
        return ErrorScreen(
            onRetry: () {
              context.read<UserBloc>().getUserData();
            },
            errorMessage: userState.errorMsg,
            imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                height: 200.h, width: 100.w));
      }
      if (userState is UserSuccessState) {
        if (!_initialDataLoaded) {
          selectedVoiceGender =
              userState.userDataModel.voiceGender!.map((element) {
            return DropdownData(
              id: element.id,
              name: element.name,
            );
          }).toList();
          selectedVoiceType = userState.userDataModel.voiceType!.map((element) {
            return DropdownData(
              id: element.id,
              name: element.name,
            );
          }).toList();
          selectedVoiceAge = userState.userDataModel.ageRange!.map((element) {
            return AgeRange(
              id: element.id,
              name: element.name,
              highestAge: element.highestAge,
              lowestAge: element.lowestAge,
            );
          }).toList();
          selectedLanguages =
              userState.userDataModel.voiceLanguage!.map((element) {
            return DropdownData(
              id: element.id,
              name: element.name,
            );
          }).toList();

          selectedAccent = userState.userDataModel.voiceAccent!.map((element) {
            return DropdownData(
              id: element.id,
              name: element.name,
            );
          }).toList();

          selectedVoiceChar =
              userState.userDataModel.voiceCharacter!.map((element) {
            return DropdownData(
              id: element.id,
              name: element.name,
            );
          }).toList();
          selectedExperience = selectedExperience ?? (userState.userDataModel.experience != null ? DropdownData(
            id: userState.userDataModel.experience?.id,
            name: userState.userDataModel.experience?.name,
          ) : null);

          previousSelectedVoiceGender = List.from(selectedVoiceGender);
          previousSelectedVoiceType = List.from(selectedVoiceType);
          previousSelectedVoiceChar = List.from(selectedVoiceChar);
          previousSelectedVoiceAge = List.from(selectedVoiceAge);
          previousSelectedVoiceLang = List.from(selectedLanguages);
          previousSelectedVoiceAccent = List.from(selectedAccent);
          _initialDataLoaded = true;
        }
        return Scaffold(
          body: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: !Responsive.isDesktop(context)
                        ? EdgeInsets.zero
                        : EdgeInsets.all(44.0.h),
                    child: Card(
                      color: colorScheme.white,
                      elevation: !Responsive.isDesktop(context) ? 0 : 8,
                      shadowColor: colorScheme.lightGreyD9D9D9,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal:
                              !Responsive.isDesktop(context) ? 16.h : 52.h,
                          vertical:
                              !Responsive.isDesktop(context) ? 16.h : 48.h,
                        ),
                        child: Column(
                          crossAxisAlignment: !Responsive.isDesktop(context)
                              ? CrossAxisAlignment.start
                              : CrossAxisAlignment.center,
                          children: [
                            if (!Responsive.isDesktop(context)) ...[
                              Row(
                                children: [
                                  CustomBackButton(
                                    onTap: () {
                                      context.pop();
                                    },
                                  ),
                                  8.pw,
                                const Text24And20SemiBold(
                                      AppStrings
                                          .editVocalCharacteristics),
                                  const SizedBox(height: 32)
                                ],
                              ),
                            ] else
                              Row(
                                children: [
                                  const TextDisplayLarge36And26(
                                      AppStrings.editVocalCharacteristics),
                                  const Spacer(),
                                  InkWell(
                                    onTap: () {
                                      context.pop();
                                    },
                                    child: TextTitle18And14(
                                      AppStrings.cancel,
                                      color: colorScheme.hyperlinkBlueColor,
                                    ),
                                  ),
                                ],
                              ),
                            !Responsive.isDesktop(context) ? 16.ph : 52.ph,
                            !Responsive.isDesktop(context)
                                ? Column(
                                    children: [
                                      BlocBuilder<StaticDataDropdownBloc,
                                          StaticDataDropdownState>(
                                        builder: (context, state) {
                                          List<DropdownData>? gender = [];
                                          if (state
                                              is StaticDataDropdownSuccessState) {
                                            gender.addAll(state
                                                    .dropDownResponseModel
                                                    ?.gender ??
                                                []);
                                          }
                                          return CustomMultiselectDropdown(
                                            isLoading: state
                                                is StaticDataDropdownLoadingState,
                                            isError: state
                                                is StaticDataDropdownErrorState,
                                            titleText: AppStrings.voiceGender,
                                            hintText:
                                                AppStrings.selectVoiceGender,
                                            items: gender,
                                            selectedValues: selectedVoiceGender,
                                            onSelectionChanged: (newSelection) {
                                              setState(() {
                                                selectedVoiceGender =
                                                    newSelection;
                                              });
                                            },
                                          );
                                        },
                                      ),
                                      if (selectedVoiceGender.isNotEmpty) 16.ph,
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: ChipsWidget(
                                          items: selectedVoiceGender,
                                          onRemove: (item) {
                                            selectedVoiceGender.remove(item);
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                      !Responsive.isDesktop(context)
                                          ? 16.ph
                                          : 24.ph,
                                      BlocBuilder<StaticDataDropdownBloc,
                                          StaticDataDropdownState>(
                                        builder: (context, state) {
                                          List<DropdownData>? voiceType = [];
                                          if (state
                                              is StaticDataDropdownSuccessState) {
                                            voiceType.addAll(state
                                                    .dropDownResponseModel
                                                    ?.voiceType ??
                                                []);
                                          }
                                          return CustomMultiselectDropdown(
                                            isLoading: state
                                                is StaticDataDropdownLoadingState,
                                            isError: state
                                                is StaticDataDropdownErrorState,
                                            titleText: AppStrings.voiceType,
                                            hintText:
                                                AppStrings.selectVoiceType,
                                            items: voiceType,
                                            selectedValues: selectedVoiceType,
                                            onSelectionChanged: (newSelection) {
                                              setState(() {
                                                selectedVoiceType =
                                                    newSelection;
                                              });
                                            },
                                          );
                                        },
                                      ),
                                      if (selectedVoiceType.isNotEmpty) 16.ph,
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: ChipsWidget(
                                          items: selectedVoiceType,
                                          onRemove: (item) {
                                            selectedVoiceType.remove(item);
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                    ],
                                  )
                                : Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Flexible(
                                        child: Column(
                                          children: [
                                            BlocBuilder<StaticDataDropdownBloc,
                                                StaticDataDropdownState>(
                                              builder: (context, state) {
                                                List<DropdownData>? gender = [];
                                                if (state
                                                    is StaticDataDropdownSuccessState) {
                                                  gender.addAll(state
                                                          .dropDownResponseModel
                                                          ?.gender ??
                                                      []);
                                                }
                                                return CustomMultiselectDropdown(
                                                  isLoading: state
                                                      is StaticDataDropdownLoadingState,
                                                  isError: state
                                                      is StaticDataDropdownErrorState,
                                                  titleText:
                                                      AppStrings.voiceGender,
                                                  hintText: AppStrings
                                                      .selectVoiceGender,
                                                  items: gender,
                                                  selectedValues:
                                                      selectedVoiceGender,
                                                  onSelectionChanged:
                                                      (newSelection) {
                                                    setState(() {
                                                      selectedVoiceGender =
                                                          newSelection;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                            Align(
                                              alignment: Alignment.centerLeft,
                                              child: ChipsWidget(
                                                items: selectedVoiceGender,
                                                onRemove: (item) {
                                                  selectedVoiceGender
                                                      .remove(item);
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      30.pw,
                                      Flexible(
                                        child: Column(
                                          children: [
                                            BlocBuilder<StaticDataDropdownBloc,
                                                StaticDataDropdownState>(
                                              builder: (context, state) {
                                                List<DropdownData>? voiceType =
                                                    [];
                                                if (state
                                                    is StaticDataDropdownSuccessState) {
                                                  voiceType.addAll(state
                                                          .dropDownResponseModel
                                                          ?.voiceType ??
                                                      []);
                                                }
                                                return CustomMultiselectDropdown(
                                                  isLoading: state
                                                      is StaticDataDropdownLoadingState,
                                                  isError: state
                                                      is StaticDataDropdownErrorState,
                                                  titleText:
                                                      AppStrings.voiceType,
                                                  hintText: AppStrings
                                                      .selectVoiceType,
                                                  items: voiceType,
                                                  selectedValues:
                                                      selectedVoiceType,
                                                  onSelectionChanged:
                                                      (newSelection) {
                                                    setState(() {
                                                      selectedVoiceType =
                                                          newSelection;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                            Align(
                                              alignment: Alignment.centerLeft,
                                              child: ChipsWidget(
                                                items: selectedVoiceType,
                                                onRemove: (item) {
                                                  selectedVoiceType
                                                      .remove(item);
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            !Responsive.isDesktop(context)
                                ? Column(
                                    children: [
                                      BlocBuilder<StaticDataDropdownBloc,
                                          StaticDataDropdownState>(
                                        builder: (context, state) {
                                          List<DropdownData>? voiceCharacter =
                                              [];
                                          if (state
                                              is StaticDataDropdownSuccessState) {
                                            voiceCharacter.addAll(state
                                                    .dropDownResponseModel
                                                    ?.voiceCharacter ??
                                                []);
                                          }
                                          return CustomMultiselectDropdown(
                                            isLoading: state
                                                is StaticDataDropdownLoadingState,
                                            isError: state
                                                is StaticDataDropdownErrorState,
                                            titleText:
                                                AppStrings.voiceCharacter,
                                            hintText:
                                                AppStrings.selectVoiceCharacter,
                                            items: voiceCharacter,
                                            selectedValues: selectedVoiceChar,
                                            onSelectionChanged: (newSelection) {
                                              setState(() {
                                                selectedVoiceChar =
                                                    newSelection;
                                              });
                                            },
                                          );
                                        },
                                      ),
                                      if (selectedVoiceChar.isNotEmpty) 16.ph,
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: ChipsWidget(
                                          items: selectedVoiceChar,
                                          onRemove: (item) {
                                            selectedVoiceChar.remove(item);
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                      !Responsive.isDesktop(context)
                                          ? 16.ph
                                          : 24.ph,
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? experience = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      experience = List.from(
                                          state.dropDownResponseModel?.experience ?? []);
                                    }
                                    return CustomDropDownWidget<DropdownData>(
                                      isLoading: state
                                          is StaticDataDropdownLoadingState,
                                      isError:
                                          state is StaticDataDropdownErrorState,
                                      hintText: AppStrings.selectExperience,
                                      titleText: AppStrings.experience,
                                      selectedValue: selectedExperience?.name,
                                      items: experience
                                          .map((item) => DropdownMenuItem(
                                              value: item,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  TextTitle18And14(
                                                    item.name ?? "",
                                                  ),
                                                  if (item.id ==
                                                      selectedExperience?.id)
                                                    const Icon(Icons.check),
                                                ],
                                              )))
                                          .toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          selectedExperience = value;
                                        });
                                      },
                                      value: selectedExperience,
                                    );
                                  },
                                ),
                                    ],
                                  )
                                : Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Flexible(
                                        child: Column(
                                          children: [
                                            BlocBuilder<StaticDataDropdownBloc,
                                                StaticDataDropdownState>(
                                              builder: (context, state) {
                                                List<DropdownData>?
                                                    voiceCharacter = [];
                                                if (state
                                                    is StaticDataDropdownSuccessState) {
                                                  voiceCharacter.addAll(state
                                                          .dropDownResponseModel
                                                          ?.voiceCharacter ??
                                                      []);
                                                }
                                                return CustomMultiselectDropdown(
                                                  isLoading: state
                                                      is StaticDataDropdownLoadingState,
                                                  isError: state
                                                      is StaticDataDropdownErrorState,
                                                  titleText:
                                                      AppStrings.voiceCharacter,
                                                  hintText: AppStrings
                                                      .selectVoiceCharacter,
                                                  items: voiceCharacter,
                                                  selectedValues:
                                                      selectedVoiceChar,
                                                  onSelectionChanged:
                                                      (newSelection) {
                                                    setState(() {
                                                      selectedVoiceChar =
                                                          newSelection;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                            if (selectedVoiceChar.isNotEmpty)
                                              16.ph,
                                            Align(
                                              alignment: Alignment.centerLeft,
                                              child: ChipsWidget(
                                                items: selectedVoiceChar,
                                                onRemove: (item) {
                                                  selectedVoiceChar
                                                      .remove(item);
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      30.pw,
                                     
                                      Flexible(
                                        child: Column(
                                          children: [
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? experience = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      experience = List.from(
                                          state.dropDownResponseModel?.experience ?? []);
                                    }
                                    return CustomDropDownWidget<DropdownData>(
                                      isLoading: state
                                          is StaticDataDropdownLoadingState,
                                      isError:
                                          state is StaticDataDropdownErrorState,
                                      hintText: AppStrings.selectExperience,
                                      titleText: AppStrings.experience,
                                      selectedValue: selectedExperience?.name,
                                      items: experience
                                          .map((item) => DropdownMenuItem(
                                              value: item,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  TextTitle18And14(
                                                    item.name ?? "",
                                                  ),
                                                  if (item.id ==
                                                      selectedExperience?.id)
                                                    const Icon(Icons.check),
                                                ],
                                              )))
                                          .toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          selectedExperience = value;
                                        });
                                      },
                                      value: selectedExperience,
                                    );
                                  },
                                ),
                                  ],
                                    ),
                                      ),
                                    ],
                                  ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            BlocBuilder<StaticDataDropdownBloc,
                                                StaticDataDropdownState>(
                                              builder: (context, state) {
                                                List<AgeRange>? ageRange = [];
                                                if (state
                                                    is StaticDataDropdownSuccessState) {
                                                  ageRange.addAll(state
                                                          .dropDownResponseModel
                                                          ?.ageRange ??
                                                      []);
                                                }
                                                return CustomMultiselectDropdown(
                                                  isLoading: state
                                                      is StaticDataDropdownLoadingState,
                                                  isError: state
                                                      is StaticDataDropdownErrorState,
                                                  titleText:
                                                      AppStrings.voiceAge,
                                                  hintText:
                                                      AppStrings.selectVoiceAge,
                                                  items: ageRange,
                                                  selectedValues:
                                                      selectedVoiceAge,
                                                  onSelectionChanged:
                                                      (newSelection) {
                                                    setState(() {
                                                      selectedVoiceAge =
                                                          newSelection;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                            if (selectedVoiceAge.isNotEmpty)
                                              16.ph,
                                            Align(
                                              alignment: Alignment.centerLeft,
                                              child: ChipsWidget(
                                                items: selectedVoiceAge,
                                                onRemove: (item) {
                                                  selectedVoiceAge.remove(item);
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            BlocBuilder<StaticDataDropdownBloc,
                                StaticDataDropdownState>(
                              builder: (context, state) {
                                List<DropdownData>? voiceLanguage = [];
                                if (state is StaticDataDropdownSuccessState) {
                                  voiceLanguage.addAll(state
                                          .dropDownResponseModel
                                          ?.voiceLanguage ??
                                      []);
                                }
                                return CustomMultiselectDropdown(
                                  isLoading:
                                      state is StaticDataDropdownLoadingState,
                                  isError:
                                      state is StaticDataDropdownErrorState,
                                  hintText: AppStrings.selectLanguage,
                                  titleText: AppStrings.language,
                                  items: voiceLanguage,
                                  selectedValues: selectedLanguages,
                                  onSelectionChanged: (newSelection) {
                                    setState(() {
                                      selectedLanguages = newSelection;
                                    });
                                  },
                                );
                              },
                            ),
                            if (selectedLanguages.isNotEmpty) 16.ph,
                            Align(
                              alignment: Alignment.centerLeft,
                              child: ChipsWidget(
                                items: selectedLanguages,
                                onRemove: (item) {
                                  selectedLanguages.remove(item);
                                  setState(() {});
                                },
                              ),
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            BlocBuilder<StaticDataDropdownBloc,
                                StaticDataDropdownState>(
                              builder: (context, state) {
                                List<DropdownData>? accent = [];
                                if (state is StaticDataDropdownSuccessState) {
                                  accent.addAll(
                                      state.dropDownResponseModel?.accent ??
                                          []);
                                }
                                return CustomMultiselectDropdown(
                                  isLoading:
                                      state is StaticDataDropdownLoadingState,
                                  isError:
                                      state is StaticDataDropdownErrorState,
                                  hintText: AppStrings.selectAccent,
                                  titleText: AppStrings.accent,
                                  items: accent,
                                  selectedValues: selectedAccent,
                                  onSelectionChanged: (newSelection) {
                                    setState(() {
                                      selectedAccent = newSelection;
                                    });
                                  },
                                );
                              },
                            ),
                            if (selectedAccent.isNotEmpty) 16.ph,
                            Align(
                              alignment: Alignment.centerLeft,
                              child: ChipsWidget(
                                items: selectedAccent,
                                onRemove: (item) {
                                  selectedAccent.remove(item);
                                  setState(() {});
                                },
                              ),
                            ),
                            44.ph,
                            BlocListener<ProfileBloc, ProfileState>(
                              listener: (context, state) {
                                if (state is ProfileLoadingState) {
                                  Dialogs.showOnlyLoader(context);
                                }
                                if (state is ProfileSuccessState) {
                                  // Show Success Message
                                  showMessage(
                                      message: AppStrings.updated,
                                      isSuccess: true);
                                  context.read<UserBloc>().getUserData();
                                  context.pop();
                                  context.pop();
                                }
                                if (state is ProfileErrorState) {
                                  // Show Error Message
                                  context.pop();
                                  showMessage(
                                    message: state.errorMsg,
                                  );
                                }
                              },
                              child: Align(
                                alignment: Alignment.center,
                                child: PrimaryButton(
                                  width: !Responsive.isDesktop(context)
                                      ? null
                                      : MediaQuery.of(context).size.width * .6,
                                  onPressed: () {
                                    final isGenderChange = areListsNotEqualById(
                                        previousSelectedVoiceGender,
                                        selectedVoiceGender);
                                    final isTypeChange = areListsNotEqualById(
                                        previousSelectedVoiceType,
                                        selectedVoiceType);
                                    final isCharChange = areListsNotEqualById(
                                        previousSelectedVoiceChar,
                                        selectedVoiceChar);
                                    final isAgeChange = areAgeListsNotEqualById(
                                        previousSelectedVoiceAge,
                                        selectedVoiceAge);
                                    final isLangChange = areListsNotEqualById(
                                        previousSelectedVoiceLang,
                                        selectedLanguages);
                                    final isAccentChange = areListsNotEqualById(
                                        previousSelectedVoiceAccent,
                                        selectedAccent);
                                     final isExperienceChanged =
                                        userState.userDataModel.experience?.id != selectedExperience?.id;
                                    if (isGenderChange ||
                                        isTypeChange ||
                                        isCharChange ||
                                        isAgeChange ||
                                        isLangChange ||
                                        isAccentChange ||
                                        isExperienceChanged) {
                                      if (selectedVoiceType.isEmpty) {
                                        showMessage(
                                            message: ValidationMsg
                                                .plsSelectVoiceType);
                                        return;
                                      }
                                      if (selectedVoiceChar.isEmpty) {
                                        showMessage(
                                            message: ValidationMsg
                                                .plsSelectVoiceChar);
                                        return;
                                      }
                                      if (selectedExperience == null) {
                                        showMessage( message:ValidationMsg.plsSelect("experience"));
                                        return;
                                      }
                                      if (selectedVoiceAge.isEmpty) {
                                        showMessage(
                                            message: ValidationMsg
                                                .plsSelectVoiceAge);
                                        return;
                                      }
                                      if (selectedLanguages.isEmpty) {
                                        showMessage(
                                            message: ValidationMsg
                                                .plsSelectVoiceLang);
                                        return;
                                      }
                                      if (selectedAccent.isEmpty) {
                                        showMessage(
                                            message: ValidationMsg
                                                .plsSelectVoiceAccent);
                                        return;
                                      }
                                      context.read<ProfileBloc>().updateProfile(
                                              userInfoRequestModel:
                                                  UserInfoRequestModel(
                                            voiceCharacter: selectedVoiceChar
                                                .map((e) => e.id)
                                                .toList(),
                                            voiceLanguage: selectedLanguages
                                                .map((e) => e.id)
                                                .toList(),
                                            voiceType: selectedVoiceType
                                                .map((e) => e.id)
                                                .toList(),
                                            voiceGender: selectedVoiceGender
                                                .map((e) => e.id)
                                                .toList(),
                                            ageRange: selectedVoiceAge
                                                .map((e) => e.id)
                                                .toList(),
                                            accent: selectedAccent
                                                .map((e) => e.id)
                                                .toList(),
                                            voiceExperience: selectedExperience?.id
                                          ));
                                    } else {
                                      showMessage(
                                          message: AppStrings.noChangesMade);
                                    }
                                  },
                                  buttonText: AppStrings.save,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
      return const SizedBox();
    });
  }

  showMessage({required String message, bool isSuccess = false}) {
    CustomToast.show(context: context, message: message, isSuccess: isSuccess);
  }
}
