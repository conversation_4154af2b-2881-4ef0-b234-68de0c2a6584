import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:textfield_tags/textfield_tags.dart' as tag_textfield;
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/features/google_address/presentation.dart/address_input_field.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../common/user_data/data/user_data_model.dart';
import '../../../prefered_project_type/bloc/static_data_dropdown_state.dart';

class EditCreateProfile extends StatefulWidget {
  const EditCreateProfile({super.key});

  @override
  State<EditCreateProfile> createState() => _EditCreateProfileState();
}

class _EditCreateProfileState extends State<EditCreateProfile> {
  final formGlobalKey = GlobalKey<FormState>();
  late TextEditingController _bioController;
  late TextEditingController _genderController;
  late TextEditingController _streetAddressController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _postalCodeController;
  late TextEditingController _countryController;
  late TextEditingController _locationController;
  late TextEditingController _companyNameController;
  bool autovalidation = false;
  DropdownData? selectedGender;
  DropdownData? selectedIndustry;
  bool isSuccess = false;
  List<String>? initialTags = [];
  late tag_textfield.TextfieldTagsController tagController;
  UserDataModel? _previousUserData;
  Map<String, dynamic>? _selectedLocation;
  bool hideAddressField = false;

  @override
  void initState() {
    context.read<UserBloc>().getUserData();
    context.read<StaticDataDropdownBloc>().dropDownListApi();
    _bioController = TextEditingController();
    _genderController = TextEditingController();
    _streetAddressController = TextEditingController();
    _cityController = TextEditingController();
    _stateController = TextEditingController();
    _countryController = TextEditingController();
    _locationController = TextEditingController();
    _postalCodeController = TextEditingController();
    _companyNameController = TextEditingController();
    tagController = tag_textfield.TextfieldTagsController();
    super.initState();
  }

  @override
  void dispose() {
    _bioController.dispose();
    _genderController.dispose();
    _locationController.dispose();
    _streetAddressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _countryController.dispose();
    _postalCodeController.dispose();
    _companyNameController.dispose();
    super.dispose();
  }

  bool _areListsEqual(List<String> list1, List<String> list2) {
    list1.sort();
    list2.sort();
    return list1.join(',') == list2.join(',');
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
        if (state is UserLoadingState) {
          return const Center(child: Loader());
        }
        if (state is UserErrorState) {
          return ErrorScreen(
              onRetry: () {
                context.read<UserBloc>().getUserData();
              },
              errorMessage: state.errorMsg,
              imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                  height: 200, width: 100));
        }
        if (state is UserSuccessState) {
          bool isClient = (state.userDataModel.role == UserType.client);
          bool isVoice = (state.userDataModel.role == UserType.voice);
          bool isAncillary = (state.userDataModel.role == UserType.ancillaryService);
          if (_previousUserData != state.userDataModel) {
            _bioController.text = state.userDataModel.bio ?? "";
            _streetAddressController.text =
                state.userDataModel.streetAddress ?? "";
            _cityController.text = state.userDataModel.city ?? "";
            _stateController.text = state.userDataModel.state ?? "";
            _countryController.text = state.userDataModel.country ?? "";
            _postalCodeController.text = state.userDataModel.postalCode ?? '';
            _companyNameController.text = state.userDataModel.company ?? "";
            initialTags = List<String>.from(state.userDataModel.tags!);
            selectedGender = selectedGender ??
                DropdownData(
                  id: state.userDataModel.gender!.id,
                  name: state.userDataModel.gender!.name,
                );
            if (isClient) {
              selectedIndustry = selectedIndustry ??
                  DropdownData(
                    id: state.userDataModel.industry!.id,
                    name: state.userDataModel.industry!.name,
                  );
            }
            _previousUserData = state.userDataModel;
          }
          return SafeArea(
              child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: !Responsive.isDesktop(context)
                      ? EdgeInsets.zero
                      : EdgeInsets.all(44.0.h),
                  child: Card(
                    color: Theme.of(context).colorScheme.white,
                    elevation: !Responsive.isDesktop(context) ? 0 : 8,
                    shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                    child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal:
                                !Responsive.isDesktop(context) ? 16.h : 25.h,
                            vertical:
                                !Responsive.isDesktop(context) ? 24.h : 30.h),
                        child: SingleChildScrollView(
                          child: Form(
                            key: formGlobalKey,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: !Responsive.isDesktop(context)
                                  ? CrossAxisAlignment.center
                                  : CrossAxisAlignment.start,
                              children: [
                                if (!Responsive.isDesktop(context)) ...[
                                  Row(
                                    children: [
                                      CustomBackButton(
                                        onTap: () {
                                          context.pop();
                                        },
                                      ),
                                      8.pw,
                                      const Expanded(
                                        child: Center(
                                            child: Text24And20SemiBold(
                                                AppStrings.editProfile)),
                                      ),
                                    SizedBox(width: 32.w)
                                    ],
                                  ),
                                ] else
                                  Row(
                                    children: [
                                      const TextDisplayLarge36And26(
                                          AppStrings.editProfile),
                                      const Spacer(),
                                      InkWell(
                                        onTap: () {
                                          context.pop();
                                        },
                                        child: TextTitle18And14(
                                          AppStrings.cancel,
                                          color: colorScheme.hyperlinkBlueColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                !Responsive.isDesktop(context) ? 24.ph : 40.ph,
                                AppTextFormField(
                                  controller: _bioController,
                                  titleText: AppStrings.bio,
                                  hintText: AppStrings.tellUsALittleAboutYourself,
                                  maxLines: 3,
                                  validator: (value) {
                                    return Validator.emptyValidator(
                                        value, ValidationMsg.plsEnterBio);
                                  },
                                  keyboardType: TextInputType.multiline,
                                  textInputAction: TextInputAction.newline,
                                ),
                                !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? gender = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      gender = List.from(
                                          state.dropDownResponseModel?.gender ??
                                              []);
                                    }
                                    return CustomDropDownWidget<DropdownData>(
                                      isLoading: state
                                          is StaticDataDropdownLoadingState,
                                      isError:
                                          state is StaticDataDropdownErrorState,
                                      hintText: AppStrings.selectGender,
                                      titleText: AppStrings.gender,
                                      selectedValue: selectedGender?.name,
                                      items: gender
                                          .map((item) => DropdownMenuItem(
                                              value: item,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  TextTitle18And14(
                                                    item.name ?? "",
                                                  ),
                                                  if (item.id ==
                                                      selectedGender?.id)
                                                    const Icon(Icons.check),
                                                ],
                                              )))
                                          .toList(),
                                      onChanged: (value) {
                                        selectedGender = value;
                                        setState(() {});
                                      },
                                      value: selectedGender,
                                    );
                                  },
                                ),
                                !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                                if (isClient || isAncillary) ...[
                                  AppTextFormField(
                                    maxLength: 250,
                                    controller: _companyNameController,
                                    hintText: AppStrings.addCompanyName,
                                    titleText: isAncillary ? AppStrings.company.substring(
                                          0, AppStrings.company.length - 1) : AppStrings.company,
                                     validator: (value) {
                                    if (isClient) {
                                      return Validator.companyNameValidator(
                                          _companyNameController.text.trim(),
                                          ValidationMsg
                                              .plsEntervalidCompanyName)(value);
                                    }
                                    return null;
                                  },
                                  ),
                                  if (isClient) ...[
                                    !Responsive.isDesktop(context)
                                        ? 16.ph
                                        : 24.ph,
                                  BlocBuilder<StaticDataDropdownBloc,
                                      StaticDataDropdownState>(
                                    builder: (context, state) {
                                      List<DropdownData>? industry = [];
                                      if (state
                                          is StaticDataDropdownSuccessState) {
                                        industry = List.from(state
                                                .dropDownResponseModel
                                                ?.industry ??
                                            []);
                                      }
                                      return CustomDropDownWidget<DropdownData>(
                                        isLoading: state
                                            is StaticDataDropdownLoadingState,
                                        isError: state
                                            is StaticDataDropdownErrorState,
                                        hintText: AppStrings.selectIndustry,
                                        titleText: AppStrings.industry,
                                        selectedValue: selectedIndustry?.name,
                                        items: industry
                                            .map((item) => DropdownMenuItem(
                                                value: item,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    TextTitle18And14(
                                                      item.name ?? "",
                                                    ),
                                                    if (item.id ==
                                                        selectedIndustry?.id)
                                                      const Icon(Icons.check),
                                                  ],
                                                )))
                                            .toList(),
                                        onChanged: (value) {
                                          selectedIndustry = value;
                                          setState(() {});
                                        },
                                        value: selectedIndustry,
                                      );
                                    },
                                  ),],
                                ],
                                if (isVoice || isAncillary) ...[
                                  !Responsive.isDesktop(context)
                                      ? 16.ph
                                      : 24.ph,
                                  if (!hideAddressField)
                                    AddressInputField(
                                      addressController: _streetAddressController,
                                      cityController: _cityController,
                                      stateController: _stateController,
                                      postalController: _postalCodeController,
                                      countryController: _countryController,
                                      hintText: AppStrings.enterYourStreet,
                                      titleText: AppStrings.streetAddress,
                                      isAutovalidateModeOn: autovalidation,
                                      validator: (value) {
                                        return Validator.emptyValidator(value,
                                            ValidationMsg.plsEntervalidAddress);
                                      },
                                      onLocationSelected: (locationData) {
                                        setState(() {
                                          _selectedLocation = locationData;
                                        });
                                      },
                                    ),
                                  !Responsive.isDesktop(context)
                                      ? 16.ph
                                      : 24.ph,
                                  Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: AppTextFormField(
                                          controller: _cityController,
                                          titleText: AppStrings.city,
                                          hintText: AppStrings.enterYourCity,
                                          validator: (value) {
                                            return Validator.emptyValidator(
                                                value,
                                                ValidationMsg
                                                    .plsEntervalidCity);
                                          },
                                          inputFormatters: [
                                            LengthLimitingTextInputFormatter(
                                                50),
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z\s]'))
                                          ],
                                        ),
                                      ),
                                      24.pw,
                                      Expanded(
                                        child: AppTextFormField(
                                          controller: _stateController,
                                          titleText: AppStrings.state,
                                          hintText: AppStrings.enterYourState,
                                          maxLines: 1,
                                          validator: (value) {
                                            return Validator.emptyValidator(
                                                value,
                                                ValidationMsg
                                                    .plsEntervalidState);
                                          },
                                          inputFormatters: [
                                            LengthLimitingTextInputFormatter(
                                                50),
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z\s]')),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 16.ph
                                      : 24.ph,
                                  Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: AppTextFormField(
                                          controller: _postalCodeController,
                                          titleText: AppStrings.postalCode,
                                          hintText: AppStrings.enterPostalCode,
                                          validator: (value) {
                                            return Validator.postalCode(value!);
                                          },
                                          inputFormatters: [
                                            LengthLimitingTextInputFormatter(6),
                                            FilteringTextInputFormatter.allow(
                                                RegExp("[0-9]")),
                                          ],
                                        ),
                                      ),
                                      24.pw,
                                      Expanded(
                                        child: AppTextFormField(
                                          controller: _countryController,
                                          titleText: AppStrings.country,
                                          hintText: AppStrings.enterCountry,
                                          validator: (value) {
                                            return Validator.emptyValidator(
                                                value,
                                                ValidationMsg
                                                    .plsEntervalidCountry);
                                          },
                                          inputFormatters: [
                                            LengthLimitingTextInputFormatter(
                                                50),
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z\s]')),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (isVoice) ...[
                                    !Responsive.isDesktop(context)
                                        ? 16.ph
                                        : 24.ph,
                                  tag_textfield.TextFieldTags(
                                    textfieldTagsController: tagController,
                                    textSeparators: const [',', ' '],
                                    initialTags: initialTags,
                                    letterCase: tag_textfield.LetterCase.small,
                                    validator: (String tag) {
                                      if (tagController.getTags != null &&
                                          tagController.getTags!.isNotEmpty) {
                                        if (tagController.getTags!
                                            .contains(tag)) {
                                          return 'You already entered that';
                                        }
                                        if (tagController.getTags!.length > 9) {
                                          return 'Max 10 tags are allowed';
                                        }
                                      }
                                      return null;
                                    },
                                    inputfieldBuilder: (context, tec, fn, error,
                                        onChanged, onSubmitted) {
                                      return ((context, sc, tags, onTagDelete) {
                                        return Column(
                                          children: [
                                            AppTextFormField(
                                              controller: tec,
                                              focusNode: fn,
                                              isenabled: tags.length < 10,
                                              hintText: AppStrings.thisWillHelpClientsFindYou,
                                              titleText: AppStrings.tags,
                                              maxLength: 50,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        "[a-zA-Z0-9 ,_]")),
                                              ],
                                              onChanged: (value) {
                                                onChanged!(value);
                                              },
                                              onSubmitted: (value) {
                                                onSubmitted!(value);
                                                fn.requestFocus();
                                              },
                                              trailingTitleTextWidget: Tooltip(
                                                message: AppStrings.typeAndPressEnterToAddAKeyword,
                                                textStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                                                  color: Theme.of(context).colorScheme.white,
                                                  fontSize: 14.sp,
                                                ),
                                                child: Icon(
                                                  Icons.info_outline,
                                                  size: 20.h,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ),
                                            if (tags.isNotEmpty) 8.ph,
                                            Align(
                                              alignment: Alignment.centerLeft,
                                              child: ChipsWidget(
                                                items: tags,
                                                onRemove: (item) {
                                                  onTagDelete(item);
                                                },
                                              ),
                                            ),
                                          ],
                                        );
                                      });
                                    },
                                  ),]
                                ],
                                !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                                BlocListener<ProfileBloc, ProfileState>(
                                  listener: (context, state) {
                                    if (state is ProfileLoadingState) {
                                      if(Responsive.isMobile(context)){
                                        setState(() {
                                          hideAddressField = true;
                                        });
                                      }
                                      Dialogs.showOnlyLoader(context);
                                    }
                                    if (state is ProfileSuccessState) {
                                      CustomToast.show(
                                          context: context,
                                          message: AppStrings.updated,
                                          isSuccess: true);

                                      context.read<UserBloc>().getUserData();
                                      context.pop();
                                      context.pop();
                                     hideAddressField = false;
                                    }
                                    if (state is ProfileErrorState) {
                                      context.pop();
                                      CustomToast.show(
                                          context: context,
                                          message: state.errorMsg,
                                          isSuccess: true);
                                    }
                                  },
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: PrimaryButton(
                                        width: !Responsive.isDesktop(context)
                                            ? null
                                            : MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                .6,
                                        buttonText: AppStrings.save,
                                        onPressed: () {
                                          if (!formGlobalKey.currentState!
                                              .validate()) return;
                                          final isGenderChanged =
                                              state.userDataModel.gender?.id !=
                                                  selectedGender?.id;
                                          final isBioChanged =
                                              _bioController.text.trim() !=
                                                  state.userDataModel.bio;
                                          final isCompanyChanged =
                                              _companyNameController.text
                                                      .trim() !=
                                                  state.userDataModel.company;
                                          final isIndustryChanged = state
                                                  .userDataModel.industry?.id !=
                                              selectedIndustry?.id;
                                          final isStreetChanged =
                                              _streetAddressController.text
                                                      .trim() !=
                                                  state.userDataModel
                                                      .streetAddress;
                                          final isCityChanged =
                                              _cityController.text.trim() !=
                                                  state.userDataModel.city;
                                          final isStateChanged =
                                              _stateController.text.trim() !=
                                                  state.userDataModel.state;
                                          final isCountryChanged = _countryController.text.trim() != state.userDataModel.country;
                                          final isPostalCodeChanged =
                                              _postalCodeController.text
                                                      .trim() !=
                                                  state
                                                      .userDataModel.postalCode;                                          
                                          bool isTagsChanged = false;
                                          if (isVoice) {
                                            final currentTags = tagController
                                                    .getTags
                                                    ?.map((e) => e.toString())
                                                    .toList() ??
                                                [];
                                            isTagsChanged = !_areListsEqual(
                                                currentTags, initialTags!);
                                          }

                                          final userInfoRequestModel =
                                              UserInfoRequestModel(
                                            gender: isGenderChanged
                                                ? selectedGender?.id
                                                : null,
                                            bio: isBioChanged
                                                ? _bioController.text.trim()
                                                : null,
                                            company: isCompanyChanged
                                                ? _companyNameController.text
                                                    .trim()
                                                : null,
                                            industry: isIndustryChanged
                                                ? selectedIndustry?.id
                                                : null,
                                            streetAddress: isStreetChanged
                                                ? _streetAddressController.text
                                                    .trim()
                                                : null,
                                            city: isCityChanged
                                                ? _cityController.text.trim()
                                                : null,
                                            state: isStateChanged
                                                ? _stateController.text.trim()
                                                : null,
                                            country: isCountryChanged ? _countryController.text.trim() : null,
                                            postalCode: isPostalCodeChanged
                                                ? int.tryParse(
                                                    _postalCodeController.text
                                                        .trim())
                                                : null,
                                            tag: isTagsChanged
                                                ? tagController.getTags
                                                : null,
                                            lat: _selectedLocation?['location']['lat'],
                                            lng: _selectedLocation?['location']['lng'],
                                          );
                                          if ((!isClient &&
                                                  (isBioChanged ||
                                                      isGenderChanged ||
                                                      isStreetChanged ||
                                                      isCityChanged ||
                                                      isStateChanged ||
                                                      isCountryChanged ||
                                                      isPostalCodeChanged ||
                                                      isCountryChanged ||
                                                      isTagsChanged)) ||
                                              ((isClient || isAncillary) &&
                                                  (isCompanyChanged ||
                                                      isIndustryChanged ||
                                                      isBioChanged ||
                                                      isGenderChanged))) {
                                            context
                                                .read<ProfileBloc>()
                                                .updateProfile(
                                                    userInfoRequestModel:
                                                        userInfoRequestModel);
                                          } else {
                                            CustomToast.show(
                                              context: context,
                                              message: AppStrings.noChangesMade,
                                              isSuccess: false,
                                            );
                                          }
                                        }),
                                  ),
                                )
                              ],
                            ),
                          ),
                        )),
                  ),
                ),
              ),
            ],
          ));
        }
        return const SizedBox();
      }),
    );
  }
}
