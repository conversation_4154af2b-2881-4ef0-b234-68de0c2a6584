import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class EditPreferredProjectType extends StatefulWidget {
  const EditPreferredProjectType({super.key});

  @override
  State<EditPreferredProjectType> createState() =>
      _EditPreferredProjectTypeState();
}

class _EditPreferredProjectTypeState extends State<EditPreferredProjectType> {
  final formGlobalKey = GlobalKey<FormState>();
  List<DropdownData> selectedProjectType = [];
  bool _initialDataLoaded = false;

  @override
  void initState() {
    super.initState();
    context.read<UserBloc>().getUserData();
    context.read<StaticDataDropdownBloc>().dropDownListApi();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocBuilder<StaticDataDropdownBloc, StaticDataDropdownState>(
        builder: (context, staticState) {
          if (staticState is StaticDataDropdownLoadingState) {
            return const Center(child: Loader());
          }
          if (staticState is StaticDataDropdownErrorState) {
            return ErrorScreen(
                onRetry: () {
                  context.read<StaticDataDropdownBloc>().dropDownListApi();
                },
                errorMessage: staticState.errorMsg,
                imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                    height: 200.h, width: 100.w));
          }
          return BlocBuilder<UserBloc, UserState>(
            builder: (context, userState) {
              if (userState is UserLoadingState) {
                return const Center(child: CircularProgressIndicator());
              }
              if (userState is UserErrorState) {
                return ErrorScreen(
                    onRetry: () {
                      context.read<UserBloc>().getUserData();
                    },
                    errorMessage: userState.errorMsg,
                    imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                        height: 200.h, width: 100.w));
              }
              if (userState is UserSuccessState &&
                  staticState is StaticDataDropdownSuccessState) {
                final projectTypes =
                    staticState.dropDownResponseModel?.projectType ?? [];
                // Initialize selected items only once
                if (!_initialDataLoaded) {
                  selectedProjectType = userState.userDataModel.projectType!
                      .map((userProj) => projectTypes.firstWhere(
                          (staticProj) => staticProj.id == userProj.id))
                      .toList();
                  _initialDataLoaded = true;
                }

                return SafeArea(
                  child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: !Responsive.isDesktop(context)
                              ? EdgeInsets.zero
                              : EdgeInsets.all(44.0.h),
                          child: Card(
                            color: Theme.of(context).colorScheme.white,
                            elevation: !Responsive.isDesktop(context) ? 0 : 8,
                            shadowColor:
                                Theme.of(context).colorScheme.lightGreyD9D9D9,
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal:
                                    !Responsive.isDesktop(context)
                                    ? 16.h
                                    : 52.h,
                                vertical:
                                    !Responsive.isDesktop(context)
                                    ? 24.h
                                    : 48.h,
                              ),
                              child: Column(
                                    children: [
                                  if (!Responsive.isDesktop(context)) ...[
                                        Row(
                                          children: [
                                            CustomBackButton(
                                              onTap: () {
                                                context.pop();
                                              },
                                            ),
                                            8.pw,
                                            const Expanded(
                                              child: Center(
                                                  child: Text24And20SemiBold(
                                                      AppStrings
                                                          .editPreferredProjectType)),
                                            ),
                                            SizedBox(width: 32.w)
                                          ],
                                        ),
                                      ] else
                                        Row(
                                          children: [
                                            const TextDisplayLarge36And26(
                                                AppStrings
                                                    .editPreferredProjectType),
                                            const Spacer(),
                                            InkWell(
                                              onTap: () {
                                                context.pop();
                                              },
                                              child: TextTitle18And14(
                                                AppStrings.cancel,
                                                color: colorScheme
                                                    .hyperlinkBlueColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                  !Responsive.isDesktop(context)
                                          ? 24.ph
                                          : 40.ph,
                                      CustomMultiselectDropdown(
                                        isLoading: false,
                                        isError: false,
                                        hintText: AppStrings.projectType,
                                        titleText: AppStrings.select,
                                        items: projectTypes,
                                        selectedValues: selectedProjectType,
                                        onSelectionChanged: (newSelection) {
                                          setState(() {
                                            selectedProjectType = newSelection;
                                          });
                                        },
                                      ),
                                      if (selectedProjectType.isNotEmpty) 16.ph,
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: ChipsWidget(
                                          items: selectedProjectType,
                                          onRemove: (item) {
                                            setState(() {
                                              selectedProjectType.removeWhere(
                                                  (e) => e.id == item.id);
                                            });
                                          },
                                        ),
                                      ),
                                      const Spacer(),
                                      BlocListener<ProfileBloc, ProfileState>(
                                        listener: (context, state) {
                                          if (state is ProfileLoadingState) {
                                            Dialogs.showOnlyLoader(context);
                                          }
                                          if (state is ProfileSuccessState) {
                                            CustomToast.show(
                                                context: context,
                                                message: AppStrings.updated,
                                                isSuccess: true);
                                            context
                                                .read<UserBloc>()
                                                .getUserData();
                                            context.pop();
                                            context.pop();
                                          }
                                          if (state is ProfileErrorState) {
                                            context.pop();
                                            CustomToast.show(
                                                context: context,
                                                message: state.errorMsg,
                                                isSuccess: false);
                                          }
                                        },
                                        child: Align(
                                          alignment: Alignment.center,
                                          child: PrimaryButton(
                                        width: !Responsive.isDesktop(context)
                                            ? null
                                            : MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                .6,
                                            onPressed: () {
                                              if (selectedProjectType.isEmpty) {
                                                CustomToast.show(
                                                  context: context,
                                                  message: ValidationMsg
                                                      .plsSelectProjectType,
                                                );
                                                return;
                                              }
                                              final initialProjectTypeIds =
                                                  userState.userDataModel
                                                      .projectType!
                                                      .map((p) => p.id)
                                                      .toSet();
                                              final selectedProjectTypeIds =
                                                  selectedProjectType
                                                      .map((p) => p.id)
                                                      .toSet();

                                              // Identify added items
                                              final addedItems =
                                                  selectedProjectTypeIds
                                                      .difference(
                                                          initialProjectTypeIds);
                                              // Identify removed items
                                              final removedItems =
                                                  initialProjectTypeIds
                                                      .difference(
                                                          selectedProjectTypeIds);

                                              // If no additions or removals, show "No changes made" message
                                              if (addedItems.isEmpty &&
                                                  removedItems.isEmpty) {
                                                CustomToast.show(
                                                  context: context,
                                                  message:
                                                      AppStrings.noChangesMade,
                                                );
                                                return;
                                              }
                                              context
                                                  .read<ProfileBloc>()
                                                  .updateProfile(
                                                    userInfoRequestModel:
                                                        UserInfoRequestModel(
                                                      projectType:
                                                          selectedProjectType
                                                              .map((project) =>
                                                                  project.id)
                                                              .toList(),
                                                    ),
                                                  );
                                            },
                                            buttonText: AppStrings.save,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox();
            },
          );
        },
      ),
    );
  }
}
