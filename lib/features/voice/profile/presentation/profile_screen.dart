import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/report/bloc/report_bloc.dart';
import 'package:the_voice_directory_flutter/features/report/bloc/report_state.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_card_widget.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../../../../utils/environment_config.dart';
import '../../../../utils/get_profile_image.dart';
import '../../../../widgets/buttons/back_button_arrow.dart';
import '../../../chat/bloc/chat_bloc.dart';

class ProfileScreen extends StatefulWidget {
  final int? id;
  const ProfileScreen({super.key, this.id});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late UserBloc userBlocCubit;
  UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
      HiveBoxName.user, HiveKeys.userData);
  final commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    userBlocCubit = context.read<UserBloc>();
    context.read<UserBloc>().getUserData(id: widget.id);
  }

  @override
  void dispose() {
    _disposeAudioPlayers();
    commentController.dispose();
    super.dispose();
  }

  void _disposeAudioPlayers() {
    if (userBlocCubit.state is UserSuccessState) {
      final currentState = userBlocCubit.state as UserSuccessState;
      for (final player in currentState.audioPlayers) {
        player.dispose();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: BlocListener<ReportBloc, ReportState>(
        listener: (_, state) {
          if (state is ReportLoadingState) {
            Dialogs.showOnlyLoader(context);
          }
          if (state is ReportSuccessState) {
            context.pop();
            CustomToast.show(
                context: context,
                isSuccess: true,
                message: "User reported successfully");
          }
          if (state is ReportErrorState) {
            context.pop();
            CustomToast.show(context: context, message: state.errorMsg);
          }
        },
        child: BlocBuilder<UserBloc, UserState>(builder: (context, state) {
          if (state is UserLoadingState) {
            return const Center(child: Loader());
          }
          if (state is UserErrorState) {
            return ErrorScreen(
              onRetry: () {
                context.read<UserBloc>().getUserData(id: widget.id);
              },
              errorMessage: state.errorMsg,
              imageWidget: SvgPicture.asset(AppImages.snapMomentIcon,
                  height: 200, width: 100),
            );
          }

        if (state is UserSuccessState) {
          bool isClient = state.userDataModel.role == UserType.client;
          bool isVoice = state.userDataModel.role == UserType.voice;
          bool isAncillary = state.userDataModel.role == UserType.ancillaryService;
          final List<NameIdModel> voiceGenderList =
              state.userDataModel.voiceGender ?? [];
          final List<NameIdModel> voiceTypeList =
              state.userDataModel.voiceType ?? [];
          final List<AgeRange> voiceAgeList =
              state.userDataModel.ageRange ?? [];
          final List<NameIdModel> voiceCharList =
              state.userDataModel.voiceCharacter ?? [];
          final List<NameIdModel> voiceLangList =
              state.userDataModel.voiceLanguage ?? [];
          final List<NameIdModel> voiceAccentList =
              state.userDataModel.voiceAccent ?? [];
          return SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(
                      horizontal: !Responsive.isDesktop(context) ? 0.h : 80.h,
                      vertical: !Responsive.isDesktop(context) ? 0.h : 40.h),
                  child: Column(
                    children: [
                      if (!!Responsive.isDesktop(context)) ...[
                        CustomBackButtonArrow(),
                        24.ph,
                      ],
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal:
                                !Responsive.isDesktop(context) ? 16.h : 0.h,
                            vertical:
                                !Responsive.isDesktop(context) ? 24.h : 0.h),
                        child: Column(
                          crossAxisAlignment: !Responsive.isDesktop(context)
                              ? CrossAxisAlignment.center
                              : CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                !Responsive.isDesktop(context)
                                    ? CustomBackButton()
                                    : const SizedBox(),
                                if (state.userDataModel.id ==
                                        userDataModel?.id &&
                                    !Responsive.isDesktop(context)) ...[
                                  Expanded(
                                    child: Center(
                                      child: const TextDisplayLarge36And26(
                                          AppStrings.myProfile),
                                    ),
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 52.ph,
                                ],
                                SizedBox(width: 32.w),
                                if (!Responsive.isDesktop(context) &&
                                    state.userDataModel.id !=
                                        userDataModel?.id) ...[
                                  Spacer(),
                                  InkWell(
                                    onTap: () {
                                      showReportDialog(context);
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(6),
                                      decoration: BoxDecoration(
                                        color: colorScheme.white,
                                        borderRadius: BorderRadius.circular(30),
                                        border: Border.all(
                                            color: colorScheme.redFD3503),
                                        boxShadow: const [
                                          BoxShadow(
                                            color: Colors.black12,
                                            blurRadius: 2,
                                            offset: Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: SvgPicture.asset(
                                          AppImages.reportIc,
                                          height: 20,
                                          width: 20),
                                    ),
                                  ),
                                  if (state.userDataModel.role == UserType.ancillaryService) ...[
                                    16.pw,
                                    InkWell(
                                      onTap: () {
                                        final idFrom = userDataModel?.id;
                                        final idTo = state.userDataModel.id;
                                        if (idFrom == null || idTo == null) {
                                          CustomToast.show(
                                              context: context,
                                              message: AppStrings.genericErrorMsg);
                                          return;
                                        }
                                        String groupId = '';
                                        if (idFrom < idTo) {
                                          groupId = "${idFrom}_$idTo";
                                        } else {
                                          groupId = "${idTo}_$idFrom";
                                        }
                                        String? myName = '${userDataModel?.firstName ?? ''} ${userDataModel?.lastName ?? ''}';
                                        String? myProfileImg = ProfileImage.getProfileImage(userDataModel?.profilePic);
                                        String? otherProfileImg = ProfileImage.getProfileImage(state.userDataModel.profilePic);
                                        context.read<ChatBloc>().updateChatState(
                                                selectedChat: groupId,
                                                isFromChatList: false,
                                                isJobChat: false);
                                        NavigationServiceImpl.getInstance()!.doNavigation(
                                          context,
                                          routeName: RouteName.chat,
                                          pathParameters: {
                                            Params.jobName: 'ND',
                                            Params.groupId: groupId,
                                            Params.jobId: 'ND',
                                            Params.idFrom: idFrom.toString(),
                                            Params.idTo: idTo.toString(),
                                            Params.myName: myName,
                                            Params.myProfileImg: myProfileImg,
                                            Params.name: "${state.userDataModel.firstName} ${state.userDataModel.lastName}",
                                            Params.profileImg: otherProfileImg,
                                            Params.needBackBtn: 'true',
                                          },
                                        );
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: colorScheme.white,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          border: Border.all(
                                              color: colorScheme.hyperlinkBlueColor),
                                          boxShadow: const [
                                            BoxShadow(
                                              color: Colors.black12,
                                              blurRadius: 2,
                                              offset: Offset(0, 1),
                                            ),
                                        ],
                                      ),
                                        child: SvgPicture.asset(
                                            AppImages.chatIc,
                                            colorFilter: ColorFilter.mode(
                                                colorScheme.hyperlinkBlueColor,
                                                BlendMode.srcIn),
                                            height: 20,
                                            width: 20),
                                    ),
                                    ),
                                  ],
                                ]
                              ],
                            ),
                            Column(
                                crossAxisAlignment:
                                    !Responsive.isDesktop(context)
                                    ? CrossAxisAlignment.center
                                    : CrossAxisAlignment.start,
                                children: [
                                  !Responsive.isDesktop(context)
                                      ? Column(
                                          children: [
                                            SizedBox(
                                              width: 140.h,
                                              height: 140.h,
                                              child: Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  CircleAvatar(
                                                    radius: 60.r,
                                                    backgroundColor:
                                                        colorScheme.blackE8E8E8,
                                                    backgroundImage: state.userDataModel.profilePic?.isNotEmpty == true
                                                                ? Uri.parse(state.userDataModel.profilePic ?? '').isAbsolute
                                                                ? NetworkImage(state.userDataModel.profilePic ??'')
                                                                : NetworkImage("${EnvironmentConfig.imageBaseUrl}${state.userDataModel.profilePic}") : null,
                                                    child: state.userDataModel.profilePic?.isNotEmpty == true
                                                                ? null
                                                                : SvgPicture.asset("assets/images/user_icon.svg"),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            16.ph,
                                            TextDisplayLarge24And16(
                                                '${state.userDataModel.firstName} ${state.userDataModel.lastName}',
                                                maxLines: 1,
                                                overflow:
                                                    TextOverflow.ellipsis),
                                            if((userDataModel?.role == UserType.ancillaryService) || (state.userDataModel.role == UserType.ancillaryService))...[
                                              12.ph,
                                              TextTitle14(state.userDataModel.services?.map((e) => e.name).join("  •  ") ?? "",),
                                            ],
                                            12.ph,
                                            if (state.userDataModel.id ==
                                                userDataModel?.id) ...[
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                      AppImages.callIcon),
                                                  8.pw,
                                                  TextTitle14(
                                                      '+${state.userDataModel.countryCode} ${state.userDataModel.phoneNumber}'),
                                                ],
                                              ),
                                              6.ph,
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                      AppImages.smsIcon),
                                                  8.pw,
                                                  TextTitle14(state
                                                          .userDataModel
                                                          .email ??
                                                      '')
                                                ],
                                              ),
                                              4.ph,
                                            ],
                                            if (state.userDataModel.id ==
                                                userDataModel?.id) ...[
                                              InkWell(
                                                onTap: () =>
                                                    NavigationServiceImpl
                                                            .getInstance()!
                                                        .doNavigation(
                                                  context,
                                                  routeName: RouteName
                                                      .editBasicDetails,
                                                ),
                                                child: TextTitle18And14(
                                                  AppStrings.edit,
                                                  color: colorScheme
                                                      .hyperlinkBlueColor,
                                                ),
                                              ),
                                            ]
                                          ],
                                        )
                                      : CommonShadowContainer(
                                          child: Padding(
                                            padding: EdgeInsets.all(
                                                !Responsive.isDesktop(context)
                                                    ? 12.h
                                                    : 28.h),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                if (state.userDataModel.id !=
                                                    userDataModel?.id) ...[
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      TextDisplayLarge24And16(
                                                        state.userDataModel.role == UserType.ancillaryService
                                                            ? "Ancillary Service Details"
                                                            : state.userDataModel.role == UserType.client
                                                                ? AppStrings.clientDetails
                                                                : AppStrings.voiceDetails,
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis),
                                                      if (state.userDataModel
                                                              .id !=
                                                          userDataModel?.id)
                                                        Row(
                                                          children: [
                                                            InkWell(
                                                              onTap: () {
                                                                showReportDialog(
                                                                    context);
                                                              },
                                                              child: Container(
                                                                width: 124.w,
                                                                padding: EdgeInsets
                                                                    .symmetric(
                                                                        vertical:
                                                                            8.h),
                                                                alignment:
                                                                    Alignment
                                                                        .center,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color:
                                                                      colorScheme
                                                                          .white,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              30),
                                                                  border: Border.all(
                                                                      color: colorScheme
                                                                          .redFD3503),
                                                                  boxShadow: const [
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .black12,
                                                                      blurRadius:
                                                                          2,
                                                                      offset:
                                                                          Offset(
                                                                              0,
                                                                              1),
                                                                    ),
                                                                  ],
                                                                ),
                                                                child: Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    SvgPicture.asset(
                                                                        AppImages
                                                                            .reportIc),
                                                                    6.pw,
                                                                    TextTitle14(
                                                                        AppStrings
                                                                            .reportUser,
                                                                        color: colorScheme
                                                                            .redFD3503),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                            if (state.userDataModel.role == UserType.ancillaryService) ...[
                                                              16.pw,
                                                              InkWell(
                                                                onTap: () {
                                                                  final idFrom = userDataModel?.id;
                                                                  final idTo = state.userDataModel.id;
                                                                  if (idFrom == null || idTo == null) {
                                                                    CustomToast.show(context: context, message: AppStrings.genericErrorMsg);
                                                                    return;
                                                                  }
                                                                  String groupId = '';
                                                                  if (idFrom < idTo) {
                                                                    groupId = "${idFrom}_$idTo";
                                                                  } else {
                                                                    groupId = "${idTo}_$idFrom";
                                                                  }
                                                                  String? myName = '${userDataModel?.firstName ?? ''} ${userDataModel?.lastName ?? ''}';
                                                                  String? myProfileImg = ProfileImage.getProfileImage(userDataModel?.profilePic);
                                                                  String? otherProfileImg = ProfileImage.getProfileImage(state.userDataModel.profilePic);
                                                                  context.read<ChatBloc>().updateChatState(selectedChat: groupId, isFromChatList: false, isJobChat: false);
                                                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.chat,
                                                                    pathParameters: {
                                                                      Params.jobName: 'ND',
                                                                      Params.groupId: groupId,
                                                                      Params.jobId: 'ND',
                                                                      Params.idFrom: idFrom.toString(),
                                                                      Params.idTo: idTo.toString(),
                                                                      Params.myName: myName,
                                                                      Params.myProfileImg: myProfileImg,
                                                                      Params.name: "${state.userDataModel.firstName} ${state.userDataModel.lastName}",
                                                                      Params.profileImg: otherProfileImg,
                                                                      Params.needBackBtn: 'true',
                                                                    },
                                                                  );
                                                                },
                                                                child:
                                                                    Container(
                                                                  width: 124.w,
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          vertical:
                                                                              8.h),
                                                                  alignment:
                                                                      Alignment
                                                                          .center,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: colorScheme
                                                                        .white,
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            30),
                                                                    border: Border.all(
                                                                        color: colorScheme
                                                                            .hyperlinkBlueColor),
                                                                    boxShadow: const [
                                                                      BoxShadow(
                                                                        color: Colors
                                                                            .black12,
                                                                        blurRadius:
                                                                            2,
                                                                        offset: Offset(
                                                                            0,
                                                                            1),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .min,
                                                                    children: [
                                                                      SvgPicture
                                                                          .asset(
                                                                        AppImages
                                                                            .chatIc,
                                                                        height:
                                                                            20,
                                                                        colorFilter: ColorFilter.mode(
                                                                            colorScheme.hyperlinkBlueColor,
                                                                            BlendMode.srcIn),
                                                                      ),
                                                                      6.pw,
                                                                      TextTitle14(
                                                                          AppStrings
                                                                              .chat,
                                                                          color:
                                                                              colorScheme.hyperlinkBlueColor),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ],
                                                        ),
                                                    ],
                                                  ),
                                                  30.ph,
                                                ],
                                                Stack(
                                                  children: [
                                                    Row(
                                                      children: [
                                                        SizedBox(
                                                          width: 120.h,
                                                          height: 120.h,
                                                          child: CircleAvatar(
                                                            radius: 60.r,
                                                            backgroundColor:
                                                                colorScheme
                                                                    .blackE8E8E8,
                                                            backgroundImage: () {
                                                              return state.userDataModel.profilePic?.isNotEmpty == true
                                                                  ? Uri.parse(state.userDataModel.profilePic ?? '').isAbsolute
                                                                      ? NetworkImage(state.userDataModel.profilePic ??'')
                                                                      : NetworkImage("${EnvironmentConfig.imageBaseUrl}${state.userDataModel.profilePic}")
                                                                  : null;
                                                            }(),
                                                            child: state.userDataModel.profilePic?.isNotEmpty == true
                                                                ? null
                                                                : SvgPicture.asset("assets/images/user_icon.svg"),
                                                          ),
                                                        ),
                                                        16.pw,
                                                        Expanded(
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              TextDisplayLarge24And16(
                                                                  '${state.userDataModel.firstName} ${state.userDataModel.lastName}',
                                                                  maxLines: 1,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis),
                                                           if((userDataModel?.role == UserType.ancillaryService) || (state.userDataModel.role == UserType.ancillaryService))...[
                                                              12.ph,
                                                              TextTitle14(state.userDataModel.services?.map((e) => e.name).join(" • ") ?? ""),
                                                              ],
                                                              if (state
                                                                      .userDataModel
                                                                      .id ==
                                                                  userDataModel
                                                                      ?.id) ...[
                                                                20.ph,
                                                                Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    SvgPicture.asset(
                                                                        AppImages
                                                                            .callIcon),
                                                                    8.pw,
                                                                    Flexible(
                                                                      flex: 2,
                                                                      child: TextTitle14(
                                                                          '+${state.userDataModel.countryCode} ${state.userDataModel.phoneNumber}',
                                                                          maxLines:
                                                                              1,
                                                                          overflow:
                                                                              TextOverflow.ellipsis),
                                                                    ),
                                                                    10.pw,
                                                                    Container(
                                                                      width: 8,
                                                                      height: 8,
                                                                      decoration:
                                                                          BoxDecoration(
                                                                        color: colorScheme
                                                                            .lightGreyB2B2B2,
                                                                        shape: BoxShape
                                                                            .circle,
                                                                      ),
                                                                    ),
                                                                    12.pw,
                                                                    SvgPicture.asset(
                                                                        AppImages
                                                                            .smsIcon),
                                                                    8.pw,
                                                                    Flexible(
                                                                      flex: 3,
                                                                      child: TextTitle14(
                                                                          state.userDataModel.email ??
                                                                              '',
                                                                          maxLines:
                                                                              1,
                                                                          overflow:
                                                                              TextOverflow.ellipsis),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ],
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    if (state
                                                            .userDataModel.id ==
                                                        userDataModel?.id) ...[
                                                      Positioned(
                                                        top: 0,
                                                        right: 0,
                                                        child: InkWell(
                                                          onTap: () =>
                                                              NavigationServiceImpl
                                                                      .getInstance()!
                                                                  .doNavigation(
                                                            context,
                                                            routeName: RouteName
                                                                .editBasicDetails,
                                                          ),
                                                          child:
                                                              TextTitle18And14(
                                                            AppStrings.edit,
                                                            color: colorScheme
                                                                .hyperlinkBlueColor,
                                                          ),
                                                        ),
                                                      ),
                                                    ]
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),

                                  if (isVoice &&
                                      state.userDataModel.id !=
                                          userDataModel?.id &&
                                      state.userDataModel.serverAuthCode ==
                                          true) ...[
                                    !Responsive.isDesktop(context)
                                        ? 24.ph
                                        : 28.ph,
                                    SizedBox(
                                      width: double.infinity,
                                      child: CommonShadowContainer(
                                        child: Padding(
                                          padding: EdgeInsets.all(
                                              !Responsive.isDesktop(context)
                                                  ? 12.h
                                                  : 28.h),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text24And20SemiBold(
                                                AppStrings.bookVoice
                                                    .toUpperCase(),
                                                style: theme
                                                    .textTheme.titleLarge!
                                                    .copyWith(
                                                  color: colorScheme.black,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ),
                                              4.ph,
                                              const TextTitle14(AppStrings
                                                  .youCanScheduleSlot),
                                              20.ph,
                                              InkWell(
                                                onTap: () =>
                                                    NavigationServiceImpl
                                                            .getInstance()!
                                                        .doNavigation(context,
                                                            routeName: RouteName
                                                                .googleCalender,
                                                            pathParameters: {
                                                      Params.id:
                                                          widget.id.toString()
                                                    }),
                                                child: Container(
                                                    decoration: BoxDecoration(
                                                        color: colorScheme
                                                            .hyperlinkBlueColor,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    40.r)),
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 6.h,
                                                            horizontal: 12.w),
                                                    child: TextTitle14(
                                                      AppStrings.bookAslot,
                                                      color: colorScheme.white,
                                                    )),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                  Responsive.isMobile(context) ? 24.ph : 28.ph,
                                  if (isClient || isAncillary) ...[
                                    CommonShadowContainer(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text24And20SemiBold(
                                                  AppStrings.details,
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                    color: colorScheme.black,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                if (state.userDataModel.id ==
                                                    userDataModel?.id) ...[
                                                  InkWell(
                                                    onTap: () =>
                                                        NavigationServiceImpl
                                                                .getInstance()!
                                                            .doNavigation(
                                                      context,
                                                      routeName: RouteName
                                                          .editProfileDetails,
                                                    ),
                                                    child: TextTitle18And14(
                                                      AppStrings.edit,
                                                      color: colorScheme
                                                          .hyperlinkBlueColor,
                                                    ),
                                                  ),
                                                ]
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                              state.userDataModel.bio ?? "",
                                            ),
                                            4.ph,
                                            const TextTitle14(AppStrings.bio_),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(state.userDataModel
                                                    .gender?.name ??
                                                ''),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.gender_),
                                            if (state.userDataModel.company != null && state.userDataModel.company!.isNotEmpty) ...[
                                              !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                                state.userDataModel.company ??
                                                    ""),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.company_),
                                            ],
                                            if(isClient)...[
                                              !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(state.userDataModel
                                                    .industry?.name ??
                                                ""),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.industry_),
                                            ],
                                            if(isAncillary)...[
                                             Responsive.isMobile(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                              "${state.userDataModel.streetAddress}, ${state.userDataModel.city}, ${state.userDataModel.state}, ${state.userDataModel.postalCode}, ${state.userDataModel.country}",
                                            ),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.address),
                                            ]        
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                  if (isVoice) ...[
                                    CommonShadowContainer(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text24And20SemiBold(
                                                  AppStrings.details,
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                const Spacer(),
                                                if (state.userDataModel.id ==
                                                    userDataModel?.id) ...[
                                                  InkWell(
                                                    onTap: () =>
                                                        NavigationServiceImpl
                                                                .getInstance()!
                                                            .doNavigation(
                                                      context,
                                                      routeName: RouteName
                                                          .editProfileDetails,
                                                    ),
                                                    child: TextTitle18And14(
                                                      AppStrings.edit,
                                                      color: colorScheme
                                                          .hyperlinkBlueColor,
                                                    ),
                                                  ),
                                                ]
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                              state.userDataModel.bio ?? "",
                                            ),
                                            4.ph,
                                            const TextTitle14(AppStrings.bio_),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                              state.userDataModel.gender
                                                      ?.name ??
                                                  '',
                                            ),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.gender_),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(
                                              "${state.userDataModel.streetAddress}, ${state.userDataModel.city}, ${state.userDataModel.state}, ${state.userDataModel.postalCode}, ${state.userDataModel.country}",
                                            ),
                                            4.ph,
                                            const TextTitle14(
                                                AppStrings.address),
                                            if (state.userDataModel.tags !=
                                                    null &&
                                                state.userDataModel.tags!
                                                    .isNotEmpty) ...[
                                              !Responsive.isDesktop(context)
                                                  ? 24.ph
                                                  : 28.ph,
                                              CommonCardWidget<Object>(
                                                items:
                                                    state.userDataModel.tags ??
                                                        [],
                                                itemBuilder: (context, item) {
                                                  return TextTitle14(
                                                    item.toString(),
                                                    color: colorScheme
                                                        .hyperlinkBlueColor,
                                                  );
                                                },
                                              ),
                                              4.ph,
                                              const TextTitle14(
                                                  AppStrings.keywords),
                                            ]
                                          ],
                                        ),
                                      ),
                                    ),
                                    !Responsive.isDesktop(context)
                                        ? 20.ph
                                        : 28.ph,
                                    CommonShadowContainer(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text24And20SemiBold(
                                                  AppStrings
                                                      .vocalCharacterstics,
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                    color: colorScheme.black,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                const Spacer(),
                                                if (state.userDataModel.id ==
                                                    userDataModel?.id) ...[
                                                  InkWell(
                                                    onTap: () =>
                                                        NavigationServiceImpl
                                                                .getInstance()!
                                                            .doNavigation(
                                                      context,
                                                      routeName: RouteName
                                                          .editVocalCharacteristics,
                                                    ),
                                                    child: TextTitle18And14(
                                                      AppStrings.edit,
                                                      color: colorScheme
                                                          .hyperlinkBlueColor,
                                                    ),
                                                  ),
                                                ]
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceGenderList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings.voiceGender
                                                .substring(
                                                    0,
                                                    AppStrings.voiceGender
                                                            .length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceTypeList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings.voiceType
                                                .substring(
                                                    0,
                                                    AppStrings
                                                            .voiceType.length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceCharList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings
                                                .voiceCharacter
                                                .substring(
                                                    0,
                                                    AppStrings.voiceCharacter
                                                            .length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(state.userDataModel
                                                    .experience?.name ??
                                                'No experience'),
                                            4.ph,
                                            TextTitle14(AppStrings.experience
                                                .substring(
                                                    0,
                                                    AppStrings
                                                            .experience.length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceAgeList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings.voiceAge
                                                .substring(
                                                    0,
                                                    AppStrings.voiceAge.length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceLangList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings.language
                                                .substring(
                                                    0,
                                                    AppStrings.language.length -
                                                        1)),
                                            !Responsive.isDesktop(context)
                                                ? 24.ph
                                                : 28.ph,
                                            TextTitle18And20(voiceAccentList
                                                .map((e) => e.name)
                                                .join(", ")),
                                            4.ph,
                                            TextTitle14(AppStrings.accent
                                                .substring(
                                                    0,
                                                    AppStrings.accent.length -
                                                        1)),
                                          ],
                                        ),
                                      ),
                                    ),
                                    !Responsive.isDesktop(context)
                                        ? 20.ph
                                        : 28.ph,
                                    CommonShadowContainer(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text24And20SemiBold(
                                                  AppStrings.projectPreference,
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                    color: colorScheme.black,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                const Spacer(),
                                                if (state.userDataModel.id ==
                                                    userDataModel?.id) ...[
                                                  InkWell(
                                                    onTap: () => NavigationServiceImpl
                                                            .getInstance()!
                                                        .doNavigation(context,
                                                            routeName: RouteName
                                                                .editPreferredProjectType),
                                                    child: TextTitle18And14(
                                                      AppStrings.edit,
                                                      color: colorScheme
                                                          .hyperlinkBlueColor,
                                                    ),
                                                  ),
                                                ]
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            CommonCardWidget<NameIdModel>(
                                              items: state.userDataModel
                                                      .projectType ??
                                                  [],
                                              itemBuilder: (context, item) {
                                                return TextTitle14(
                                                  item.name.toString(),
                                                  color: colorScheme
                                                      .hyperlinkBlueColor,
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    !Responsive.isDesktop(context)
                                        ? 20.ph
                                        : 28.ph,
                                    CommonShadowContainer(
                                      child: Padding(
                                        padding: EdgeInsets.all(
                                            !Responsive.isDesktop(context)
                                                ? 12.h
                                                : 28.h),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text24And20SemiBold(
                                                  AppStrings.vocalSamples,
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                    color: colorScheme.black,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                const Spacer(),
                                                if (state.userDataModel.id ==
                                                    userDataModel?.id) ...[
                                                  InkWell(
                                                    onTap: () {
                                                      NavigationServiceImpl
                                                              .getInstance()!
                                                          .doNavigation(
                                                        context,
                                                        routeName:
                                                            RouteName.editAudio,
                                                      );
                                                      _disposeAudioPlayers();
                                                    },
                                                    child: TextTitle18And14(
                                                      AppStrings.edit,
                                                      color: colorScheme
                                                          .hyperlinkBlueColor,
                                                    ),
                                                  ),
                                                ]
                                              ],
                                            ),
                                            !Responsive.isDesktop(context)
                                                ? 12.ph
                                                : 28.ph,
                                            _audioSamplesWidget(
                                                state, colorScheme),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                  //   if (state.userDataModel.reviews?.totalReviewers !=0) ...[
                                  !Responsive.isDesktop(context)
                                      ? 20.ph
                                      : 28.ph,
                                  CommonShadowContainer(
                                    child: Padding(
                                      padding: EdgeInsets.all(
                                          !Responsive.isDesktop(context)
                                              ? 12.h
                                              : 28.h),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text24And20SemiBold(
                                                  AppStrings.reviews
                                                      .toUpperCase(),
                                                  style: theme
                                                      .textTheme.titleLarge!
                                                      .copyWith(
                                                          color:
                                                              colorScheme.black,
                                                          fontWeight:
                                                              FontWeight.w700)),
                                              const Spacer(),
                                              InkWell(
                                                onTap: () {
                                                  NavigationServiceImpl
                                                          .getInstance()!
                                                      .doNavigation(context,
                                                          routeName:
                                                              RouteName.reviews,
                                                          pathParameters: {
                                                        Params.id:
                                                            widget.id.toString()
                                                      });
                                                },
                                                child: TextTitle18And14(
                                                    AppStrings.viewAll,
                                                    color: colorScheme
                                                        .hyperlinkBlueColor),
                                              ),
                                            ],
                                          ),
                                          !Responsive.isDesktop(context)
                                              ? 12.ph
                                              : 28.ph,
                                          (state.userDataModel.reviews
                                                          ?.totalReviewers ==
                                                      0 ||
                                                  state.userDataModel.reviews
                                                          ?.totalReviewers ==
                                                      null)
                                              ? Center(
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text24And20SemiBold(
                                                        AppStrings.noReviewsYet,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                      20.ph,
                                                    ],
                                                  ),
                                                )
                                              : _buildRatingWidget(
                                                  colorScheme, state),
                                        ],
                                      ),
                                    ),
                                  ),
                                ]
                                // ]
                                ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }
          return const SizedBox();
        }),
      ),
    );
  }

  Widget _audioSamplesWidget(UserSuccessState state, ColorScheme colorScheme) {
    if (state.userDataModel.voiceAudioUrls == null ||
        state.userDataModel.voiceAudioUrls!.isEmpty) {
      return const Center(child: Text(AppStrings.noAudioSamplesAvailable));
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: state.userDataModel.voiceAudioUrls!.length,
      itemBuilder: (context, index) {
        final isPlaying = state.playerStates[index] == PlayerState.playing;
        final duration = state.durations[index];
        final position = state.positions[index];
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 9.h),
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: colorScheme.lightGreyD9D9D9),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        icon: SvgPicture.asset(
                          isPlaying ? AppImages.pauseIcon : AppImages.play,
                        ),
                        onPressed: () {
                          context.read<UserBloc>().playAudio(index);
                        },
                      ),
                      Expanded(
                        child: Row(
                          children: [
                            Expanded(
                              child: SliderTheme(
                                data: SliderThemeData(
                                  thumbShape: RoundSliderThumbShape(
                                    enabledThumbRadius: 6.r,
                                  ),
                                ),
                                child: Slider(
                                  thumbColor: colorScheme.white,
                                  activeColor: colorScheme.primary,
                                  inactiveColor: colorScheme.lightGreyD9D9D9,
                                  value: position.inSeconds.toDouble(),
                                  max: duration.inSeconds.toDouble(),
                                  onChanged: (value) async {
                                    final newPosition =
                                        Duration(seconds: value.toInt());
                                    await state.audioPlayers[index]
                                        .seek(newPosition);
                                  },
                                ),
                              ),
                            ),
                            TextTitle14(
                              isPlaying
                                  ? "${(duration - position).inMinutes}:${((duration - position).inSeconds % 60).toString().padLeft(2, '0')}"
                                  : "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRatingWidget(ColorScheme colorScheme, UserSuccessState state) {
    return !Responsive.isDesktop(context)
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  TextDisplayLarge36And26("${state.userDataModel.reviews?.averageRating ?? 0}/",
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(color: colorScheme.textfieldTitleColor),
                  ),
                  TextDisplayLarge36And26( "5",style: Theme.of(context).textTheme.displayLarge!.copyWith(color: colorScheme.hintTextColor)),
                ],
              ),
              12.ph,
              TextTitle14("${state.userDataModel.reviews?.totalReviewers ?? 0} ${(state.userDataModel.reviews?.totalReviewers ?? 0) == 1 ? 'user' : 'users'} rated",
                color: colorScheme.hyperlinkBlueColor),
              16.ph,
             _buildRatingBar(5, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 5), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                    12.ph,
                    _buildRatingBar(4, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 4), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                    12.ph,
                    _buildRatingBar(3, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 3),state.userDataModel.reviews?.totalReviewers ?? 0, colorScheme),
                    12.ph,
                    _buildRatingBar(2, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 2), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                    12.ph,
              _buildRatingBar(1, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 1), state.userDataModel.reviews?.totalReviewers ?? 0, colorScheme),
            ],
          )
        : Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      TextDisplayLarge36And26("${state.userDataModel.reviews?.averageRating ?? 0}/",
                        style:Theme.of(context).textTheme.displayLarge!.copyWith(color: colorScheme.textfieldTitleColor)),

                      TextDisplayLarge36And26("5",style:Theme.of(context).textTheme.displayLarge!.copyWith(color: colorScheme.hintTextColor)),
                    ],
                  ),
                  12.ph,
                  TextTitle14(
                    "${state.userDataModel.reviews?.totalReviewers ?? 0} ${(state.userDataModel.reviews?.totalReviewers ?? 0) == 1 ? 'user' : 'users'} rated",
                    color: colorScheme.hyperlinkBlueColor),
                ],
              ),
              SizedBox(width: MediaQuery.of(context).size.width * .17),
              Expanded(
                child: Column(
                  children: [
                    _buildRatingBar(5, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 5), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                    12.ph,
                    _buildRatingBar(4, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 4), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                    12.ph,
                    _buildRatingBar(3, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 3),state.userDataModel.reviews?.totalReviewers ?? 0, colorScheme),
                    12.ph,
                    _buildRatingBar(2, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 2),state.userDataModel.reviews?.totalReviewers ?? 0, colorScheme),
                    12.ph,
                    _buildRatingBar(1, _getRatingCount(state.userDataModel.reviews?.ratingCounts, 1), state.userDataModel.reviews?.totalReviewers ?? 0,colorScheme),
                  ],
                ),
              ),
            ],
          );
  }

  int _getRatingCount(List<RatingCount>? ratingCounts, double rating) {
    if (ratingCounts == null) return 0;
    return ratingCounts.firstWhere((element) => element.rating == rating, orElse: () => RatingCount(rating: rating, count: 0)).count ?? 0;
  }

  Widget _buildRatingBar(int rating, int count, int totalCount, ColorScheme colorScheme) {
    Color ratingColor;
    switch (rating) {
      case 5:
        ratingColor = colorScheme.green13B25D;
        break;
      case 4:
        ratingColor = colorScheme.green13B25D;
        break;
      case 3:
        ratingColor = colorScheme.yellowFFC500;
        break;
      case 2:
        ratingColor = colorScheme.redFF6257;
        break;
      case 1:
        ratingColor = colorScheme.redFF6257;
        break;
      default:
        ratingColor = colorScheme.primary;
    }

    return Row(
      children: [
        TextBodySmall12(rating.toString(),color: colorScheme.hintTextColor),
        3.pw,
        SvgPicture.asset(AppImages.starFilled,colorFilter: ColorFilter.mode(colorScheme.hintTextColor, BlendMode.srcIn), height: 10.h, width: 10.w),
        4.pw,
        Expanded(
          child: Stack(
            children: [
              Container(
                height: 7.h,
                decoration: BoxDecoration(
                  color: colorScheme.hintTextColor,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              FractionallySizedBox(
                widthFactor: count / totalCount,
                child: Container(
                  height: 7.h,
                  decoration: BoxDecoration(
                    color: ratingColor,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ],
          ),
        ),
        11.pw,
        TextTitle14(count.toString(), textAlign: TextAlign.end, color: colorScheme.textfieldTitleColor,
        ),
      ],
    );
  }

  void showReportDialog(BuildContext context) {
    Dialogs.showCommonDialogWithComment(
      context: context,
      title: AppStrings.reportUser,
      message: AppStrings.areYouSureToReport,
      primaryButtonText: AppStrings.report,
      commentHint: AppStrings.pleaseEnterReasonForReporting,
      onPrimaryButtonTap: () {
        context
            .read<ReportBloc>()
            .report(widget.id ?? 0, commentController.text.trim());
        context.pop();
        commentController.clear();
      },
      commentController: commentController,
    );
  }
}
