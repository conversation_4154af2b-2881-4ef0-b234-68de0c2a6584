import 'package:flutter/material.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class ErrorScreen extends StatelessWidget {
  final VoidCallback onRetry;
  final String? errorMessage;
  final Widget? imageWidget;
  final String? buttonText;

  const ErrorScreen({
    super.key,
    required this.onRetry,
    this.errorMessage,
    this.imageWidget,
    this.buttonText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (imageWidget != null) imageWidget!,
          20.ph,
          TextTitle18And20(errorMessage ?? AppStrings.somethingWentWrongPleaseTryAgain),
          20.ph,
          PrimaryButton(
            width: !Responsive.isDesktop(context)
                ? MediaQuery.of(context).size.width * .63
                : MediaQuery.of(context).size.width * .17,
            onPressed: onRetry,
            buttonText: buttonText ?? AppStrings.retry,
          ),
        ],
      ),
    );
  }
}
