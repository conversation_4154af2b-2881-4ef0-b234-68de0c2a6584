import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class ProfileBloc extends Cubit<ProfileState> {
  @override
  ProfileBloc() : super(ProfileInitState());

  void updateProfile({required UserInfoRequestModel userInfoRequestModel}) async {
    emit(ProfileLoadingState());
    try {
      final response = await ApiService.instance.submitUserInfoData(userInfoRequestModel: userInfoRequestModel);
      if (response.success) {
        emit(ProfileSuccessState());
      } else {
        emit(ProfileErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ProfileErrorState(AppStrings.genericErrorMsg));
    }
  }
}
