
import 'package:audioplayers/audioplayers.dart';
import 'package:chewie/chewie.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/bloc/apply_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/bloc/apply_job_state.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/data/apply_job_rq_model.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_state.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_audio_video_cubit.dart';
import 'package:the_voice_directory_flutter/utils/common_models/media_info_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:video_player/video_player.dart';

import '../../../../utils/extensions/string_extn.dart';
import '../../../job_details/bloc/job_detail_bloc.dart';
import '../../../job_details/bloc/job_detail_state.dart';
import '../../../jobs/bloc/jobs_cubit.dart';
import '../../../post_job/data/enums/sample_script_type.dart';

class VoiceJobApplyScreen extends StatefulWidget {
  final int jobId;

  const VoiceJobApplyScreen({super.key, required this.jobId});

  @override
  State<VoiceJobApplyScreen> createState() => _VoiceJobApplyScreenState();
}

class _VoiceJobApplyScreenState extends State<VoiceJobApplyScreen> {
  late TextEditingController _proposal;
  late TextEditingController _revisionPolicy;
  final TextEditingController _projectAmount = TextEditingController();
  final TextEditingController _calculatedAmount = TextEditingController();
  bool autovalidation = false;
  final formGlobalKey = GlobalKey<FormState>();
  late UploadAudioVideoCubit _uploadAudioVideoCubit;

  @override
  void initState() {
    _proposal = TextEditingController();
    _revisionPolicy = TextEditingController();
    _uploadAudioVideoCubit = UploadAudioVideoCubit();
    super.initState();
    context.read<JobDetailBloc>().getJobDetail(widget.jobId);
  }

  @override
  void dispose() {
    _projectAmount.removeListener(_updateCalculatedAmount);
    _proposal.dispose();
    _revisionPolicy.dispose();
    _projectAmount.dispose();
    _calculatedAmount.dispose();
    _uploadAudioVideoCubit.close();
    super.dispose();
  }

  void _updateCalculatedAmount() {
    String value = _projectAmount.text;
    if (value.isNotEmpty) {
      double amount = double.tryParse(value) ?? 0;
      double calculatedAmount = amount * 1.2; // 20% extra
      _calculatedAmount.text = calculatedAmount.toStringAsFixed(2);
    } else {
      _calculatedAmount.text = "";
    }
  }

  Widget _buildAudioPlayer(UploadAudioVideoState state) {
    if (state.audioFiles.isEmpty) return const SizedBox();

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: state.audioFiles.length,
      itemBuilder: (context, index) {
        final isPlaying = state.playerStates[index] == PlayerState.playing;
        final duration = state.durations[index];
        final position = state.positions[index];

        return Container(
          margin: EdgeInsets.only(top: 16.h),
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.lightGreyD9D9D9,
            ),
          ),
          child: Row(
            children: [
              IconButton(
                icon: SvgPicture.asset(
                  isPlaying ? AppImages.pauseIcon : AppImages.play,
                ),
                onPressed: () =>
                    context.read<UploadAudioVideoCubit>().playAudio(index),
              ),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: SliderTheme(
                        data: SliderThemeData(
                          thumbShape: RoundSliderThumbShape(
                            enabledThumbRadius: 6.r,
                          ),
                        ),
                        child: Slider(
                          thumbColor: Theme.of(context).colorScheme.white,
                          activeColor: Theme.of(context).colorScheme.primary,
                          inactiveColor:
                              Theme.of(context).colorScheme.lightGreyD9D9D9,
                          min: 0,
                          max: duration.inSeconds.toDouble(),
                          value: position.inSeconds.toDouble(),
                          onChanged: (value) {
                            context
                                .read<UploadAudioVideoCubit>()
                                .seekTo(index, value.toInt());
                          },
                        ),
                      ),
                    ),
                    16.pw,
                    Text(
                      '${position.inMinutes}:${(position.inSeconds % 60).toString().padLeft(2, '0')} / '
                      '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  context.read<UploadAudioVideoCubit>().deleteAudio(index);
                },
                icon: SvgPicture.asset(AppImages.trash),
              ),
            ],
          ),
        );
      },
    );
  }

  // Widget _buildVideoPlayer(UploadAudioVideoState state) {
  //   if (state.videoFiles.isEmpty) return const SizedBox.shrink();

  //   // This is because we are allowing single video file
  //   const videoIndex = 0;
  //   final chewieController = state.chewieControllers[videoIndex];

  //   return Container(
  //     margin: EdgeInsets.only(top: 16.h),
  //     decoration: BoxDecoration(
  //       color: Theme.of(context).colorScheme.white,
  //       borderRadius: BorderRadius.circular(12.r),
  //       border: Border.all(
  //         color: Theme.of(context).colorScheme.lightGreyD9D9D9,
  //       ),
  //     ),
  //     child: ClipRRect(
  //       borderRadius: BorderRadius.circular(12.r),
  //       child: AspectRatio(
  //         aspectRatio: chewieController.videoPlayerController.value.aspectRatio,
  //         child: Chewie(controller: chewieController),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildVideoPlayer(UploadAudioVideoState state) {
    if (state.videoFiles.isEmpty) return const SizedBox.shrink();
    const videoIndex = 0;
    final videoFile = state.videoFiles[videoIndex];
    return GestureDetector(
      onTap: () {
        _showVideoPlayerDialog(context, state.chewieControllers[videoIndex]);
      },
      child: Container(
        margin: EdgeInsets.only(top: 16.h),
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Theme.of(context).colorScheme.lightGreyD9D9D9,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60.w,
              height: 60.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: SvgPicture.asset(AppImages.play,
                  width: 24.w,
                  height: 24.h,
                ),
              ),
            ),
            16.pw,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    videoFile.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                final cubit = _uploadAudioVideoCubit;
                if (cubit.state.chewieControllers.isNotEmpty) {
                  cubit.state.chewieControllers[0].pause();
                }
                cubit.state.copyWith(
                  videoFiles: <MediaInfoModel>[],
                  chewieControllers: <ChewieController>[],
                  videoControllers: <VideoPlayerController>[],
                );
              },
              icon: SvgPicture.asset(AppImages.trash),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoPlayerDialog(BuildContext context, ChewieController chewieController) {
    final tempController = ChewieController(
      videoPlayerController: chewieController.videoPlayerController,
      autoPlay: true,
      looping: false,
      showControls: true,
      allowFullScreen: false,
      showOptions: false,
      materialProgressColors: ChewieProgressColors(
        playedColor: Theme.of(context).colorScheme.primary,
        bufferedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
      ),
      customControls: const MaterialControls(),
    );
    
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "Video",
      pageBuilder: (context, animation1, animation2) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.black,
            body: Stack(
              fit: StackFit.expand,
              children: [
                Center(
                  child: AspectRatio(
                    aspectRatio: chewieController.videoPlayerController.value.aspectRatio,
                    child: Chewie(controller: tempController),
                  ),
                ),
                Positioned(
                  top: 16.h,
                  right: 16.w,
                  child: GestureDetector(
                    onTap: () {
                      tempController.pause();
                      tempController.dispose();
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: Icon( Icons.close, color: Colors.white, size: 24.r,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      transitionBuilder: (context, animation1, animation2, child) {
        return FadeTransition(
          opacity: animation1,
          child: child,
        );
      },
    ).then((_) {
      tempController.dispose();
    });
  }

  String? _audioVideoValidator(UploadAudioVideoState state) {
    if (state.audioFiles.isEmpty && state.videoFiles.isEmpty) {
      return ValidationMsg.plsEnter('sample script');
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocBuilder<ApplyJobBloc, ApplyJobState>(
      builder: (context, state) {
        return BlocProvider.value(
          value: _uploadAudioVideoCubit,
          child: BlocConsumer<UploadAudioVideoCubit, UploadAudioVideoState>(
            listener: (context, state) {
              if (state.errorMessage != null &&
                  state.errorMessage!.isNotEmpty) {
                CustomToast.show(
                    context: context, message: state.errorMessage!);
                _uploadAudioVideoCubit.resetError();
              }
            },
            builder: (context, state) {
              return Scaffold(
                body: Column(
                  children: [
                    !Responsive.isDesktop(context) ? 40.ph : 0.ph,
                    Expanded(
                      child: SingleChildScrollView(
                        child: Form(
                          key: formGlobalKey,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal:
                                    !Responsive.isDesktop(context)
                                    ? 0.h
                                    : 80.h,
                                vertical: !Responsive.isDesktop(context)
                                    ? 0.h
                                    : 40.h),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: !Responsive.isDesktop(context)
                                      ? 16.h
                                      : 0.h,
                                  vertical: !Responsive.isDesktop(context)
                                      ? 12.h
                                      : 0.h),
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  if (!!Responsive.isDesktop(context)) ...[
                               CustomBackButtonArrow(),
                                24.ph,
                                  ],
                                  Row(
                                    children: [
                                      if (!Responsive.isDesktop(context))
                                        const Align(
                                          alignment: Alignment.centerLeft,
                                          child: CustomBackButton(),
                                        ),
                                      if (Responsive.isDesktop(
                                          context)) ...[
                                        const TextDisplayLarge36And26(
                                            AppStrings.applyToJob),
                                      ] else
                                        const Expanded(
                                          child: Center(
                                            child: TextDisplayLarge36And26(
                                                AppStrings.applyToJob),
                                          ),
                                        ),
                                        SizedBox(width: 32.w)
                                    ],
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 40.ph,
                                  Text24And20SemiBold(
                                    AppStrings.yourProposal,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.w800),
                                  ),
                                  16.ph,
                                  AppTextFormField(
                                    controller: _proposal,
                                    hintText: AppStrings.introduceYourSelf,
                                    maxLines: 5,
                                    keyboardType: TextInputType.multiline,
                                    textInputAction: TextInputAction.newline,
                                    validator: (value) {
                                      return Validator.emptyValidator(
                                          value,
                                          ValidationMsg.plsEnter(
                                              'your proposal'));
                                    },
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 40.ph,
                                  Text24And20SemiBold(
                                    AppStrings.yourRevisionPolicy,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.w800),
                                  ),
                                  16.ph,
                                  AppTextFormField(
                                    controller: _revisionPolicy,
                                    hintText: AppStrings.explainWhatYou,
                                    maxLines: 5,
                                    keyboardType: TextInputType.multiline,
                                    textInputAction: TextInputAction.newline,
                                  ),
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 40.ph,
                                  Text24And20SemiBold(
                                    AppStrings.yourQuoteForPorject,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.w800),
                                  ),
                                  16.ph,
                                  AppTextFormField(
                                    prefixIcon: Padding(
                                      padding: const EdgeInsets.all(13),
                                      child: SvgPicture.asset(
                                        AppImages.rsIcon,
                                      ),
                                    ),
                                    controller: _projectAmount,
                                    hintText: AppStrings.enterAmount,
                                    maxLines: 1,
                                    inputFormatters: [
                                      LengthLimitingTextInputFormatter(10),
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    validator: (value) {
                                      return Validator.emptyValidator(
                                          value,
                                          ValidationMsg.plsEntervalid(
                                              "project amount"));
                                    },
                                    onChanged: (value) {
                                      if (value.isNotEmpty) {
                                        double amount =
                                            double.tryParse(value) ?? 0;
                                        double calculatedAmount =
                                            amount * 1.2; // 20% extra
                                        _calculatedAmount.text =
                                            calculatedAmount
                                                .toStringAsFixed(2);
                                      } else {
                                        _calculatedAmount.text = "";
                                      }
                                    },
                                  ),
                                  // 12.ph,
                                  // TextTitle14(
                                  //     AppStrings.yourQuoteWillInclude,
                                  //     color:
                                  //         colorScheme.hyperlinkBlueColor),
                                  // 24.ph,
                                  //  Text24And20SemiBold(
                                  //   AppStrings.finalQuoteForClient,
                                  //   style: Theme.of(context)
                                  //       .textTheme
                                  //       .bodyMedium!
                                  //       .copyWith(
                                  //           fontWeight: FontWeight.w800),
                                  // ),
                                  // 16.ph,
                                  // AppTextFormField(
                                  //   prefixIcon: Padding(
                                  //     padding: const EdgeInsets.all(13.0),
                                  //     child: SvgPicture.asset(
                                  //       AppImages.rsIcon,
                                  //     ),
                                  //   ),
                                  //   filled: true,
                                  //   fillColor: colorScheme.lightGreyD9D9D9,
                                  //   isenabled: false,
                                  //   controller: _calculatedAmount,
                                  //   hintText: AppStrings.toBeCalculated,
                                  //   hintTextFontWeight: FontWeight.w500,
                                  //   readOnly: true,
                                  //   maxLines: 1,
                                  // ),
                                  // 12.ph,
                                  // TextTitle14(
                                  //   AppStrings.theClientWillSeeThisAmount,
                                  //   color: colorScheme.hyperlinkBlueColor,
                                  // ),
                                  //24.ph,
                                  !Responsive.isDesktop(context)
                                      ? 24.ph
                                      : 40.ph,
                                    BlocBuilder<JobDetailBloc, JobDetailState>(
                                      builder: (context, jobDetailState) {
                                        final isOptional = jobDetailState is JobSuccessState && jobDetailState.jobDetailModel.sampleScriptType == SampleScriptType.noScriptSample;
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text24And20SemiBold(
                                              isOptional ? AppStrings.uploadScriptSample.removeLastChar() : AppStrings.uploadScriptSample,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                    fontWeight: FontWeight.w800,
                                                  ),
                                            ),
                                            12.ph,
                                            TextTitle14(
                                              AppStrings.hereYouCanUpload,
                                              textAlign: TextAlign.start,
                                            ),
                                            4.ph, // Add small spacing
                                            TextTitle14(
                                              AppStrings.audioVideo10MbLimit,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall,
                                            ),
                                            16.ph,
                                            DottedBorder(
                                              borderType: BorderType.RRect,
                                              radius: Radius.circular(12.r),
                                              padding: EdgeInsets.symmetric(
                                                vertical:
                                                !Responsive.isDesktop(context)
                                                        ? 28.h
                                                        : 38.h,
                                              ),
                                              color:
                                                  colorScheme.hyperlinkBlueColor,
                                              strokeWidth: 1.5,
                                              dashPattern: const [10, 10],
                                              child: Container(
                                                width: double.infinity,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8.0.r),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    SvgPicture.asset(
                                                        AppImages.export),
                                                    12.ph,
                                                    PrimaryButton(
                                                      width: 164.w,
                                                      height: 44.h,
                                                      backgroundColor: colorScheme
                                                          .lightGreenFDFFDA,
                                                      onPressed: () async {
                                                        if (state.videoFiles.isEmpty && state.audioFiles.isEmpty) {
                                                          context.read<UploadAudioVideoCubit>().browseFiles(context, maxFiles: 1, isAudioVideoBoth: true);
                                                        } else {
                                                          CustomToast.show(
                                                            context: context,
                                                            message: AppStrings
                                                                .youCanUploadMaximum1Files,
                                                          );
                                                        }
                                                      },
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                40.r),
                                                        side: BorderSide(
                                                          color:
                                                              colorScheme.primary,
                                                          width: 1.2.h,
                                                        ),
                                                      ),
                                                      child: TextTitle14(
                                                        AppStrings.browseFile,
                                                        style: theme.textTheme
                                                            .titleMedium!
                                                            .copyWith(
                                                          fontSize: 16.sp,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            if (autovalidation && _audioVideoValidator(state) != null)
                                              Padding(
                                                padding: EdgeInsets.only(top: 8.h, left: 16.w),
                                                child: TextBodySmall12(
                                                  _audioVideoValidator(state)!,
                                                  style: TextStyle(
                                                    color: colorScheme.error,
                                                    fontSize: 9,
                                                  ),
                                                ),
                                              ),
                                            _buildAudioPlayer(state),
                                            _buildVideoPlayer(state),
                                          ],
                                        );
                                    }),
                                  if (!!Responsive.isDesktop(context)) ...[
                                    40.ph,
                                    Divider(
                                        color: colorScheme.lightGreyD9D9D9,
                                        thickness: 1),
                                  ],
                                  Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 20.h),
                                      child: Align(
                                        alignment: Alignment.center,
                                        child: MultiBlocListener(
                                          listeners: [
                                            BlocListener<ApplyJobBloc, ApplyJobState>(
                                              listener: (context, state) {
                                                if (state is ApplyJobLoadingState) {
                                                  Dialogs.showOnlyLoader(context);
                                                }
                                                if (state is ApplyJobSuccessState) {
                                                  context.pop();
                                                  CustomToast.show(
                                                    context: context, 
                                                    message: AppStrings.jobAppliedSuccessfully,
                                                    isSuccess: true,
                                                  );
                                                  context.pop();
                                                  context.read<JobsCubit>().fetchJobs();
                                                }
                                                if (state is ApplyJobErrorState) {
                                                  context.pop();
                                                  CustomToast.show(
                                                    context: context,
                                                    message: state.errorMsg,
                                                  );
                                                }
                                              },
                                            ),
                                            BlocListener<UploadMediaBloc, UploadMediaState>(
                                              listener: (context, uploadState) {
                                                if (uploadState is UploadMediaLoadingState) {
                                                  Dialogs.showOnlyLoader(context);
                                                }
                                                if (uploadState is UploadMediaSuccessState) {
                                                  context.pop();
                                                  context.read<ApplyJobBloc>().applyJob(
                                                    applyJobRequestModel: ApplyJobRequestModel(
                                                      job: widget.jobId,
                                                      proposal: _proposal.text.trim(),
                                                      revisionPolicy: _revisionPolicy.text.trim(),
                                                      attachment: uploadState.getPreSignedUrlModel?.presignedUrls?.jobAttachments?.first.path,
                                                      quote: int.tryParse(_projectAmount.text.trim())
                                                    ),
                                                  );
                                                }
                                                if (uploadState is UploadMediaErrorState) {
                                                  context.pop();
                                                  CustomToast.show(
                                                    context: context,
                                                    message: uploadState.errorMsg,
                                                  );
                                                }
                                              },
                                            ),
                                          ],
                                          child: Align(
                                              alignment:
                                                  !Responsive.isDesktop(context) 
                                                ? Alignment.center 
                                                : Alignment.centerRight,
                                            child: PrimaryButton(
                                              buttonText: AppStrings.submit,
                                                width: !Responsive.isDesktop(
                                                        context)
                                                    ? null
                                                    : MediaQuery.of(context)
                                                            .size
                                                            .width *
                                                        .19,
                                              onPressed: () {
                                                final jobDetailState = context.read<JobDetailBloc>().state;
                                                if (formGlobalKey.currentState!.validate()) {
                                                  final isOptional = jobDetailState is JobSuccessState && jobDetailState.jobDetailModel.sampleScriptType == SampleScriptType.noScriptSample;
                              
                                                  if (isOptional && (state.audioFiles.isEmpty && state.videoFiles.isEmpty)) {
                                                    context.read<ApplyJobBloc>().applyJob(
                                                      applyJobRequestModel: ApplyJobRequestModel(
                                                        job: widget.jobId,
                                                        proposal: _proposal.text.trim(),
                                                        revisionPolicy: _revisionPolicy.text.trim(),
                                                        quote: int.tryParse(_projectAmount.text.trim())
                                                      ),
                                                    );
                                                    return;
                                                  } else if (_audioVideoValidator(state) == null) {
                                                    final uploadAudioCubit = context.read< UploadAudioVideoCubit>();
                                                    for (int i = 0; i < uploadAudioCubit.state.audioPlayers.length; i++) {
                                                      uploadAudioCubit.pauseAudio(i);
                                                    }
                                                    for (int i = 0; i < uploadAudioCubit.state.videoControllers.length; i++) {
                                                      uploadAudioCubit.pauseVideo(i);
                                                    }
                                                    if (state.audioFiles.isNotEmpty) {
                                                      context.read<UploadMediaBloc>().getPreSignedUrl(
                                                        contentType: "audio/mp3",
                                                        preSignedUrlReqModel: PreSignedUrlReqModel(
                                                          jobAttachments: [
                                                            MediaDetails(
                                                              extn: state.audioFiles[0].ext,
                                                              fileName: state.audioFiles[0].path?.split('/').last,
                                                              fileType: "audio",
                                                            ),
                                                          ],
                                                        ),
                                                        jobAttachments: [state.audioFiles[0].bytes],
                                                      );
                                                    } else if (state.videoFiles.isNotEmpty) {
                                                      context.read<UploadMediaBloc>().getPreSignedUrl(
                                                        contentType: "video/mp4",
                                                        preSignedUrlReqModel: PreSignedUrlReqModel(
                                                          jobAttachments: [
                                                            MediaDetails(
                                                              extn: state.videoFiles[0].ext,
                                                              fileName: state.videoFiles[0].path?.split('/').last,
                                                              fileType: "video",
                                                            ),
                                                          ],
                                                        ),
                                                        jobAttachments: [state.videoFiles[0].bytes],
                                                      );
                                                    }
                                                  } else {
                                                    setState(() {
                                                      autovalidation = true;
                                                    });
                                                  }
                                                } else {
                                                  setState(() {
                                                    autovalidation = true;
                                                  });
                                                }
                                              },
                                            ),
                                          ),
                                      ))
                              )],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
