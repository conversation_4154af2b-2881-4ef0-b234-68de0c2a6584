import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:just_audio/just_audio.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/api/api_params.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/audio_player_cubit.dart';
import '../data/model/player_state.dart';

class AudioCard extends StatefulWidget {
  final UserDataModel? user;
  final double? width;

  const AudioCard({
    super.key,
    required this.user,
    this.width,
  });

  @override
  State<AudioCard> createState() => _AudioCardState();
}

class _AudioCardState extends State<AudioCard> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  String? audioUrl;
  
  final ValueNotifier<PlayerStateModel> playerStateNotifier = ValueNotifier(
    const PlayerStateModel(
      url: '',
      isPlaying: false,
      isLoading: true,
      position: Duration.zero,
      duration: Duration.zero,
    ),
  );

  @override
  void initState() {
    super.initState();

    final cubit = context.read<AudioPlayerCubit>();
    cubit.addPlayer(_audioPlayer);

    audioUrl = widget.user?.voiceAudioUrls?.isNotEmpty ?? false
        ? Uri.parse(widget.user!.voiceAudioUrls!.first.audioUrl ?? '').isAbsolute
            ? widget.user!.voiceAudioUrls!.first.audioUrl ?? ''
            : '${EnvironmentConfig.imageBaseUrl}${widget.user!.voiceAudioUrls!.first.audioUrl ?? ''}'
        : null;

    setUpAudioPlayer(audioUrl);
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> setUpAudioPlayer(String? audioUrl) async {
    try {
      if (audioUrl == null || audioUrl.isEmpty) throw Exception('Audio URL is empty');

      final duration = await _audioPlayer.setUrl(audioUrl);

      playerStateNotifier.value = playerStateNotifier.value.copyWith(
        url: audioUrl,
        duration: duration ?? Duration.zero,
        isLoading: false,
      );

      _audioPlayer.playerStateStream.listen((state) {
        if (!mounted) return;
        playerStateNotifier.value = playerStateNotifier.value.copyWith(
          isPlaying: state.playing,
        );
      });

      _audioPlayer.positionStream.listen((position) {
        if (!mounted) return;
        playerStateNotifier.value = playerStateNotifier.value.copyWith(
          position: position,
        );
      });
    } catch (e) {
      debugPrint("Error setting up audio player: ${e.toString()}");
      if (!mounted) return;
      playerStateNotifier.value = playerStateNotifier.value.copyWith(
        isLoading: false,
        url: '',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<String> items = [];
    if (widget.user?.voiceGender?.isNotEmpty ?? false) {
      items.add(widget.user!.voiceGender!.first.name ?? '');
    }
    if (widget.user?.ageRange?.isNotEmpty ?? false) {
      items.add(widget.user!.ageRange!.first.name ?? '');
    }
    if (widget.user?.voiceLanguage?.isNotEmpty ?? false) {
      items.add(widget.user!.voiceLanguage!.first.name ?? '');
    }
    if (widget.user?.voiceCharacter?.isNotEmpty ?? false) {
      items.add(widget.user!.voiceCharacter!.first.name ?? '');
    }
    if (widget.user?.projectType?.isNotEmpty ?? false) {
      items.add(widget.user!.projectType!.first.name ?? '');
    }
    items.removeWhere((item) => item.isEmpty);
    final colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      onTap: () {
        final cubit = context.read<AudioPlayerCubit>();
        for (var player in cubit.state.players) {
          player.pause();
        }

        NavigationServiceImpl.getInstance()!.doNavigation(
          context,
          routeName: RouteName.voiceProfile,
          pathParameters: {
            Params.id: widget.user?.id.toString() ?? '',
          },
        );
      },
      child: Container(
        width: widget.width ?? (Responsive.isDesktop(context) ? 390.w : 225.w),
        padding: EdgeInsets.symmetric(
          horizontal: Responsive.isDesktop(context) ? 20.w : 12.w,
          vertical: Responsive.isDesktop(context) ? 10.h : 6.h,
        ),
        decoration: BoxDecoration(
          color: colorScheme.white,
          borderRadius: BorderRadius.circular(15.r),
          border: Border.all(
            color: colorScheme.lightShadowF3F3F3,
            width: Responsive.isDesktop(context) ? 1.5.w : 1.w,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF101828).withOpacity(0.05),
              blurRadius: 10.r,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CachedNetworkImage(
                  imageUrl: Uri.parse(widget.user?.profilePic ?? '').isAbsolute
                      ? widget.user?.profilePic ?? ''
                      : '${EnvironmentConfig.imageBaseUrl}${widget.user?.profilePic ?? ''}',
                  imageBuilder: (context, imageProvider) => Container(
                    width: Responsive.isDesktop(context) ? 76.r : 44.r,
                    height: Responsive.isDesktop(context) ? 76.r : 44.r,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      border: Border.all(
                        color: colorScheme.lightGreyB2B2B2,
                        width: Responsive.isDesktop(context) ? 1.29.w : 0.74.w,
                      ),
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  placeholder: (context, url) => Container(
                    width: Responsive.isDesktop(context) ? 76.r : 44.r,
                    height: Responsive.isDesktop(context) ? 76.r : 44.r,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      border: Border.all(
                        color: colorScheme.lightGreyB2B2B2,
                        width: Responsive.isDesktop(context) ? 1.29.w : 0.74.w,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: ClipOval(
                      child: Shimmer.fromColors(
                        baseColor: colorScheme.lightGreyF2F2F2,
                        highlightColor: colorScheme.white,
                        child: Container(
                          decoration: BoxDecoration(
                            color: colorScheme.lightGreyF2F2F2,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: Responsive.isDesktop(context) ? 76.r : 44.r,
                    height: Responsive.isDesktop(context) ? 76.r : 44.r,
                    decoration: BoxDecoration(
                      color: colorScheme.lightGreyF2F2F2,
                      border: Border.all(
                        color: colorScheme.lightGreyB2B2B2,
                        width: Responsive.isDesktop(context) ? 1.29.w : 0.74.w,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.error, color: Colors.red),
                  ),
                ),
                Responsive.isDesktop(context) ? 16.pw : 10.pw,
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Expanded(
                            child: TextTitle18And14(
                              '${widget.user?.firstName?.trim() ?? ''}${(widget.user?.firstName?.trim().isNotEmpty ?? false) && (widget.user?.lastName?.trim().isNotEmpty ?? false) ? ' ' : ''}${widget.user?.lastName?.trim() ?? ''}',
                              fontSize: Responsive.isDesktop(context) ? 22.sp : 18.sp,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Responsive.isDesktop(context) ? 10.pw : 7.pw,
                          Padding(
                            padding: EdgeInsets.only(right: Responsive.isDesktop(context) ? 0.w : 30.w),
                            child: Transform.translate(
                              offset: Offset(0, -5.h),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    AppImages.blueStarFilled,
                                    width: Responsive.isDesktop(context) ? null : 12.r,
                                    height: Responsive.isDesktop(context) ? null : 12.r,
                                  ),
                                  Responsive.isDesktop(context) ? 3.pw : 2.pw,
                                  Text(
                                    widget.user?.reviews?.averageRating?.toString() ?? 'N/A',
                                    style: TextStyle(
                                      fontSize: Responsive.isDesktop(context) ? 8.sp : 12.sp,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'NotoSans-Regular',
                                      color: colorScheme.primaryGrey,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      Responsive.isDesktop(context) ? 5.ph : 3.ph,
                      Padding(
                        padding: EdgeInsets.only(right: Responsive.isDesktop(context) ? 0.w : 30.w),
                        child: Wrap(
                          spacing: 2.w,
                          runSpacing: 1.h,
                          children: [
                            ...List.generate(items.isNotEmpty ? items.length * 2 - 1 : 0, (index) {
                              if (index.isEven) {
                                final item = items[index ~/ 2];
                                return Text(
                                  item,
                                  style: TextStyle(
                                    fontSize: Responsive.isDesktop(context) ? 12.sp : 14.sp,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'NotoSans-Regular',
                                    color: colorScheme.lightGrey5D5D5D,
                                  ),
                                );
                              } else {
                                // Dot separator
                                return Text(
                                  ' • ',
                                  style: TextStyle(
                                    fontSize: Responsive.isDesktop(context) ? 12.sp : 14.sp,
                                    color: colorScheme.lightGrey5D5D5D,
                                  ),
                                );
                              }
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            4.ph,
            Divider(color: colorScheme.lightGreyF0F0F0),
            BlocBuilder<AudioPlayerCubit, AudioPlayerState>(
              builder: (context, state) {      
                return ValueListenableBuilder(
                  valueListenable: playerStateNotifier,
                  builder: (context, playerState, child) {
                    final audioUrl = playerState.url;
                    final isPlaying = playerState.isPlaying;
                    final position = playerState.position;
                    final duration = playerState.duration;
                    final isLoading = playerState.isLoading;
      
                    return Row(
                      children: [
                        InkWell(
                          onTap: audioUrl.isEmpty
                              ? null
                              : () {
                                  final cubit = context.read<AudioPlayerCubit>();
                                  if (isPlaying) {
                                    cubit.pause(_audioPlayer);
                                  } else {
                                    cubit.play(_audioPlayer);
                                  }
                                },
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          child: Container(
                            width: 32.r,
                            height: 32.r,
                            decoration: BoxDecoration(
                              color: isPlaying ? colorScheme.hyperlinkBlueColor : colorScheme.white,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isPlaying ? colorScheme.hyperlinkBlueColor : colorScheme.lightGreyD9D9D9,
                                width: Responsive.isDesktop(context) ? 0.7.w : 0.5.w,
                              ),
                            ),
                            child: Center(
                              child: isLoading
                                  ? const CircularProgressIndicator.adaptive()
                                  : audioUrl.isNotEmpty
                                      ? SvgPicture.asset(
                                          isPlaying ? AppImages.pauseIconFilled : AppImages.playIconOutlined,
                                        )
                                      : Icon(
                                          Icons.error,
                                          color: colorScheme.redFF6257,
                                        ),
                            ),
                          ),
                        ),
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            thumbShape: RoundSliderThumbShape(
                              enabledThumbRadius: 6.r,
                            ),
                            disabledThumbColor: colorScheme.darkGrey525252,
                            thumbColor: colorScheme.darkGrey525252,
                            activeTrackColor: colorScheme.hyperlinkBlueColor.withOpacity(0.36),
                            inactiveTrackColor: colorScheme.lightGreyC8C8C8,
                            trackHeight: 5.h,
                          ),
                          child: Expanded(
                            child: Slider(
                              value: position.inSeconds.toDouble().clamp(0.0, duration.inSeconds.toDouble()),
                              max: duration.inSeconds.toDouble() > 0 ? duration.inSeconds.toDouble() : 1.0,
                              onChanged: (value) {
                                _audioPlayer.seek(Duration(seconds: value.toInt()));
                              },
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
