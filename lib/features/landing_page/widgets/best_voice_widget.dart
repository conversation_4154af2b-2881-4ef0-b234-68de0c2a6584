import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
// import 'blurred_circle.dart';
import 'find_best_voice_talent_widget.dart';
import 'orbiting_animation_widget.dart';

class BestVoiceWidget extends StatelessWidget {
  final VoidCallback onNavigateToDashboard;
  final VoidCallback onFindVoicePressed;

  const BestVoiceWidget({
    super.key,
    required this.onNavigateToDashboard,
    required this.onFindVoicePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Color(0xFFEFF3BC),
            Color(0xFFF4F6CE),
            Color(0xFFFEFFF2),
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: Center(
        child: Builder(
          builder: (context) {
            if (Responsive.isDesktop(context)) {
              return Padding(
                padding: EdgeInsets.only(left: 100.w),
                child: Row(
                  children: [
                    Expanded(
                      child: FindBestVoiceTalentWidget(
                        onNavigateToDashboard: onNavigateToDashboard,
                        onFindVoicePressed: onFindVoicePressed,
                      ),
                    ),
                    const Expanded(
                      child: OrbitingAnimationWidget(),
                    ),
                  ],
                ),
              );
            }
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 16.w),
              child: Column(
                children: [
                  FindBestVoiceTalentWidget(
                    onNavigateToDashboard: onNavigateToDashboard,
                    onFindVoicePressed: onFindVoicePressed,
                  ),
                  24.ph,
                  const OrbitingAnimationWidget(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   final colorScheme = Theme.of(context).colorScheme;
  //   return Stack(
  //     children: [
  //       Positioned(
  //         top: -500,
  //         left: -(634.r / 2),
  //         child: BlurredCircle(
  //           diameter: 634.r,
  //           color: colorScheme.primary,
  //         ),
  //       ),
  //       Positioned(
  //         right: -10.w,
  //         bottom: 55.h,
  //         child: BlurredCircle(
  //           diameter: 634.r,
  //           color: colorScheme.primary,
  //         ),
  //       ),
  //       Positioned(
  //         right: -(634.r / 2),
  //         bottom: -19.h,
  //         child: BlurredCircle(
  //           diameter: 634.r,
  //           color: colorScheme.primary,
  //         ),
  //       ),
  //       Center(
  //         child: Builder(
  //           builder: (context) {
  //             if (Responsive.isDesktop(context)) {
  //               return Padding(
  //                 padding: EdgeInsets.symmetric(vertical: 60.h).copyWith(left: 100.w),
  //                 child: Row(
  //                   children: [
  //                     Expanded(
  //                       child: FindBestVoiceTalentWidget(
  //                         onNavigateToDashboard: onNavigateToDashboard,
  //                         onFindVoicePressed: onFindVoicePressed,
  //                       ),
  //                     ),
  //                     const Expanded(
  //                       child: OrbitingAnimationWidget(),
  //                     ),
  //                   ],
  //                 ),
  //               );
  //             }
  //             return Padding(
  //               padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 16.w),
  //               child: Column(
  //                 children: [
  //                   FindBestVoiceTalentWidget(
  //                     onNavigateToDashboard: onNavigateToDashboard,
  //                     onFindVoicePressed: onFindVoicePressed,
  //                   ),
  //                   24.ph,
  //                   const OrbitingAnimationWidget(),
  //                 ],
  //               ),
  //             );
  //           },
  //         ),
  //       ),
  //     ],
  //   );
  // }
}
