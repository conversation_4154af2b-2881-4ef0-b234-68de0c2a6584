import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/custom_toast.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../utils/validations.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/dialogs.dart';
import '../bloc/contact_support_cubit.dart';
import '../data/model/contact_us_form_model.dart';

class ContactUsForm extends StatefulWidget {
  const ContactUsForm({super.key});

  @override
  State<ContactUsForm> createState() => _ContactUsFormState();
}

class _ContactUsFormState extends State<ContactUsForm> {
  final _formKey = GlobalKey<FormState>();

  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();

  bool autoValidate = false;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final isDesktop = Responsive.isDesktop(context);

    return BlocListener<ContactSupportCubit, ContactSupportState>(
      listener: (context, state) {
        if (state is ContactSupportLoadingState) {
          Dialogs.showOnlyLoader(context);
        }
    
        if (state is ContactSupportErrorState) {
          context.pop();
          CustomToast.show(context: context, message: state.errorMsg);
        }
    
        if (state is ContactSupportSuccessState) {
          context.pop();
          CustomToast.show(context: context, message: AppStrings.messageSentSuccessfully, isSuccess: true);
          _nameController.clear();
          _emailController.clear();
          _titleController.clear();
          _messageController.clear();
          setState(() {
            autoValidate = false;
            _formKey.currentState!.reset();
          });
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.white,
          borderRadius: BorderRadius.circular(5.r),
          boxShadow: [
            BoxShadow(
              color: Color(0xFF89930B).withOpacity(0.16),
              blurRadius: 22.5.r,
              spreadRadius: 12.75.r,
              offset: const Offset(0, 9),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? 36.w : 40.w,
          vertical: isDesktop ? 30.h : 24.h,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppStrings.contactUs,
                style: textTheme.displayLarge?.copyWith(
                  fontSize: 24.sp,
                ),
              ),
              12.ph,
              _ContactUsTextFieldItem(
                label: AppStrings.name,
                hintText: AppStrings.enterYourName,
                controller: _nameController,
                autoValidate: autoValidate,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z\s'-]")),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ValidationMsg.plsEntervalid(AppStrings.name.toLowerCase());
                  }
                  return null;
                },
              ),
              _ContactUsTextFieldItem(
                label: AppStrings.email.removeLastChar(),
                hintText: AppStrings.enterYourEmail,
                controller: _emailController,
                maxLength: 100,
                inputFormatters: [
                  FilteringTextInputFormatter.deny(RegExp(r'\s')),
                ],
                validator: (value) => Validator.emailValidator(value ?? ''),
                autoValidate: autoValidate,
              ),
              _ContactUsTextFieldItem(
                label: AppStrings.title.removeLastChar(),
                hintText: AppStrings.enterYourTitle,
                controller: _titleController,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ValidationMsg.plsEntervalid(AppStrings.title.removeLastChar().toLowerCase());
                  }
                  return null;
                },
                autoValidate: autoValidate,
              ),
              _ContactUsTextFieldItem(
                label: AppStrings.message,
                hintText: AppStrings.enterYourMessage,
                controller: _messageController,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ValidationMsg.plsEntervalid(AppStrings.message.toLowerCase());
                  }
                  return null;
                },
                autoValidate: autoValidate,
              ),
              50.ph,
              Align(
                alignment: Alignment.centerRight,
                child: PrimaryButton(
                  height: 44.h,
                  width: 100.w,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40.r),
                  ),
                  backgroundColor: colorScheme.hyperlinkBlueColor,
                  textColor: colorScheme.white,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      final contactUsFormModel = ContactUsFormModel(
                        name: _nameController.text.trim(),
                        email: _emailController.text.trim(),
                        title: _titleController.text.trim(),
                        message: _messageController.text.trim(),
                      );
    
                      context.read<ContactSupportCubit>().sendMessage(contactUsFormModel);
                    } else {
                      if (autoValidate) return;
                      setState(() {
                        autoValidate = true;
                      });
                    }
                  },
                  buttonText: AppStrings.send,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _titleController.dispose();
    _messageController.dispose();
    super.dispose();
  }
}

class _ContactUsTextFieldItem extends StatelessWidget {
  final String label;
  final String hintText;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final bool autoValidate;

  const _ContactUsTextFieldItem({
    required this.label,
    required this.hintText,
    required this.controller,
    this.validator,
    this.maxLength,
    this.inputFormatters,
    this.autoValidate = false,
  });

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        20.ph,
        Text(
          label,
          style: TextStyle(
            fontFamily: 'NotoSans-Regular',
            fontWeight: FontWeight.w400,
            fontSize: 10.sp,
            color: colorScheme.lightGrey9497A1,
          ),
        ),
        4.ph,
        _ContactUsTextField(
          controller: controller,
          hintText: hintText,
          validator: validator,
          inputFormatters: inputFormatters,
          maxLength: maxLength,
          autovalidateMode: autoValidate
              ? AutovalidateMode.always
              : AutovalidateMode.disabled,
        ),
      ],
    );
  }
}

class _ContactUsTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final String? Function(String?)? validator;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final AutovalidateMode? autovalidateMode;

  const _ContactUsTextField({
    required this.controller,
    required this.hintText,
    this.validator,
    this.maxLength = 350,
    this.inputFormatters,
    this.autovalidateMode,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return SizedBox(
      width: Responsive.isDesktop(context) ? 250.w : 275.w,
      child: TextFormField(
        controller: controller,
        inputFormatters: inputFormatters,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: textTheme.displaySmall?.copyWith(
            fontSize: 12.sp,
            color: colorScheme.black.withOpacity(0.75),
          ),
          isDense: true,
          contentPadding: EdgeInsets.symmetric(vertical: 8.h),
          counterText: "",
        ),
        cursorColor: colorScheme.black,
        validator: validator,
        style: textTheme.displaySmall?.copyWith(
          fontSize: 12.sp,
          color: colorScheme.black,
        ),
        maxLines: 1,
        maxLength: maxLength,
        autovalidateMode: autovalidateMode,
      ),
    );
  }
}
