import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/faq_model.dart';

class FaqWidget extends StatelessWidget {
  const FaqWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final List<FaqModel> faqs = [
      FaqModel(
        question: 'Who can join TVD?',
        answer: 'TVD is open to professional voice actors, businesses or individuals seeking voice talent, and ancillary service providers like editors, writers, and audio engineers.',
      ),
      FaqModel(
        question: 'Is there a cost to join the platform?',
        answer: 'Signing up is free for all users. Clients may pay based on project scope, while voice actors and service providers earn through completed work.',
      ),
      FaqModel(
        question: 'How does the hiring process work at TVD?',
        answer: 'Clients can post jobs or directly invite talent. Voice actors apply or get selected based on their profile, demos, and expertise.',
      ),
      FaqModel(
        question: "What services are considered 'Ancillary Services' on TVD?",
        answer: 'Services like scriptwriting, audio editing, sound mixing, post-production, and mastering fall under Ancillary Services and can be offered by verified professionals.',
      ),
      FaqModel(
        question: 'How do payments and project delivery work?',
        answer: 'All payments are managed securely through the platform. Once a project is delivered and approved, funds are released to the voice actor or service provider.',
      ),
    ];
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      width: double.infinity,
      color: colorScheme.white,
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isDesktop(context) ? 230.w : 16.w,
        vertical: Responsive.isDesktop(context) ? 60.h : 40.h,
      ),
      child: Column(
        children: [
          Text(
            'Frequently asked questions',
            style: textTheme.displayLarge?.copyWith(
              fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
              color: colorScheme.darkGrey333333,
            ),
          ),
          Responsive.isDesktop(context) ? 40.ph : 24.ph,
          ExpansionTileGroup(
            toggleType: ToggleType.expandOnlyCurrent,
            spaceBetweenItem: 20.h,
            children: faqs.map((faq) {
              return ExpansionTileItem(
                isHasLeftBorder: true,
                isHasRightBorder: true,
                isHasTopBorder: true,
                isHasBottomBorder: true,
                backgroundColor: colorScheme.white,
                collapsedBackgroundColor: colorScheme.white,
                collapsedBorderColor: colorScheme.white,
                expendedBorderColor: colorScheme.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.black.withOpacity(0.06),
                    blurRadius: 20.r,
                    spreadRadius: 4.r,
                    offset: Offset(0, 0),
                  ),
                ],
                isDefaultVerticalPadding: false,
                tilePadding: EdgeInsets.symmetric(
                  vertical: Responsive.isDesktop(context) ? 28.h : 12.h,
                ).copyWith(left: Responsive.isDesktop(context) ? 20.w : 16.w),
                trailingIcon: Padding(
                  padding: EdgeInsets.symmetric(horizontal: Responsive.isDesktop(context) ? 30.w : 16.w),
                  child: SvgPicture.asset(
                    AppImages.tileDownArrow,
                    height: Responsive.isDesktop(context) ? 14.h : 8.h,
                    width: Responsive.isDesktop(context) ? 14.w : 8.w,
                  ),
                ),
                childrenPadding: EdgeInsets.symmetric(
                  horizontal: Responsive.isDesktop(context) ? 20.w : 12.w,
                ).copyWith(bottom: 20.h),
                title: TextTitle18And14(
                  faq.question,
                  fontSize: Responsive.isDesktop(context) ? 24.sp : 18.sp,
                  color: colorScheme.black,
                ),
                children: [
                  Text(
                    faq.answer,
                    style: TextStyle(
                      fontFamily: 'NotoSans-Regular',
                      fontWeight: FontWeight.w400,
                      fontSize: 16.sp,
                      color: colorScheme.lightGrey5C5C5C,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
