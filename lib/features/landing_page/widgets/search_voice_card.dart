import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/texts/app_text.dart';
import '../../common/user_data/data/user_data_model.dart';

class SearchVoiceCard extends StatelessWidget {
  final UserDataModel? user;

  const SearchVoiceCard({
    super.key,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    final List<String> items = [];
    if (user?.voiceGender?.isNotEmpty ?? false) {
      items.add(user!.voiceGender!.first.name ?? '');
    }
    if (user?.ageRange?.isNotEmpty ?? false) {
      items.add(user!.ageRange!.first.name ?? '');
    }
    if (user?.voiceLanguage?.isNotEmpty ?? false) {
      items.add(user!.voiceLanguage!.first.name ?? '');
    }
    if (user?.voiceCharacter?.isNotEmpty ?? false) {
      items.add(user!.voiceCharacter!.first.name ?? '');
    }
    if (user?.projectType?.isNotEmpty ?? false) {
      items.add(user!.projectType!.first.name ?? '');
    }
    items.removeWhere((item) => item.isEmpty);
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      width: 325.w,
      padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 14.h),
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: colorScheme.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: colorScheme.lightShadowF3F3F3,
          width: 1.3.w,
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF101828).withOpacity(0.05),
            blurRadius: 10.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CachedNetworkImage(
            imageUrl: Uri.parse(user?.profilePic ?? '').isAbsolute
                ? user?.profilePic ?? ''
                : '${EnvironmentConfig.imageBaseUrl}${user?.profilePic ?? ''}',
            imageBuilder: (context, imageProvider) => Container(
              width: 26.r,
              height: 26.r,
              decoration: BoxDecoration(
                color: Colors.transparent,
                border: Border.all(
                  color: colorScheme.lightGreyB2B2B2,
                  width: 1.w,
                ),
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: imageProvider,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            placeholder: (context, url) => Container(
              width: 26.r,
              height: 26.r,
              decoration: BoxDecoration(
                color: Colors.transparent,
                border: Border.all(
                  color: colorScheme.lightGreyB2B2B2,
                  width: 1.w,
                ),
                shape: BoxShape.circle,
              ),
              child: ClipOval(
                child: Shimmer.fromColors(
                  baseColor: colorScheme.lightGreyF2F2F2,
                  highlightColor: colorScheme.white,
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.lightGreyF2F2F2,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              width: 26.r,
              height: 26.r,
              decoration: BoxDecoration(
                color: colorScheme.lightGreyF2F2F2,
                border: Border.all(
                  color: colorScheme.lightGreyB2B2B2,
                  width: 1.w,
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.error, color: Colors.red),
            ),
          ),
          12.pw,
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: TextTitle18And14(
                        '${user?.firstName?.trim() ?? ''}${(user?.firstName?.trim().isNotEmpty ?? false) && (user?.lastName?.trim().isNotEmpty ?? false) ? ' ' : ''}${user?.lastName?.trim() ?? ''}',
                        fontSize: 16.sp,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    10.pw,
                    Padding(
                      padding: EdgeInsets.only(right: 6.w),
                      child: Transform.translate(
                        offset: Offset(0, -5.h),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              AppImages.blueStarFilled,
                              width: 10.r,
                              height: 10.r,
                            ),
                            2.pw,
                            Text(
                              user?.reviews?.averageRating?.toString() ?? 'N/A',
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'NotoSans-Regular',
                                color: colorScheme.primaryGrey,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                5.ph,
                Wrap(
                  spacing: 2.w,
                  runSpacing: 1.h,
                  children: [
                    ...List.generate(items.isNotEmpty ? items.length * 2 - 1 : 0, (index) {
                      if (index.isEven) {
                        final item = items[index ~/ 2];
                        return Text(
                          item,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'NotoSans-Regular',
                            color: colorScheme.lightGrey5D5D5D,
                          ),
                        );
                      } else {
                        // Dot separator
                        return Text(
                          '•',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: colorScheme.lightGrey5D5D5D,
                          ),
                        );
                      }
                    }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}