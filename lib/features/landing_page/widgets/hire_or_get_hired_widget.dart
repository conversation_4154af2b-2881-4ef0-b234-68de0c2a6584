import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';

class HireOrGetHiredWidget extends StatelessWidget {
  final VoidCallback onNavigateToDashboard;

  const HireOrGetHiredWidget({
    super.key,
    required this.onNavigateToDashboard,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        if (Responsive.isDesktop(context)) {
          return Stack(
            children: [
              Positioned.fill(
                child: Image.asset(
                  AppImages.micImageDesktop,
                  fit: BoxFit.cover,
                ),
              ),
              Row(
                children: [
                  Spacer(),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 80.h),
                      child: _HireOrGetHired(onNavigateToDashboard: onNavigateToDashboard),
                    ),
                  ),
                ],
              ),
            ],
          );
        } else {
          return Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 40.h),
                child: _HireOrGetHired(onNavigateToDashboard: onNavigateToDashboard),
              ),
              Image.asset(
                AppImages.micImageMobile,
                filterQuality: FilterQuality.high,
                fit: BoxFit.contain,
                width: double.infinity,
              ),
            ],
          );
        }
      },
    );
  }
}

class _HireOrGetHired extends StatelessWidget {
  final VoidCallback onNavigateToDashboard;

  const _HireOrGetHired({
    required this.onNavigateToDashboard,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.hireOrGetHiredTitle,
          style: textTheme.displayLarge?.copyWith(
            fontSize: Responsive.isDesktop(context) ? 36.sp : 24.sp,
            color: colorScheme.darkGrey333333,
          ),
        ),
        Responsive.isDesktop(context) ? 20.ph : 16.ph,
        Text(
          AppStrings.hireOrGetHiredDescription,
          style: TextStyle(
            fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
            color: colorScheme.darkGrey333333,
            fontWeight: FontWeight.w400,
            fontFamily: 'NotoSans-Regular',
            height: Responsive.isDesktop(context) ? 2 : null,
          ),
        ),
        Responsive.isDesktop(context) ? 20.ph : 16.ph,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildItem(AppStrings.hireOrGetHiredItem1, context, colorScheme),
                _buildItem(AppStrings.hireOrGetHiredItem2, context, colorScheme),
              ],
            ),
            8.ph,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildItem(AppStrings.hireOrGetHiredItem3, context, colorScheme),
                _buildItem(AppStrings.hireOrGetHiredItem4, context, colorScheme),
              ],
            ),
          ],
        ),
        Responsive.isDesktop(context) ? 26.ph : 16.ph,
        PrimaryButton(
          height: 44.h,
          width: 150.w,
          onPressed: () {
            final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
            if (isUserLoggedIn == true) {
              onNavigateToDashboard.call();
            } else {
              NavigationServiceImpl.getInstance()?.doNavigation(
                context,
                routeName: RouteName.signUp,
              );
            }
          },
          buttonText: AppStrings.learnMore,
          textColor: colorScheme.white,
          backgroundColor: colorScheme.hyperlinkBlueColor,
        ),
      ],
    );
  }

  Widget _buildItem(
    String title,
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    return Expanded(
      child: Row(
        children: [
          Container(
            width: 8.r,
            height: 8.r,
            decoration: BoxDecoration(
              color: colorScheme.hyperlinkBlueColor,
              shape: BoxShape.circle,
            ),
          ),
          8.pw,
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w400,
                fontFamily: 'NotoSans-Regular',
                color: colorScheme.darkGrey333333,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}
