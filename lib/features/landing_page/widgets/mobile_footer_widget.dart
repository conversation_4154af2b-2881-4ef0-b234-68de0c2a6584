import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';


import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';

import '../data/model/footer_data_model.dart';

class MobileFooterWidget extends StatelessWidget {
  final List<FooterSectionData> footerSections;

  const MobileFooterWidget({
    super.key,
    required this.footerSections,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: colorScheme.darkGrey333333,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 40.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ExpansionTileGroup(
            toggleType: ToggleType.expandOnlyCurrent,
            spaceBetweenItem: 1.h,
            children: footerSections.map((section) {
              return ExpansionTileItem(
                title: Text(
                  section.title,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.white,
                    fontSize: 20.sp,
                  ),
                ),
                border: Border.all(color: Colors.transparent),
                tilePadding: EdgeInsets.zero,
                childrenPadding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 0),
                trailingIcon: SizedBox.shrink(),
                children: List.generate(
                  section.links.length,
                  (index) => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          section.links[index].onTap.call();
                        },
                        child: Text(
                          section.links[index].label,
                          style: textTheme.titleLarge?.copyWith(
                            color: colorScheme.lightGreyA6A6A6,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                      if (index != section.links.length - 1) 18.ph,
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
          40.ph,
          Row(
            children: [
              InkWell(
                onTap: () {
                  context.pushNamed(RouteName.termsAndConditions);
                },
                child: Text(
                  AppStrings.termsAndConditionsFooter,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
              10.pw,
              Container(
                width: 4.r,
                height: 4.r,
                decoration: BoxDecoration(
                  color: colorScheme.lightGreyA6A6A6,
                  shape: BoxShape.circle,
                ),
              ),
              10.pw,
              InkWell(
                onTap: () {
                  context.pushNamed(RouteName.privacyPolicy);
                },
                child: Text(
                  AppStrings.privacyPolicyFooter,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ],
          ),
          20.ph,
          Text(
            AppStrings.copyright,
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.white,
              fontSize: 14.sp,
            ),
          ),
        ],
      ),
    );
  }
}
