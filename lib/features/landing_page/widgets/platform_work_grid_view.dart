import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../utils/responsive.dart';
import '../data/model/how_platform_works_model.dart';
import 'platform_work_item.dart';

class PlatformWorkGridView extends StatelessWidget {
  final List<HowPlatformWorksModel> items;

  const PlatformWorkGridView({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    // For large screens (Desktop/Web)
    if (Responsive.isDesktop(context)) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items.map((item) {
          return Flexible(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: PlatformWorkItem(item: item),
            ),
          );
        }).toList(),
      );
    }

    return Wrap(
      alignment: WrapAlignment.spaceAround,
      runAlignment: WrapAlignment.center,
      spacing: 16.w,
      runSpacing: 32.h,
      children: items.map((item) {
        return SizedBox(
          width: (1.sw / 2) - 24.w,
          child: PlatformWorkItem(item: item),
        );
      }).toList(),
    );
  }
}