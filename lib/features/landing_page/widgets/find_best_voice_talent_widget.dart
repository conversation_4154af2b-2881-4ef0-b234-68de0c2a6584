import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';

class FindBestVoiceTalentWidget extends StatelessWidget {
  final VoidCallback onFindVoicePressed;
  final VoidCallback onNavigateToDashboard;

  const FindBestVoiceTalentWidget({
    super.key,
    required this.onFindVoicePressed,
    required this.onNavigateToDashboard,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final serviceNames = [
      AppStrings.voiceTalent,
      AppStrings.client.toUpperCase(),
      AppStrings.ancillary.toUpperCase(),
    ];

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.oneStopPlatformToFind,
          style: TextStyle(
            fontSize: Responsive.isDesktop(context) ? 26.sp : 16.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'NotoSans-Regular',
            color: colorScheme.primaryGrey,
            letterSpacing: 0.17,
          ),
        ),
        Text(
          AppStrings.theBestProfessional,
          style: textTheme.displayLarge?.copyWith(
            fontSize: Responsive.isDesktop(context) ? 64.sp : 42.sp,
            letterSpacing: 0.17,
          ),
        ),
        SizedBox(
          height: Responsive.isDesktop(context) ? 64.h : 44.h,
          width: 1.sw,
          child: CarouselSlider.builder(
            options: CarouselOptions(
              scrollDirection: Axis.vertical,
              viewportFraction: 1,
              padEnds: false,
              autoPlay: true,
              pauseAutoPlayOnTouch: false,
              pauseAutoPlayOnManualNavigate: false,
              autoPlayInterval: const Duration(milliseconds: 1800),
              scrollPhysics: const NeverScrollableScrollPhysics(),
            ),
            itemCount: serviceNames.length,
            itemBuilder: (context, index, realIndex) {
              return Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: serviceNames[index],
                      style: textTheme.displayLarge?.copyWith(
                        fontSize: Responsive.isDesktop(context) ? 64.sp : 42.sp,
                        color: colorScheme.hyperlinkBlueColor,
                        letterSpacing: 0.17,
                      ),
                    ),
                    TextSpan(
                      text: '!',
                      style: textTheme.displayLarge?.copyWith(
                        fontSize: Responsive.isDesktop(context) ? 64.sp : 42.sp,
                        letterSpacing: 0.17,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        16.ph,
        Text(
          AppStrings.findVoiceTalentDescription,
          style: TextStyle(
            fontSize: Responsive.isDesktop(context) ? 20.sp : 14.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'NotoSans-Regular',
            color: colorScheme.primaryGrey,
          ),
        ),
        16.ph,
        Row(
          children: [
            PrimaryButton(
              width: 138.w,
              height: 44.h,
              onPressed: () {
                final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
                if (isUserLoggedIn == true) {
                  onNavigateToDashboard.call();
                } else {
                  NavigationServiceImpl.getInstance()?.doNavigation(
                    context,
                    routeName: RouteName.signUp,
                  );
                }
              },
              buttonText: AppStrings.findWork,
              backgroundColor: colorScheme.hyperlinkBlueColor,
              textColor: colorScheme.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(40.r),
              ),
            ),
            18.pw,
            PrimaryButton(
              width: 138.w,
              height: 44.h,
              onPressed: () {
                onFindVoicePressed.call();
              },
              buttonText: AppStrings.findVoice,
              shadowColor: false,
              backgroundColor: Colors.transparent,
              textColor: colorScheme.hyperlinkBlueColor,
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: colorScheme.hyperlinkBlueColor,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(40.r),
              ),
            ),
          ],
        ),
        Responsive.isDesktop(context) ? 40.ph : 20.ph,
        Row(
          children: [
            InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                launchUrl(
                  Uri.parse(
                    'https://apps.apple.com/us',
                  ),
                );
              },
              child: SvgPicture.asset(
                AppImages.appStoreLogo,
                width: Responsive.isDesktop(context) ? null : 132.w,
                height: Responsive.isDesktop(context) ? null : 38.h,
              ),
            ),
            Responsive.isDesktop(context) ? 26.pw : 16.pw,
            InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                launchUrl(
                  Uri.parse(
                    'https://play.google.com/store/apps/details?id=com.legup.customer.android',
                  ),
                );
              },
              child: SvgPicture.asset(
                AppImages.googlePlayLogo,
                width: Responsive.isDesktop(context) ? null : 132.w,
                height: Responsive.isDesktop(context) ? null : 38.h,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
