import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/audio_player_cubit.dart';
import '../bloc/explore_public_voices_cubit.dart';
import 'audio_card.dart';
// import 'blurred_circle.dart';
import 'shimmer/audio_card_shimmer.dart';

class ExploreVoicesWidget extends StatefulWidget {
  const ExploreVoicesWidget({super.key});

  @override
  State<ExploreVoicesWidget> createState() => _ExploreVoicesWidgetState();
}

class _ExploreVoicesWidgetState extends State<ExploreVoicesWidget> {

  @override
  void initState() {
    super.initState();
    context.read<ExplorePublicVoicesCubit>().fetchTopPublicVoices();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return BlocProvider(
      create: (_) => AudioPlayerCubit(),
      child: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: double.infinity,
            colors: [
              Color(0xFFEBEFF7),
              Color(0xFFFFFFFF),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: Center(
          child: Builder(
            builder: (context) {
              return Padding(
                padding: EdgeInsets.only(
                  top: Responsive.isDesktop(context) ? 100.h : 50.h,
                  bottom: Responsive.isDesktop(context) ? 100.h : 36.h,
                  left: Responsive.isDesktop(context) ? 100.w : 16.w,
                  right: Responsive.isDesktop(context) ? 100.w : 16.w,
                ),
                child: Column(
                  children: [
                    Text(
                      AppStrings.exploreVoicesTitle,
                      style: textTheme.displayLarge?.copyWith(
                        fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
                        color: colorScheme.darkGrey333333,
                      ),
                    ),
                    Responsive.isDesktop(context) ? 24.ph : 16.ph,
                    Text(
                      AppStrings.exploreVoicesDescription,
                      style: TextStyle(
                        fontFamily: 'NotoSans-Regular',
                        fontWeight: FontWeight.w400,
                        fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
                        color: colorScheme.primaryGrey,
                      ),
                    ),
                    Responsive.isDesktop(context) ? 24.ph : 16.ph,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text24And20SemiBold(
                          AppStrings.topArtists,
                          fontSize: Responsive.isDesktop(context) ? 22.sp : 14.sp,
                          color: colorScheme.primaryGrey,
                        ),
                        if (Responsive.isDesktop(context)) ...[
                          InkWell(
                            hoverColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () {
                              final cubit = context.read<AudioPlayerCubit>();
                              for (var player in cubit.state.players) {
                                player.pause();
                              }

                              NavigationServiceImpl.getInstance()!.doNavigation(
                                context,
                                routeName: RouteName.explorePublicVoices,
                              );
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  AppStrings.seeMoreArtists,
                                  style: textTheme.displayLarge?.copyWith(
                                    fontSize: Responsive.isDesktop(context) ? 22.sp : 18.sp,
                                    color: colorScheme.primaryGrey,
                                  ),
                                ),
                                Responsive.isDesktop(context) ? 4.pw : 2.pw,
                                Icon(
                                  Icons.arrow_forward_rounded,
                                  size: Responsive.isDesktop(context) ? 20.h : 16.h,
                                  color: colorScheme.primaryGrey,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    Responsive.isDesktop(context) ? 24.ph : 12.ph,
                    Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: BlocBuilder<ExplorePublicVoicesCubit, ExplorePublicVoicesState>(
                        builder: (context, state) {
                          if (state is ExplorePublicVoicesLoadingState) {
                            if (Responsive.isDesktop(context)) {
                              return LayoutBuilder(
                                builder: (context, constraints) {
                                  final double totalWidth = constraints.maxWidth;
                                  const int crossAxisCount = 3;
                                  final double spacing = 20.w;
                                  final double cardWidth = (totalWidth - (spacing * (crossAxisCount - 1))) / crossAxisCount;
                                  return Wrap(
                                    spacing: spacing,
                                    runSpacing: 20.h,
                                    children: List.generate(
                                      6,
                                      (_) => AudioCardShimmer(width: cardWidth, height: 168.5.h),
                                    ),
                                  );
                                }
                              );
                            }
                            return ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (_, __) => const AudioCardShimmer(),
                              separatorBuilder: (_, __) => 16.ph,
                              itemCount: 4,
                            );
                          }
                          if (state is ExplorePublicVoicesSuccessState) {
                            final topPublicVoices = state.artistsList;
                            if (Responsive.isDesktop(context)) {
                              return LayoutBuilder(
                                builder: (context, constraints) {
                                  final double totalWidth = constraints.maxWidth;
                                  const int crossAxisCount = 3;
                                  final double spacing = 20.w;
                                  final double cardWidth = (totalWidth - (spacing * (crossAxisCount - 1))) / crossAxisCount;
                                  return Wrap(
                                    spacing: spacing,
                                    runSpacing: 20.h,
                                    children: List.generate(
                                      topPublicVoices.length > 6 ? 6 : topPublicVoices.length,
                                      (index) => AudioCard(
                                        user: topPublicVoices[index],
                                        width: cardWidth,
                                      ),
                                    ),
                                  );
                                }
                              );
                            }
                            return ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              separatorBuilder: (_, __) => 16.ph,
                              itemCount: topPublicVoices.length > 4 ? 4 : topPublicVoices.length,
                              itemBuilder: (_, index) {
                                return AudioCard(
                                  user: topPublicVoices[index],
                                );
                              },
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                    if (!Responsive.isDesktop(context)) ...[
                      24.ph,
                      InkWell(
                        hoverColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () {
                          NavigationServiceImpl.getInstance()!.doNavigation(
                            context,
                            routeName: RouteName.explorePublicVoices,
                          );
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              AppStrings.seeMoreArtists,
                              style: textTheme.displayLarge?.copyWith(
                                fontSize: 18.sp,
                                color: colorScheme.primaryGrey,
                              ),
                            ),
                            2.ph,
                            Icon(
                              Icons.arrow_forward_rounded,
                              size: 16.h,
                              color: colorScheme.primaryGrey,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   final colorScheme = Theme.of(context).colorScheme;
  //   final textTheme = Theme.of(context).textTheme;
  //   return BlocProvider(
  //     create: (_) => AudioPlayerCubit(),
  //     child: Stack(
  //       children: [
  //         Positioned(
  //           bottom: 0,
  //           left: 0,
  //           right: 0,
  //           child: BlurredCircle(
  //             diameter: 419.r,
  //             color: colorScheme.hyperlinkBlueColor.withOpacity(0.66),
  //             // blurSigma: 0,
  //           ),
  //         ),
  //         Center(
  //           child: Builder(
  //             builder: (context) {
  //               return Padding(
  //                 padding: EdgeInsets.only(
  //                   top: Responsive.isDesktop(context) ? 100.h : 50.h,
  //                   bottom: Responsive.isDesktop(context) ? 100.h : 36.h,
  //                   left: Responsive.isDesktop(context) ? 100.w : 16.w,
  //                   right: Responsive.isDesktop(context) ? 100.w : 16.w,
  //                 ),
  //                 child: Column(
  //                   children: [
  //                     Text(
  //                       AppStrings.exploreVoices.capitalizeEachWord(),
  //                       style: textTheme.displayLarge?.copyWith(
  //                         fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
  //                         color: colorScheme.darkGrey333333,
  //                       ),
  //                     ),
  //                     Responsive.isDesktop(context) ? 24.ph : 16.ph,
  //                     Text(
  //                       AppStrings.exploreVoicesDescription,
  //                       style: TextStyle(
  //                         fontFamily: 'NotoSans-Regular',
  //                         fontWeight: FontWeight.w400,
  //                         fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
  //                         color: colorScheme.primaryGrey,
  //                       ),
  //                     ),
  //                     Responsive.isDesktop(context) ? 24.ph : 16.ph,
  //                     Row(
  //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                       children: [
  //                         Text24And20SemiBold(
  //                           AppStrings.topArtists,
  //                           fontSize: Responsive.isDesktop(context) ? 22.sp : 14.sp,
  //                           color: colorScheme.primaryGrey,
  //                         ),
  //                         if (Responsive.isDesktop(context)) ...[
  //                           InkWell(
  //                             hoverColor: Colors.transparent,
  //                             splashColor: Colors.transparent,
  //                             focusColor: Colors.transparent,
  //                             highlightColor: Colors.transparent,
  //                             onTap: () {
  //                               NavigationServiceImpl.getInstance()!.doNavigation(
  //                                 context,
  //                                 routeName: RouteName.explorePublicVoices,
  //                                 useGo: true,
  //                               );
  //                             },
  //                             child: Row(
  //                               mainAxisAlignment: MainAxisAlignment.center,
  //                               children: [
  //                                 Text(
  //                                   AppStrings.seeMoreArtists,
  //                                   style: textTheme.displayLarge?.copyWith(
  //                                     fontSize: Responsive.isDesktop(context) ? 22.sp : 18.sp,
  //                                     color: colorScheme.primaryGrey,
  //                                   ),
  //                                 ),
  //                                 Responsive.isDesktop(context) ? 4.ph : 2.ph,
  //                                 Icon(
  //                                   Icons.arrow_forward_rounded,
  //                                   size: Responsive.isDesktop(context) ? 20.h : 16.h,
  //                                   color: colorScheme.primaryGrey,
  //                                 ),
  //                               ],
  //                             ),
  //                           ),
  //                         ],
  //                       ],
  //                     ),
  //                     Responsive.isDesktop(context) ? 24.ph : 12.ph,
  //                     BlocBuilder<ExplorePublicVoicesCubit, ExplorePublicVoicesState>(
  //                       builder: (context, state) {
  //                         if (state is ExplorePublicVoicesLoadingState) {
  //                           if (Responsive.isDesktop(context)) {
  //                             return Wrap(
  //                               spacing: 20.w,
  //                               runSpacing: 20.h,
  //                               children: List.generate(
  //                                 6,
  //                                 (_) => AudioCardShimmer(width: 320.w, height: 168.5.h),
  //                               ),
  //                             );
  //                           }
  //                           return ListView.separated(
  //                             shrinkWrap: true,
  //                             physics: const NeverScrollableScrollPhysics(),
  //                             itemBuilder: (_, __) => const AudioCardShimmer(),
  //                             separatorBuilder: (_, __) => 16.ph,
  //                             itemCount: 4,
  //                           );
  //                         }
  //                         if (state is ExplorePublicVoicesSuccessState) {
  //                           final topPublicVoices = state.artistsList;
  //                           if (Responsive.isDesktop(context)) {
  //                             return Wrap(
  //                               spacing: 20.w,
  //                               runSpacing: 20.h,
  //                               children: List.generate(
  //                                 topPublicVoices.length > 6 ? 6 : topPublicVoices.length,
  //                                 (index) => AudioCard(
  //                                   user: topPublicVoices[index],
  //                                   width: Responsive.isDesktop(context) ? 320.w : null,
  //                                 ),
  //                               ),
  //                             );
  //                           }
  //                           return ListView.separated(
  //                             padding: EdgeInsets.zero,
  //                             shrinkWrap: true,
  //                             physics: const NeverScrollableScrollPhysics(),
  //                             separatorBuilder: (_, __) => 16.ph,
  //                             itemCount: topPublicVoices.length > 4 ? 4 : topPublicVoices.length,
  //                             itemBuilder: (_, index) {
  //                               return AudioCard(
  //                                 user: topPublicVoices[index],
  //                               );
  //                             },
  //                           );
  //                         }
  //                         return const SizedBox.shrink();
  //                       },
  //                     ),
  //                     if (!Responsive.isDesktop(context)) ...[
  //                       24.ph,
  //                       InkWell(
  //                         hoverColor: Colors.transparent,
  //                         splashColor: Colors.transparent,
  //                         focusColor: Colors.transparent,
  //                         highlightColor: Colors.transparent,
  //                         onTap: () {
  //                           NavigationServiceImpl.getInstance()!.doNavigation(
  //                             context,
  //                             routeName: RouteName.explorePublicVoices,
  //                             useGo: true,
  //                           );
  //                         },
  //                         child: Row(
  //                           mainAxisAlignment: MainAxisAlignment.center,
  //                           children: [
  //                             Text(
  //                               AppStrings.seeMoreArtists,
  //                               style: textTheme.displayLarge?.copyWith(
  //                                 fontSize: 18.sp,
  //                                 color: colorScheme.primaryGrey,
  //                               ),
  //                             ),
  //                             2.ph,
  //                             Icon(
  //                               Icons.arrow_forward_rounded,
  //                               size: 16.h,
  //                               color: colorScheme.primaryGrey,
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ],
  //                   ],
  //                 ),
  //               );
  //             },
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
