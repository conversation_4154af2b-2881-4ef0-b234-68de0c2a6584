// import 'dart:ui';

// import 'package:flutter/material.dart';

// class BlurredCircle extends StatelessWidget {
//   final double diameter;
//   final Color color;
//   final double blurSigma;

//   const BlurredCircle({
//     super.key,
//     required this.diameter,
//     required this.color,
//     this.blurSigma = 1000.0,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return ImageFiltered(
//       imageFilter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
//       child: Container(
//         width: diameter,
//         height: diameter,
//         decoration: BoxDecoration(
//           color: color,
//           shape: BoxShape.circle,
//         ),
//       ),
//     );
//   }
// }
