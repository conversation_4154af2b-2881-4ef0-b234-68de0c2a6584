import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../data/model/orbiting_icon_data.dart';
import 'static_audio_card.dart';

class OrbitingAnimationWidget extends StatefulWidget {
  const OrbitingAnimationWidget({super.key});

  @override
  State<OrbitingAnimationWidget> createState() => _OrbitingAnimationWidgetState();
}

class _OrbitingAnimationWidgetState extends State<OrbitingAnimationWidget> with TickerProviderStateMixin {
  late final AnimationController _entryAnimationController;
  late final AnimationController _orbitAnimationController;
  late final Animation<double> _entryAnimation;

  @override
  void initState() {
    super.initState();
    
    _orbitAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 15),
    );

    _entryAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 750),
    );

    _entryAnimation = CurvedAnimation(
      parent: _entryAnimationController,
      curve: Curves.easeOut,
    );
    
    // Chain the animations:
    // 1. Start the entry animation.
    // 2. When it completes, start the orbit animation on repeat.
    _entryAnimationController.forward().whenComplete(() {
      _orbitAnimationController.repeat();
    });
  }

  @override
  void dispose() {
    _entryAnimationController.dispose();
    _orbitAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double scaleFactor;
    if (Responsive.isDesktop(context)) {
      scaleFactor = 1.0;
    } else {
      scaleFactor = 0.7;
    }

    final List<OrbitingIconData> icons = [
      OrbitingIconData(
        path: AppImages.micFilled,
        initialAngle: math.pi * 0.2,
        radius: Responsive.isDesktop(context) ? (200.r * scaleFactor) : (165.r * scaleFactor),
        size: Responsive.isDesktop(context) ? 100.r : 75.r,
      ),
      OrbitingIconData(
        path: AppImages.musicFilled,
        initialAngle: math.pi * 0.7,
        radius: Responsive.isDesktop(context) ? (220.r * scaleFactor) : (180.r * scaleFactor),
        size: Responsive.isDesktop(context) ? 52.r : 45.r,
      ),
      OrbitingIconData(
        path: AppImages.appStoreFilled,
        initialAngle: math.pi * 1.2,
        radius: Responsive.isDesktop(context) ? (230.r * scaleFactor) : (172.5.r * scaleFactor),
        size: Responsive.isDesktop(context) ? 40.r : 36.r,
      ),
      OrbitingIconData(
        path: AppImages.podcastFilled,
        initialAngle: math.pi * 1.6,
        radius: Responsive.isDesktop(context) ? (215.r * scaleFactor) : (175.r * scaleFactor),
        size: Responsive.isDesktop(context) ? 32.r : 28.5.r,
      ),
      OrbitingIconData(
        path: AppImages.playStoreFilled,
        initialAngle: math.pi * 1.9,
        radius: Responsive.isDesktop(context) ? (240.r * scaleFactor) : (200.r * scaleFactor),
        size: Responsive.isDesktop(context) ? 55.r : 45.r,
        isSvg: false,
      ),
    ];
    return AnimatedBuilder(
      animation: Listenable.merge([_entryAnimationController, _orbitAnimationController]),
      builder: (context, child) {
        return Center(
          child: Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              ..._buildOrbitingIcons(icons: icons, isBehind: true),

              Image.asset(AppImages.personCutout),

              ..._buildOrbitingIcons(icons: icons, isBehind: false),

              Positioned(
                bottom: Responsive.isDesktop(context) ? 50.h : 0.h,
                left: Responsive.isDesktop(context) ? null : 30.w,
                right: Responsive.isDesktop(context) ? null : 60.w,
                child: StaticAudioCard(
                  width: Responsive.isDesktop(context) ? 390.w : 225.w,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<Widget> _buildOrbitingIcons({
    required List<OrbitingIconData> icons,
    required bool isBehind,
  }) {
    return icons.map((iconData) {
      final double angle = _orbitAnimationController.isAnimating
          ? (_orbitAnimationController.value * 2 * math.pi + iconData.initialAngle)
          : iconData.initialAngle;

      final bool isInFront = math.sin(angle) > 0;

      if ((isBehind && isInFront) || (!isBehind && !isInFront)) {
        return const SizedBox.shrink();
      }

      final double currentRadius = iconData.radius * _entryAnimation.value;
      final double x = math.cos(angle) * currentRadius;

      final double y = math.sin(angle) * currentRadius * 0.8;

      final double orbitScale = 1.0 + (math.sin(angle) * 0.15);
      final double finalScale = orbitScale * _entryAnimation.value;

      return Transform.translate(
        offset: Offset(x, y),
        child: Transform.scale(
          scale: finalScale,
          child: iconData.isSvg
              ? SvgPicture.asset(iconData.path, width: iconData.size)
              : Image.asset(iconData.path, width: iconData.size),
        ),
      );
    }).toList();
  }
}