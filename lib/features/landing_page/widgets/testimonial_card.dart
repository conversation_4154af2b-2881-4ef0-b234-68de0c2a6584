import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/testimonial_model.dart';

class TestimonialCard extends StatelessWidget {
  final TestimonialModel testimonial;

  const TestimonialCard({
    super.key,
    required this.testimonial,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDesktop = Responsive.isDesktop(context);

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isDesktop ? 0 : 16.w,
        vertical: 20.h,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: isDesktop ? 30.w : 24.w,
        vertical: isDesktop ? 40.h : 35.h,
      ),
      decoration: BoxDecoration(
        color: colorScheme.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 25,
            offset: const Offset(0, 5),
          )
        ],
      ),
      child: Column(
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              CircleAvatar(
                radius: isDesktop ? 75.r : 37.5.r,
                backgroundImage: AssetImage(AppImages.staticAudioProfile),
                // backgroundImage: NetworkImage(testimonial.imageUrl),
                backgroundColor: colorScheme.lightGreyF9F9F9,
              ),
              Positioned(
                bottom: -5.r,
                right: -5.r,
                child: Container(
                  height: isDesktop ? 55.r : 35.r,
                  width: isDesktop ? 55.r : 35.r,
                  decoration: BoxDecoration(
                    color: colorScheme.hyperlinkBlueColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      AppImages.quoteIcon,
                      width: isDesktop ? null : 14.r,
                    ),
                  ),
                ),
              )
            ],
          ),
          isDesktop ? 22.ph : 28.ph,
          Text(
            testimonial.quote,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'NotoSans-Regular',
              fontWeight: FontWeight.w400,
              fontSize: isDesktop ? 18.sp : 14.sp,
              color: colorScheme.primaryGrey,
              height: 1.6,
            ),
            maxLines: 5,
            overflow: TextOverflow.ellipsis,
          ),
          const Spacer(),
          // Divider
          Container(
            height: 4.h,
            width: 52.5.w,
            color: colorScheme.secondary,
          ),
          isDesktop ? 18.ph : 14.ph,
          // User Name
          Text24And20SemiBold(
            testimonial.name,
            fontSize: isDesktop ? 24.sp : 18.sp,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (isDesktop) 6.ph,
          // User Role
          Text(
            testimonial.role,
            style: TextStyle(
              fontFamily: 'NotoSans-Regular',
              fontWeight: FontWeight.w400,
              fontSize: isDesktop ? 18.sp : 14.sp,
              color: colorScheme.lightGrey656057,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
