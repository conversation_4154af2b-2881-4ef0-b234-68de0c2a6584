import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';

import '../data/model/footer_data_model.dart';

class DesktopFooterWidget extends StatelessWidget {
  final List<FooterSectionData> footerSections;

  const DesktopFooterWidget({
    super.key,
    required this.footerSections,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      color: colorScheme.darkGrey333333,
      padding: EdgeInsets.only(
        left: 130.w,
        right: 130.w,
        top: 80.h,
        bottom: 40.h,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset(
                    AppImages.tvdCompleteLogo,
                    width: 100.w,
                  ),
                  30.ph,
                  Text(
                    AppStrings.copyright,
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.white,
                      fontSize: 18.sp,
                    ),
                  ),
                  Text(
                    AppStrings.allRightsReserved,
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.white,
                      fontSize: 18.sp,
                    ),
                  ),
                ],
              ),
              const Spacer(flex: 2),
              Flexible(
                flex: 3,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: footerSections
                      .map((section) => _DesktopFooterColumn(section: section))
                      .toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   final textTheme = Theme.of(context).textTheme;
  //   final colorScheme = Theme.of(context).colorScheme;

  //   return Container(
  //     color: colorScheme.darkGrey333333,
  //     padding: EdgeInsets.only(
  //       left: 130.w,
  //       right: 130.w,
  //       top: 80.h,
  //       bottom: 40.h,
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Row(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Flexible(
  //               flex: 6,
  //               child: Row(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: footerSections
  //                     .map((section) => _DesktopFooterColumn(section: section))
  //                     .toList(),
  //               ),
  //             ),
  //             const Spacer(flex: 1),
  //           ],
  //         ),
  //         56.ph,
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               AppStrings.copyright,
  //               style: textTheme.titleLarge?.copyWith(
  //                 color: colorScheme.white,
  //                 fontSize: 18.sp,
  //               ),
  //             ),
  //             Row(
  //               children: [
  //                 InkWell(
  //                   splashColor: Colors.transparent,
  //                   highlightColor: Colors.transparent,
  //                   hoverColor: Colors.transparent,
  //                   onTap: () {
  //                     // TODO: Implement on tap logic
  //                   },
  //                   child: Text(
  //                     AppStrings.termsAndConditionsFooter,
  //                     style: textTheme.titleLarge?.copyWith(
  //                       color: colorScheme.white,
  //                       fontSize: 18.sp,
  //                     ),
  //                   ),
  //                 ),
  //                 10.pw,
  //                 Container(
  //                   width: 4.r,
  //                   height: 4.r,
  //                   decoration: BoxDecoration(
  //                     color: colorScheme.lightGreyA6A6A6,
  //                     shape: BoxShape.circle,
  //                   ),
  //                 ),
  //                 10.pw,
  //                 InkWell(
  //                   splashColor: Colors.transparent,
  //                   highlightColor: Colors.transparent,
  //                   hoverColor: Colors.transparent,
  //                   onTap: () {
  //                     // TODO: Implement on tap logic
  //                   },
  //                   child: Text(
  //                     AppStrings.privacyPolicyFooter,
  //                     style: textTheme.titleLarge?.copyWith(
  //                       color: colorScheme.white,
  //                       fontSize: 18.sp,
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ],
  //     ),
  //   );
  // }
}

class _DesktopFooterColumn extends StatelessWidget {
  final FooterSectionData section;

  const _DesktopFooterColumn({required this.section});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          section.title,
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.white,
            fontSize: 18.sp,
          ),
        ),
        27.ph,
        ...List.generate(
          section.links.length,
          (index) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onTap: () {
                  section.links[index].onTap.call();
                },
                child: Text(
                  section.links[index].label,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.lightGreyA6A6A6,
                    fontSize: 18.sp,
                  ),
                ),
              ),
              if (index != section.links.length - 1) 18.ph,
            ],
          ),
        ),
      ],
    );
  }
}
