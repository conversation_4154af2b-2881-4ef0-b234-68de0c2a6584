import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/testimonial_model.dart';
// import 'blurred_circle.dart';
import 'testimonial_card.dart';

class UserTestimonials extends StatefulWidget {
  const UserTestimonials({super.key});

  @override
  State<UserTestimonials> createState() => _UserTestimonialsState();
}

class _UserTestimonialsState extends State<UserTestimonials> {
  final List<TestimonialModel> testimonials = [
    TestimonialModel(
      imageUrl: 'https://i.pravatar.cc/150?img=11',
      quote: "“Working with <PERSON><PERSON><PERSON><PERSON> is a breath of fresh air, they delivered my project on time and exceeded my expectations. I’m especially fond of their beautiful and insightful illustrations”",
      name: "<PERSON>",
      role: "Voice Artist",
    ),
    TestimonialModel(
      imageUrl: 'https://i.pravatar.cc/150?img=32',
      quote: "“Working with DianApps is a breath of fresh air, they delivered my project on time and exceeded my expectations. I’m especially fond of their beautiful and insightful illustrations”",
      name: "Joe <PERSON>",
      role: "Voice Artist",
    ),
    TestimonialModel(
      imageUrl: 'https://i.pravatar.cc/150?img=1',
      quote: "“Working with DianApps is a breath of fresh air, they delivered my project on time and exceeded my expectations. I’m especially fond of their beautiful and insightful illustrations”",
      name: "Mitch Marsh",
      role: "Voice Company",
    ),
  ];

  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDesktop = Responsive.isDesktop(context);

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
          colors: [
            Color(0xFFFBFCEC),
            Color(0xFFE9ECF1),
            Color(0xFFDBE3F4),
          ],
          stops: [0.0, 0.48, 1.0],
        ),
      ),
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: isDesktop ? 80.h : 60.h,
      ),
      child: Column(
        children: [
          Text(
            AppStrings.whatOurUsersSay,
            style: textTheme.displayLarge?.copyWith(
              fontSize: isDesktop ? 38.sp : 28.sp,
              color: colorScheme.darkGrey333333,
            ),
          ),
          isDesktop ? 30.ph : 16.ph,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              AppStrings.checkOutSomeTestimonials,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'NotoSans-Regular',
                fontWeight: FontWeight.w400,
                fontSize: isDesktop ? 18.sp : 14.sp,
                color: colorScheme.primaryGrey,
              ),
            ),
          ),
          isDesktop ? 40.ph : 32.ph,
          CarouselSlider(
            items: testimonials
                .map((e) => TestimonialCard(testimonial: e))
                .toList(),
            carouselController: _controller,
            options: CarouselOptions(
              viewportFraction: isDesktop ? 0.31 : 1,
              enlargeFactor: isDesktop ? 0.25 : 0,
              enlargeCenterPage: true,
              height: isDesktop ? 550.h : 450.h,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              scrollPhysics: const NeverScrollableScrollPhysics(),
            ),
          ),
          40.ph,
    
          // Navigation Arrows
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _ScrollArrows(isLeft: true, controller: _controller),
              20.w.pw,
              _ScrollArrows(isLeft: false, controller: _controller),
            ],
          )
        ],
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   final colorScheme = Theme.of(context).colorScheme;
  //   final textTheme = Theme.of(context).textTheme;
  //   final isDesktop = Responsive.isDesktop(context);

  //   return Stack(
  //     children: [
  //       Positioned(
  //         top: 90.h,
  //         left: -125.r,
  //         child: BlurredCircle(
  //           diameter: 634.r,
  //           color: colorScheme.hyperlinkBlueColor.withOpacity(0.66),
  //         ),
  //       ),
  //       Positioned(
  //         right: -460.r,
  //         bottom: 0.h,
  //         child: BlurredCircle(
  //           diameter: 634.r,
  //           color: colorScheme.primary.withOpacity(0.48),
  //         ),
  //       ),

  //       Container(
  //         color: Colors.transparent,
  //         width: double.infinity,
  //         padding: EdgeInsets.symmetric(
  //           vertical: isDesktop ? 80.h : 60.h,
  //         ),
  //         child: Column(
  //           children: [
  //             Text(
  //               AppStrings.whatOurUsersSay,
  //               style: textTheme.displayLarge?.copyWith(
  //                 fontSize: isDesktop ? 38.sp : 28.sp,
  //                 color: colorScheme.darkGrey333333,
  //               ),
  //             ),
  //             isDesktop ? 30.ph : 16.ph,
  //             Padding(
  //               padding: EdgeInsets.symmetric(horizontal: 16.w),
  //               child: Text(
  //                 AppStrings.checkOutSomeTestimonials,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontFamily: 'NotoSans-Regular',
  //                   fontWeight: FontWeight.w400,
  //                   fontSize: isDesktop ? 18.sp : 14.sp,
  //                   color: colorScheme.primaryGrey,
  //                 ),
  //               ),
  //             ),
  //             isDesktop ? 40.ph : 32.ph,
  //             CarouselSlider(
  //               items: testimonials
  //                   .map((e) => TestimonialCard(testimonial: e))
  //                   .toList(),
  //               carouselController: _controller,
  //               options: CarouselOptions(
  //                 viewportFraction: isDesktop ? 0.31 : 1,
  //                 enlargeFactor: isDesktop ? 0.25 : 0,
  //                 enlargeCenterPage: true,
  //                 height: isDesktop ? 550.h : 450.h,
  //                 autoPlay: true,
  //                 autoPlayInterval: const Duration(seconds: 3),
  //                 scrollPhysics: const NeverScrollableScrollPhysics(),
  //               ),
  //             ),
  //             40.ph,

  //             // Navigation Arrows
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 _ScrollArrows(isLeft: true, controller: _controller),
  //                 20.w.pw,
  //                 _ScrollArrows(isLeft: false, controller: _controller),
  //               ],
  //             )
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }
}

class _ScrollArrows extends StatelessWidget {
  final bool isLeft;
  final CarouselSliderController controller;

  const _ScrollArrows({required this.isLeft, required this.controller});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      onTap: () {
        if (isLeft) {
          controller.previousPage();
        } else {
          controller.nextPage();
        }
      },
      child: Container(
        padding: EdgeInsets.all(8.r),
        decoration: BoxDecoration(
          color: colorScheme.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          isLeft ? Icons.arrow_back : Icons.arrow_forward,
          color: colorScheme.black,
          size: 20.r,
        ),
      ),
    );
  }
}