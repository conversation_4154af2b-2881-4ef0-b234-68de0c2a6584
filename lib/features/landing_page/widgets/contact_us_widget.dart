import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../bloc/contact_support_cubit.dart';
import 'contact_us_details.dart';
import 'contact_us_form.dart';

class ContactUsWidget extends StatelessWidget {
  const ContactUsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDesktop = Responsive.isDesktop(context);
    return BlocProvider(
      create: (_) => ContactSupportCubit(),
      child: Container(
        color: colorScheme.lightGreenFDFFDA,
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          vertical: isDesktop ? 80.h : 40.h,
          horizontal: isDesktop ? 0 : 16.w,
        ),
        child: Builder(builder: (context) {
          if (isDesktop) {
            return Row(
              children: [
                Spacer(flex: 2),
                const Expanded(flex: 4, child: ContactUsDetails()),
                60.pw,
                const Expanded(flex: 3, child: ContactUsForm()),
                Spacer(flex: 2),
              ],
            );
          }
          return Column(
            children: [
              const ContactUsDetails(),
              36.ph,
              const ContactUsForm(),
            ],
          );
        }),
      ),
    );
  }
}