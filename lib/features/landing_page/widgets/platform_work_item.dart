import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/how_platform_works_model.dart';

class PlatformWorkItem extends StatelessWidget {
  final HowPlatformWorksModel item;

  const PlatformWorkItem({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: Responsive.isDesktop(context) ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: Responsive.isDesktop(context) ? 255.w : 156.w,
          height: Responsive.isDesktop(context) ? 170.h : 112.h,
          child: SvgPicture.asset(item.image),
        ),
        Responsive.isDesktop(context) ? 30.ph : 12.ph,
        Text24And20SemiBold(
          item.title,
          fontSize: Responsive.isDesktop(context) ? 24.sp : 14.sp,
          color: colorScheme.black,
          textAlign: Responsive.isDesktop(context) ? TextAlign.start : TextAlign.center,
        ),
        Responsive.isDesktop(context) ? 14.ph : 8.ph,
        Text(
          item.description,
          style: TextStyle(
            fontFamily: 'NotoSans-Regular',
            fontWeight: FontWeight.w400,
            color: colorScheme.darkGrey525252,
            fontSize: Responsive.isDesktop(context) ? 16.sp : 12.sp,
          ),
          textAlign: Responsive.isDesktop(context)
              ? TextAlign.start
              : TextAlign.center,
        ),
      ],
    );
  }
}
