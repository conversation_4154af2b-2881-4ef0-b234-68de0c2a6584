import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../widgets/texts/app_text.dart';

class StaticAudioCard extends StatelessWidget {
  final double? width;

  const StaticAudioCard({
    super.key,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final List<String> items = ['English', 'Young Adult (18-35)', 'Friendly', 'Video Narration', 'Conversational'];
    items.removeWhere((item) => item.isEmpty);
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      width: width ?? (Responsive.isDesktop(context) ? 390.w : 225.w),
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isDesktop(context) ? 20.w : 12.w,
        vertical: Responsive.isDesktop(context) ? 10.h : 6.h,
      ).copyWith(top: Responsive.isDesktop(context) ? 26.h : 15.h),
      decoration: BoxDecoration(
        color: colorScheme.white,
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(
          color: colorScheme.lightShadowF3F3F3,
          width: Responsive.isDesktop(context) ? 1.54.w : 0.89.w,
        ),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: Responsive.isDesktop(context) ? 76.r : 44.r,
                height: Responsive.isDesktop(context) ? 76.r : 44.r,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                    color: colorScheme.lightGreyB2B2B2,
                    width: Responsive.isDesktop(context) ? 1.29.w : 0.74.w,
                  ),
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: AssetImage(AppImages.staticAudioProfile),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Responsive.isDesktop(context) ? 16.pw : 10.pw,
              Flexible(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: TextTitle18And14(
                            'Jorge Manuel O.',
                            fontSize: Responsive.isDesktop(context) ? 22.sp : 14.sp,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Responsive.isDesktop(context) ? 10.pw : 7.pw,
                        Transform.translate(
                          offset: Offset(0, -5.h),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AppImages.blueStarFilled,
                                width: Responsive.isDesktop(context) ? null : 8.r,
                                height: Responsive.isDesktop(context) ? null : 8.r,
                              ),
                              Responsive.isDesktop(context) ? 3.pw : 2.pw,
                              Text(
                                '5.0',
                                style: TextStyle(
                                  fontSize: Responsive.isDesktop(context) ? 8.sp : 7.sp,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'NotoSans-Regular',
                                  color: colorScheme.primaryGrey,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Responsive.isDesktop(context) ? 5.ph : 3.ph,
                    Wrap(
                      spacing: 2.w,
                      runSpacing: 1.h,
                      children: [
                        ...List.generate(items.isNotEmpty ? items.length * 2 - 1 : 0, (index) {
                          if (index.isEven) {
                            final item = items[index ~/ 2];
                            return Text(
                              item,
                              style: TextStyle(
                                fontSize: Responsive.isDesktop(context) ? 12.sp : 7.sp,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'NotoSans-Regular',
                                color: colorScheme.lightGrey5D5D5D,
                              ),
                            );
                          } else {
                            // Dot separator
                            return Text(
                              ' • ',
                              style: TextStyle(
                                fontSize: Responsive.isDesktop(context) ? 12.sp : 7.sp,
                                color: colorScheme.lightGrey5D5D5D,
                              ),
                            );
                          }
                        }),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          4.ph,
          Divider(color: colorScheme.lightGreyF0F0F0),
          Row(
            children: [
              Container(
                width: 32.r,
                height: 32.r,
                decoration: BoxDecoration(
                  color: colorScheme.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: colorScheme.lightGreyD9D9D9,
                    width: Responsive.isDesktop(context) ? 0.7.w : 0.5.w,
                  ),
                ),
                child: Center(
                  child: SvgPicture.asset(
                    AppImages.playIconOutlined,
                  ),
                ),
              ),
              6.pw,
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  thumbShape: RoundSliderThumbShape(
                    enabledThumbRadius: 6.r,
                  ),
                  disabledThumbColor: colorScheme.darkGrey525252,
                  thumbColor: colorScheme.darkGrey525252,
                  activeTrackColor: colorScheme.hyperlinkBlueColor.withOpacity(0.36),
                  inactiveTrackColor: colorScheme.lightGreyC8C8C8,
                  trackHeight: 5.h,
                  overlayShape: SliderComponentShape.noOverlay,
                ),
                child: Expanded(
                  child: Slider(
                    value: 0,
                    max: 1.0,
                    onChanged: (value) {
                      // _audioPlayer.seek(Duration(seconds: value.toInt()));
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
