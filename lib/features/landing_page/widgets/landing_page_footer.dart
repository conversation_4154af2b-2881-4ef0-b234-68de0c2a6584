import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';

import '../../../utils/responsive.dart';
import '../data/enums/landing_page_item_type.dart';
import '../data/model/footer_data_model.dart';
import 'desktop_footer_widget.dart';
import 'mobile_footer_widget.dart';

class LandingPageFooter extends StatelessWidget {
  final void Function(LandingPageItemType)? onSectionTap;

  const LandingPageFooter({
    super.key,
    required this.onSectionTap,
  });

  @override
  Widget build(BuildContext context) {
    final footerSections = [
      FooterSectionData(
        title: 'Discover',
        links: [
          FooterLinkData(label: 'About', onTap: () {
            onSectionTap?.call(LandingPageItemType.about);
          }),
          FooterLinkData(label: 'Explore Voices', onTap: () {
            onSectionTap?.call(LandingPageItemType.exploreVoices);
          }),
          FooterLinkData(label: 'How It Works', onTap: () {
            onSectionTap?.call(LandingPageItemType.howItWorks);
          }),
        ],
      ),
      FooterSectionData(
        title: 'Resources',
        links: [
          FooterLinkData(
              label: 'Privacy Policy',
              onTap: () {
                context.pushNamed(RouteName.privacyPolicy);
              }),
          FooterLinkData(
              label: 'Terms of Service',
              onTap: () {
                context.pushNamed(RouteName.termsAndConditions);
              }),
          FooterLinkData(
              label: 'Contact Us',
              onTap: () {
                onSectionTap?.call(LandingPageItemType.contactUs);
              }),
        ],
      ),
    ];
    if (Responsive.isDesktop(context)) {
      return DesktopFooterWidget(footerSections: footerSections);
    } else {
      return MobileFooterWidget(footerSections: footerSections);
    }
  }
}
