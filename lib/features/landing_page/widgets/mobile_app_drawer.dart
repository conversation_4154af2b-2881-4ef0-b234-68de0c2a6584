import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../data/enums/landing_page_item_type.dart';
import 'search_public_voices_text_field.dart';

class MobileAppDrawer extends StatelessWidget {
  final void Function(LandingPageItemType)? onSectionTap;
  final void Function() onTapNavigateToDashboard;

  const MobileAppDrawer({
    super.key,
    required this.onSectionTap,
    required this.onTapNavigateToDashboard,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Drawer(
      backgroundColor: colorScheme.white,
      width: ScreenUtil().screenWidth * 0.87,
      shape: const RoundedRectangleBorder(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            32.ph,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Image.asset(
                  AppImages.tvdSoundVisionLogo,
                  width: 150.w,
                ),
                InkWell(
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: colorScheme.lightShadowF3F3F3,
                        width: 0.75.h,
                      ),
                    ),
                    child: SvgPicture.asset(
                      AppImages.closeIcon,
                      width: 24.w,
                      height: 24.h,
                    ),
                  ),
                ),
              ],
            ),
            20.ph,
            Divider(color: colorScheme.lightGreyD9D9D9, height: 1.h),
            SearchPublicVoicesTextField(
              onSearchPressed: () {
                Navigator.pop(context);
                NavigationServiceImpl.getInstance()?.doNavigation(
                  context,
                  routeName: RouteName.explorePublicVoices,
                );
              },
            ),
            Divider(color: colorScheme.lightGreyD9D9D9, height: 1.h),
            24.ph,
            _DrawerItemTile(
              text: AppStrings.about,
              onTap: () {
                Navigator.pop(context);
                onSectionTap?.call(LandingPageItemType.about);
              },
            ),
            24.ph,
            _DrawerItemTile(
              text: AppStrings.exploreVoices,
              onTap: () {
                Navigator.pop(context);
                onSectionTap?.call(LandingPageItemType.exploreVoices);
              },
            ),
            24.ph,
            _DrawerItemTile(
              text: AppStrings.howItWorks,
              onTap: () {
                Navigator.pop(context);
                onSectionTap?.call(LandingPageItemType.howItWorks);
              },
            ),
            24.ph,
            Divider(color: colorScheme.lightGreyD9D9D9, height: 1.h),
            30.ph,
            Builder(
              builder: (context) {
                final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
                final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);

                if (isUserLoggedIn == true) {
                  return Center(
                    child: TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        onTapNavigateToDashboard.call();
                      },  
                      child: Text(
                        AppStrings.hello(userData?.firstName ?? ''),
                        style: textTheme.titleMedium,
                      ),
                    ),
                  );
                }
                return Row(
                  children: [
                    Expanded(
                      child: PrimaryButton(
                        height: 50.h,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        backgroundColor: colorScheme.white,
                        elevation: 2,
                        onPressed: () {
                          Navigator.pop(context);
                          NavigationServiceImpl.getInstance()?.doNavigation(
                            context,
                            routeName: RouteName.login,
                            useGo: true,
                          );
                        },
                        child: Text(
                          AppStrings.login,
                          style: textTheme.titleMedium,
                        ),
                      ),
                    ),
                    16.pw,
                    Expanded(
                      child: PrimaryButton(
                        height: 50.h,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        elevation: 2,
                        onPressed: () {
                          Navigator.pop(context);
                          NavigationServiceImpl.getInstance()?.doNavigation(
                            context,
                            routeName: RouteName.signUp,
                          );
                        },
                        child: Text(
                          AppStrings.signUp,
                          style: textTheme.titleMedium,
                        ),
                      ),
                    ),
                  ],
                );
              }
            ),
            16.ph,
          ],
        ),
      ),
    );
  }
}

class _DrawerItemTile extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const _DrawerItemTile({
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            text,
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.steelBlue25272D,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              Icons.arrow_forward_ios,
              size: 12.r,
              color: colorScheme.steelGrey6C6D6F,
            ),
          ),
        ],
      ),
    );
  }
}
