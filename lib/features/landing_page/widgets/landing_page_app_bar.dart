import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/api/api_params.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/explore_public_voices_cubit.dart';
import '../data/enums/landing_page_item_type.dart';
import 'search_public_voices_text_field.dart';

class LandingPageAppBar extends StatelessWidget {
  final void Function(LandingPageItemType)? onSectionTap;
  final bool isOnLandingPage;

  const LandingPageAppBar({
    super.key,
    required this.onSectionTap,
    this.isOnLandingPage = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: 100.h,
      decoration: BoxDecoration(
        color: colorScheme.white,
        boxShadow: [
          BoxShadow(
            color: colorScheme.black.withOpacity(0.1),
            blurRadius: 12.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: Responsive.isDesktop(context)
            ? MainAxisAlignment.start
            : MainAxisAlignment.spaceBetween,
        children: [
          if (Responsive.isDesktop(context)) Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: 24.h,
              horizontal: Responsive.isDesktop(context) ? 0 : 16.w,
            ),
            child: Image.asset(
              AppImages.tvdSoundVisionLogo,
              width: Responsive.isDesktop(context) ? 200.w : 150.w,
              height: Responsive.isDesktop(context) ? 52.h : 40.h,
            ),
          ),
          if (Responsive.isDesktop(context)) ...[
            ..._buildDesktopNavItems(context),
            if (isOnLandingPage) ...[
              Flexible(
                flex: 4,
                child: BlocProvider(
                  create: (_) => ExplorePublicVoicesCubit(),
                  child: const SearchPublicVoicesTextField(),
                ),
              ),
            ] else ... [
              Spacer(flex: 4),
            ],
            if (HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn) != true) ...[
              ..._buildDesktopAuthActions(context),
            ] else ...[
              Spacer(),
              _buildDesktopHelloUser(context),
            ],
          ] else ...[
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: GestureDetector(
                onTap: () {
                  Scaffold.of(context).openDrawer();
                },
                child: Icon(
                  Icons.menu_rounded,
                  size: 24.w,
                  color: colorScheme.black,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildDesktopNavItems(
    BuildContext context,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return [
      Spacer(),
      InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () {
          onSectionTap?.call(LandingPageItemType.about);
        },
        child: Text(AppStrings.about, style: textTheme.titleMedium),
      ),
      42.pw,
      InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () {
          onSectionTap?.call(LandingPageItemType.exploreVoices);
        },
        child: Container(
          decoration: isOnLandingPage
              ? null
              : BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.15),
                  border: Border(
                    bottom: BorderSide(
                      color: colorScheme.primary,
                      width: 4,
                    ),
                  ),
                ),
          padding: isOnLandingPage ? null : EdgeInsets.symmetric(horizontal: 6.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppStrings.exploreVoices,
                style: isOnLandingPage
                    ? textTheme.titleMedium
                    : textTheme.displayLarge?.copyWith(
                        fontSize: 16.sp,
                      ),
              ),
            ],
          ),
        ),
      ),
      42.pw,
      InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () {
          onSectionTap?.call(LandingPageItemType.howItWorks);
        },
        child: Text(AppStrings.howItWorks, style: textTheme.titleMedium),
      ),
      32.pw,
    ];
  }

  List<Widget> _buildDesktopAuthActions(
    BuildContext context,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return [
      20.pw,
      InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () {
          NavigationServiceImpl.getInstance()?.doNavigation(
            context,
            routeName: RouteName.login,
          );
        },
        child: Text(AppStrings.logIn, style: textTheme.titleMedium),
      ),
      24.pw,
      PrimaryButton(
        height: 50.h,
        width: 132.w,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.r)),
        onPressed: () {
          NavigationServiceImpl.getInstance()?.doNavigation(
            context,
            routeName: RouteName.signUp,
          );
        },
        buttonText: AppStrings.signUp,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      Spacer(),
    ];
  }

  Widget _buildDesktopHelloUser(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      onTap: () {
        if (userData?.intermediateStep == 0) {
          NavigationServiceImpl.getInstance()!.doNavigation(
            context,
            routeName: RouteName.createProfile,
            pathParameters: {
              Params.type: userData?.role?.getString() ?? '',
            },
            useGo: true,
          );
          return;
        }

        bool isClient = userData?.role == UserType.client;
        bool isVoice = userData?.role == UserType.voice;
        bool isAncillary = userData?.role == UserType.ancillaryService;

        if ((isClient || isAncillary) && userData?.intermediateStep == 1) {
          NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
          return;
        }
        if (isVoice && userData?.intermediateStep == 1) {
          NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.vocalCharacteristics, useGo: true);
          return;
        }
        if (isVoice && userData?.intermediateStep == 2) {
          NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.uploadSamples, useGo: true);
          return;
        }
        if (isVoice && userData?.intermediateStep == 3) {
          NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.projectType, useGo: true);
          return;
        }
        if (isVoice && userData?.intermediateStep == 4) {
          NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
          return;
        }
      },
      child: Text(
        AppStrings.hello(userData?.firstName ?? ''),
        style: textTheme.titleMedium,
      ),
    );
  }
}
