import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';

class EnrollPlatformWidget extends StatelessWidget {
  final VoidCallback onNavigateToDashboard;

  const EnrollPlatformWidget({
    super.key,
    required this.onNavigateToDashboard,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.primaryGrey,
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: Opacity(
              opacity: 0.16,
              child: Image.asset(
                AppImages.waves,
                fit: BoxFit.cover,
                colorBlendMode: BlendMode.color,
                color: colorScheme.black,
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Responsive.isDesktop(context) ? 100.w : 16.w,
                vertical: Responsive.isDesktop(context) ? 107.h : 40.h,
              ),
              child: Builder(
                builder: (context) {
                  if (Responsive.isDesktop(context)) {
                    return Row(
                      children: [
                        Expanded(
                          child: _EnrollPlatformContent(onNavigateToDashboard: onNavigateToDashboard),
                        ),
                        _EnrollPlatformQrCode(),
                      ],
                    );
                  }
                  return Column(
                    children: [
                      _EnrollPlatformContent(onNavigateToDashboard: onNavigateToDashboard),
                      40.ph,
                      _EnrollPlatformQrCode(),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _EnrollPlatformContent extends StatelessWidget {
  final VoidCallback onNavigateToDashboard;
  const _EnrollPlatformContent({required this.onNavigateToDashboard});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: Responsive.isDesktop(context) ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(
            right: Responsive.isDesktop(context) ? 487.w : 0,
          ),
          child: Text(
            AppStrings.enrollToOurPlatformTitle,
            style: textTheme.displayLarge?.copyWith(
              color: colorScheme.white,
              fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
            ),
            textAlign: Responsive.isDesktop(context)
                ? TextAlign.start
                : TextAlign.center,
          ),
        ),
        Responsive.isDesktop(context) ? 26.ph : 16.ph,
        Padding(
          padding: EdgeInsets.only(
            right: Responsive.isDesktop(context) ? 437.w : 0,
          ),
          child: Text(
            AppStrings.enrollToOurPlatformDescription,
            style: TextStyle(
              fontFamily: 'NotoSans-Regular',
              fontWeight: FontWeight.w400,
              fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
              color: Responsive.isDesktop(context)
                  ? colorScheme.lightGreyD9D9D9
                  : colorScheme.white,
              height: 1.6,
            ),
            textAlign: Responsive.isDesktop(context)
                ? TextAlign.start
                : TextAlign.center,
          ),
        ),
        Responsive.isDesktop(context) ? 26.ph : 16.ph,
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(64.r),
            ),
            fixedSize: Size(144.w, 44.h),
            backgroundColor: colorScheme.hyperlinkBlueColor,
            elevation: 0,
            padding: EdgeInsets.zero,
          ),
          onPressed: () {
            final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
            if (isUserLoggedIn == true) {
              onNavigateToDashboard.call();
            } else {
              NavigationServiceImpl.getInstance()?.doNavigation(
                context,
                routeName: RouteName.signUp,
              );
            }
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppStrings.joinNow,
                style: TextStyle(
                  fontFamily: 'NotoSans-Regular',
                  fontSize: 16.sp,
                  color: colorScheme.white,
                ),
              ),
              SizedBox(width: 10.w),
              Icon(
                Icons.arrow_forward_rounded,
                size: 16.sp,
                color: colorScheme.white,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _EnrollPlatformQrCode extends StatelessWidget {
  const _EnrollPlatformQrCode();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isDesktop(context) ? 20.w : 16.w,
        vertical: Responsive.isDesktop(context) ? 20.h : 16.h,
      ),
      child: Column(
        children: [
          SvgPicture.asset(
            AppImages.qrCode,
            width: Responsive.isDesktop(context) ? 200.w : 160.w,
            height: Responsive.isDesktop(context) ? 200.h : 160.h,
          ),
          Responsive.isDesktop(context) ? 12.ph : 9.5.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.scanIcon,
                width: Responsive.isDesktop(context) ? 22.w : 18.w,
                height: Responsive.isDesktop(context) ? 22.h : 18.h,
              ),
              Text(
                AppStrings.scanToJoinInstantly,
                style: TextStyle(
                  fontFamily: 'NotoSans-Regular',
                  fontWeight: FontWeight.w400,
                  fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
                  color: colorScheme.black,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ],
      ),
    );
  }
}