import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';

class ContactUsDetails extends StatelessWidget {
  const ContactUsDetails({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final isDesktop = Responsive.isDesktop(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(right: isDesktop ? 36.w : 50.w),
          child: Text(
            AppStrings.contactUsTitle,
            style: textTheme.displayLarge?.copyWith(
              fontSize: isDesktop ? 38.sp : 28.sp,
            ),
          ),
        ),
        isDesktop ? 18.ph : 12.ph,
        Padding(
          padding: EdgeInsets.only(right: isDesktop ? 36.w : 0),
          child: Text(
            AppStrings.contactUsDescription,
            style: TextStyle(
              fontFamily: 'NotoSans-Regular',
              fontWeight: FontWeight.w400,
              fontSize: 14.sp,
              color: colorScheme.darkGrey525252,
            ),
          ),
        ),
        isDesktop ? 22.ph : 36.ph,
        Text(
          AppStrings.emailAddress,
          style: TextStyle(
            fontFamily: 'NotoSans-Regular',
            fontWeight: FontWeight.w400,
            fontSize: 14.sp,
            color: colorScheme.darkGrey525252,
          ),
        ),
        InkWell(
          onTap: () {
            launchUrl(Uri.parse('mailto:${AppStrings.supportEmail}'));
          },
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Text(
            AppStrings.supportEmail,
            style: textTheme.displayLarge?.copyWith(
              fontSize: 14.sp,
              color: colorScheme.darkGrey525252,
            ),
          ),
        ),
        isDesktop ? 18.ph : 24.ph,
        Text(
          AppStrings.phoneNumber.removeLastChar(),
          style: TextStyle(
            fontFamily: 'NotoSans-Regular',
            fontWeight: FontWeight.w400,
            fontSize: 14.sp,
            color: colorScheme.darkGrey525252,
          ),
        ),
        InkWell(
          onTap: () {
            launchUrl(Uri.parse('tel:${AppStrings.supportPhone}'));
          },
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Text(
            AppStrings.supportPhone,
            style: textTheme.displayLarge?.copyWith(
              fontSize: 14.sp,
              color: colorScheme.darkGrey525252,
            ),
          ),
        ),
        isDesktop ? 18.ph : 24.ph,
        Text(
          AppStrings.address,
          style: TextStyle(
            fontFamily: 'NotoSans-Regular',
            fontWeight: FontWeight.w400,
            fontSize: 14.sp,
            color: colorScheme.darkGrey525252,
          ),
        ),
        InkWell(
          onTap: () {
            launchUrl(Uri.parse(AppStrings.supportAddressUrl));
          },
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Text(
            AppStrings.supportAddress,
            style: textTheme.displayLarge?.copyWith(
              fontSize: 14.sp,
              color: colorScheme.darkGrey525252,
            ),
          ),
        ),
      ],
    );
  }
}
