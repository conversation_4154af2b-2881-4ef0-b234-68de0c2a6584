import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/responsive.dart';

class CustomTabSlider extends StatefulWidget {
  final Function(int) onTabSelected;

  const CustomTabSlider({
    super.key,
    required this.onTabSelected,
  });

  @override
  State<CustomTabSlider> createState() => _CustomTabSliderState();
}

class _CustomTabSliderState extends State<CustomTabSlider> {
  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    List<String> tabs = [];
    if (Responsive.isDesktop(context)) {
      tabs = const ['Voice Actors', 'Client', 'Ancillary Services'];
    } else {
      tabs = const ['Voice', 'Client', 'Ancillaries'];
    }

    return Container(
      margin: const EdgeInsets.all(16),
      height: Responsive.isDesktop(context) ? 62.h : 40.h,
      width: Responsive.isDesktop(context) ? 560.w : double.infinity,
      decoration: BoxDecoration(
        color: colorScheme.primaryGrey,
        borderRadius: BorderRadius.circular(Responsive.isDesktop(context) ? 40.r : 25.r),
      ),
      padding: EdgeInsets.all(Responsive.isDesktop(context) ? 3.5.r : 2.5.r),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final flexUnitWidth = constraints.maxWidth / tabs.length;
          return Stack(
            children: [
              AnimatedPositioned(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                left: flexUnitWidth * selectedIndex,
                top: 0,
                bottom: 0,
                child: Container(
                  width: flexUnitWidth,
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(Responsive.isDesktop(context) ? 40.r : 25.r),
                  ),
                ),
              ),
              Row(
                children: List.generate(tabs.length, (index) {
                  final isSelected = selectedIndex == index;
                  return Flexible(
                    child: InkWell(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          selectedIndex = index;
                        });
                        widget.onTabSelected(index);
                      },
                      child: Container(
                        color: Colors.transparent,
                        alignment: Alignment.center,
                        child: Text(
                          tabs[index],
                          style: TextStyle(
                            fontFamily: isSelected ? 'NotoSans-Medium' : 'NotoSans-Regular',
                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                            fontSize: 18.sp,
                            color: isSelected ? colorScheme.black : colorScheme.lightGreyD9D9D9,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ],
          );
        },
      ),
    );
  }
}
