import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

import '../../../core/api/api_params.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/explore_public_voices_cubit.dart';
import 'search_voice_card.dart';
import 'shimmer/audio_card_shimmer.dart';

class SearchPublicVoicesTextField extends StatefulWidget {
  final VoidCallback? onSearchPressed;

  const SearchPublicVoicesTextField({
    super.key,
    this.onSearchPressed,
  });

  @override
  State<SearchPublicVoicesTextField> createState() => _SearchPublicVoicesTextFieldState();
}

class _SearchPublicVoicesTextFieldState extends State<SearchPublicVoicesTextField> {
  late TextEditingController searchController;
  late FocusNode focusNode;

  bool isFocused = false;

  @override
  void initState() {
    searchController = TextEditingController();
    focusNode = FocusNode();
    super.initState();

    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        setState(() {
          isFocused = true;
        });
      } else {
        setState(() {
          isFocused = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Container(
      constraints: BoxConstraints(maxWidth: 283.w, minWidth: 100.w),
      margin: EdgeInsets.symmetric(vertical: 24.h),
      decoration: BoxDecoration(
        color: colorScheme.black.withOpacity(0.05),
        borderRadius: BorderRadius.circular(60.r),
        border: Border.all(color: Colors.transparent),
        boxShadow: isFocused
            ? [
                BoxShadow(
                  color: colorScheme.black.withOpacity(0.11),
                  blurRadius: 5.5.r,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: TypeAheadField<UserDataModel?>(
        controller: searchController,
        focusNode: focusNode,
        debounceDuration: const Duration(milliseconds: 500),
        onSelected: (user) {
          NavigationServiceImpl.getInstance()!.doNavigation(
            context,
            routeName: RouteName.voiceProfile,
            pathParameters: {
              Params.id: user?.id.toString() ?? '',
            },
          );
        },
        builder: (context, controller, focusNode) {
          return TextField(
            maxLines: 1,
            controller: controller,
            focusNode: focusNode,
            cursorColor: colorScheme.black,
            cursorHeight: 20.h,
            style: textTheme.titleSmall!.copyWith(
              color: colorScheme.black,
            ),
            onTap: () => widget.onSearchPressed?.call(),
            onChanged: (_) {
              setState(() {});
            },
            decoration: InputDecoration(
              hintText: AppStrings.search,
              hintStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'NotoSans-Regular',
                fontSize: 16.sp,
                color: colorScheme.darkGrey525252,
              ),
              alignLabelWithHint: true,
              isDense: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(60.r),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(60.r),
                borderSide: const BorderSide(color: Colors.transparent),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(60.r),
                borderSide: BorderSide(color: colorScheme.lightGreyD9D9D9),
              ),
              fillColor: !isFocused
                  ? colorScheme.black.withOpacity(0.05)
                  : colorScheme.white,
              focusColor: colorScheme.white,
              hoverColor: Colors.transparent,
              filled: true,
              contentPadding: EdgeInsets.all(14.h),
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: 14.h, vertical: 10.h).copyWith(left: 20.w),
                child: SvgPicture.asset(AppImages.searchThinIc),
              ),
              suffixIcon: Builder(builder: (context) {
                if (searchController.text.isNotEmpty) {
                  return GestureDetector(
                    onTap: () {
                      searchController.clear();
                      setState(() {});
                    },
                    child: Padding(
                      padding: EdgeInsets.all(8.0.h).copyWith(right: 16.w),
                      child: SvgPicture.asset(AppImages.closeSquareIc),
                    ),
                  );
                } else {
                  return const SizedBox.shrink();
                }
              }),
            ),
          );
        },
        suggestionsCallback: (pattern) async {
          if (pattern.trim().isEmpty) return null;
          return await context.read<ExplorePublicVoicesCubit>().searchPublicVoices(pattern);
        },
        decorationBuilder: (context, suggestionsBox) {
          return Container(
            decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.black.withOpacity(0.1),
                  blurRadius: 8.r,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            constraints: BoxConstraints(maxHeight: 512.h),
            child: suggestionsBox,
          );
        },
        itemBuilder: (context, artist) {
          if (artist == null) {
            return const SizedBox.shrink();
          }
          return SearchVoiceCard(user: artist);
        },
        loadingBuilder: (context) {
          return ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
                child: AudioCardShimmer(width: 325.w, height: 90.h),
              );
            },
          );
        },
        emptyBuilder: (context) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 80.h, horizontal: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(AppImages.emptySearchResultVoice),
                28.ph,
                Text(
                  AppStrings.noSearchResultFoundArtist(searchController.text),
                  textAlign: TextAlign.center,
                  style: textTheme.titleMedium,
                  maxLines: 5,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    focusNode.removeListener(() {});
    focusNode.dispose();
    searchController.dispose();
    super.dispose();
  }
}
