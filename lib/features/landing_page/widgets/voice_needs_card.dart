import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_strings.dart';

class VoiceNeedsCard extends StatelessWidget {
  const VoiceNeedsCard({super.key});

  @override
  Widget build(BuildContext context) {
    final availableVoices = [
      'Voice Over',
      'Promos',
      'Lip Sync Dub',
      'Podcast',
      'Audiobook',
      'Radio Show',
      'Narration',
      'Webinar',
      'Movie',
      'Documentary',
      'Audio Drama',
      'Gaming',
      'Educational Content',
      'TV Commercials',
    ];
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isDesktop(context) ? 100.w : 16.w,
        vertical: Responsive.isDesktop(context) ? 78.h : 40.h,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.needVoiceTitle,
            style: textTheme.displayLarge?.copyWith(
              fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
              color: colorScheme.darkGrey333333,
            ),
          ),
          Responsive.isDesktop(context) ? 40.ph : 30.ph,
          Wrap(
            runSpacing: 20.w,
            spacing: 20.h,
            children: availableVoices
                .map((e) => Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        border: Border.all(color: colorScheme.primary),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                      child: Text(
                        e.toUpperCase(),
                      ),
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }
}
