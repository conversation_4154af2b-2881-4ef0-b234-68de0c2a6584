import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/responsive.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../data/model/how_platform_works_model.dart';
import 'custom_tab_slider.dart';
import 'platform_work_grid_view.dart';

class HowPlatformWorks extends StatefulWidget {
  final VoidCallback onNavigateToDashboard;

  const HowPlatformWorks({
    super.key,
    required this.onNavigateToDashboard,
  });

  @override
  State<HowPlatformWorks> createState() => _HowPlatformWorksState();
}

class _HowPlatformWorksState extends State<HowPlatformWorks> {
  final ValueNotifier<int> selectedIndex = ValueNotifier(0);
  final ValueNotifier<int> previousIndex = ValueNotifier(0);

  @override
  void dispose() {
    selectedIndex.dispose();
    previousIndex.dispose();
    super.dispose();
  }

  void _onTabSelected(int index) {
    previousIndex.value = selectedIndex.value;
    selectedIndex.value = index;
  }

  final List<Widget> _pages = const [
    _VoiceTab(key: ValueKey('VoiceTab')),
    _ClientTab(key: ValueKey('ClientTab')),
    _AncillaryTab(key: ValueKey('AncillaryTab')),
  ];

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Container(
      width: double.infinity,
      color: colorScheme.lightGreyF9F9F9,
      padding: EdgeInsets.only(
        left: Responsive.isDesktop(context) ? 100.w : 16.w,
        right: Responsive.isDesktop(context) ? 100.w : 16.w,
        top: Responsive.isDesktop(context) ? 80.h : 40.h,
        bottom: Responsive.isDesktop(context) ? 58.h : 40.h,
      ),
      child: Column(
        children: [
          Text(
            AppStrings.howThePlatformWorks,
            style: textTheme.displayLarge?.copyWith(
              fontSize: Responsive.isDesktop(context) ? 38.sp : 24.sp,
              color: colorScheme.darkGrey333333,
            ),
          ),
          Responsive.isDesktop(context) ? 26.ph : 16.ph,
          Text(
            AppStrings.howThePlatformWorksDescription,
            style: TextStyle(
              fontFamily: 'NotoSans-Regular',
              fontWeight: FontWeight.w400,
              fontSize: Responsive.isDesktop(context) ? 18.sp : 14.sp,
              color: colorScheme.lightGrey5D5D5D,
            ),
            textAlign: TextAlign.center,
          ),
          Responsive.isDesktop(context) ? 40.ph : 38.ph,
          CustomTabSlider(
            onTabSelected: _onTabSelected,
          ),
          Responsive.isDesktop(context) ? 58.ph : 38.ph,
          ValueListenableBuilder<int>(
            valueListenable: selectedIndex,
            builder: (context, value, child) {
              final isGoingForward = value > previousIndex.value;
              previousIndex.value = value;

              return AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _pages[value],
                transitionBuilder: (Widget child, Animation<double> animation) {
                  final isNewChild = (child.key == _pages[value].key);
                  final Offset enterOffset, exitOffset;
                  if (isGoingForward) {
                    enterOffset = const Offset(1.0, 0.0);
                    exitOffset = const Offset(-1.0, 0.0);
                  } else {
                    enterOffset = const Offset(-1.0, 0.0);
                    exitOffset = const Offset(1.0, 0.0);
                  }

                  final slideAnimation = Tween<Offset>(
                    begin: isNewChild ? enterOffset : exitOffset,
                    end: Offset.zero,
                  ).animate(animation);

                  return SlideTransition(
                    position: slideAnimation,
                    child: child,
                  );
                },
              );
            },
          ),
          30.ph,
          PrimaryButton(
            width: 148.w,
            height: 44.h,
            padding: EdgeInsets.zero,
            buttonText: AppStrings.getStarted,
            onPressed: () {
              final isUserLoggedIn = HiveStorageHelper.getData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn);
              if (isUserLoggedIn == true) {
                widget.onNavigateToDashboard.call();
              } else {
                NavigationServiceImpl.getInstance()?.doNavigation(
                  context,
                  routeName: RouteName.signUp,
                );
              }
            },
            backgroundColor: colorScheme.hyperlinkBlueColor,
            textColor: colorScheme.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(40.r),
            ),
          )
        ],
      ),
    );
  }
}

class _VoiceTab extends StatelessWidget {
  const _VoiceTab({super.key});

  static final List<HowPlatformWorksModel> voiceWorks = [
    HowPlatformWorksModel(
      image: AppImages.createAccountHTPW,
      title: AppStrings.voiceItem1Title,
      description: AppStrings.voiceItem1Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.defineVocalHTPW,
      title: AppStrings.voiceItem2Title,
      description: AppStrings.voiceItem2Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.exploreApplyHTPW,
      title: AppStrings.voiceItem3Title,
      description: AppStrings.voiceItem3Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.getPaidHTPW,
      title: AppStrings.voiceItem4Title,
      description: AppStrings.voiceItem4Description,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return PlatformWorkGridView(items: voiceWorks);
  }
}

class _ClientTab extends StatelessWidget {
  const _ClientTab({super.key});

  static final List<HowPlatformWorksModel> clientWorks = [
    HowPlatformWorksModel(
      image: AppImages.createAccountHTPW,
      title: AppStrings.clientItem1Title,
      description: AppStrings.clientItem1Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.postJobsHTPW,
      title: AppStrings.clientItem2Title,
      description: AppStrings.clientItem2Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.shortlistHTPW,
      title: AppStrings.clientItem3Title,
      description: AppStrings.clientItem3Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.getWorkDoneHTPW,
      title: AppStrings.clientItem4Title,
      description: AppStrings.clientItem4Description,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return PlatformWorkGridView(items: clientWorks);
  }
}

class _AncillaryTab extends StatelessWidget {
  const _AncillaryTab({super.key});

  static final List<HowPlatformWorksModel> ancillaryWorks = [
    HowPlatformWorksModel(
      image: AppImages.createAccountHTPW,
      title: AppStrings.ancillaryItem1Title,
      description: AppStrings.ancillaryItem1Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.defineVocalHTPW,
      title: AppStrings.ancillaryItem2Title,
      description: AppStrings.ancillaryItem2Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.exploreApplyHTPW,
      title: AppStrings.ancillaryItem3Title,
      description: AppStrings.ancillaryItem3Description,
    ),
    HowPlatformWorksModel(
      image: AppImages.getPaidHTPW,
      title: AppStrings.ancillaryItem4Title,
      description: AppStrings.ancillaryItem4Description,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return PlatformWorkGridView(items: ancillaryWorks);
  }
}
