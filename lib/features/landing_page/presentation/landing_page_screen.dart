import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_params.dart';
import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../bloc/explore_public_voices_cubit.dart';
import '../data/enums/landing_page_item_type.dart';
import '../widgets/best_voice_widget.dart';
import '../widgets/contact_us_widget.dart';
import '../widgets/enroll_platform_widget.dart';
import '../widgets/explore_voices_widget.dart';
import '../widgets/faq_widget.dart';
import '../widgets/hire_or_get_hired_widget.dart';
import '../widgets/how_platform_works.dart';
import '../widgets/landing_page_app_bar.dart';
import '../widgets/landing_page_footer.dart';
import '../widgets/mobile_app_drawer.dart';
import '../widgets/user_testimonials.dart';
import '../widgets/voice_needs_card.dart';

class LandingPageScreen extends StatefulWidget {
  final String? scrollTo;

  const LandingPageScreen({
    super.key,
    this.scrollTo,
  });

  @override
  State<LandingPageScreen> createState() => _LandingPageScreenState();
}

class _LandingPageScreenState extends State<LandingPageScreen> {
  final ScrollController _scrollController = ScrollController();
  late final TextEditingController _drawerSearchController;

  final aboutKey = GlobalKey();
  final howItWorksKey = GlobalKey();
  final exploreVoicesKey = GlobalKey();
  final faqKey = GlobalKey();
  final testimonialsKey = GlobalKey();
  final contactUsKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _drawerSearchController = TextEditingController();

    if (widget.scrollTo != null) {
      final scrollTo = LandingPageItemType.fromString(widget.scrollTo!);
      if (scrollTo != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onSectionTap(scrollTo);
        });
      }
    }
  }

  @override
  void didUpdateWidget(covariant LandingPageScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.scrollTo != null) {
      final scrollTo = LandingPageItemType.fromString(widget.scrollTo!);
      if (scrollTo != null) {
        onSectionTap(scrollTo);
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _drawerSearchController.dispose();
    super.dispose();
  }

  void scrollToSection(GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void onSectionTap(LandingPageItemType type) {
    switch (type) {
      case LandingPageItemType.about:
        scrollToSection(aboutKey);
        break;
      case LandingPageItemType.exploreVoices:
        scrollToSection(exploreVoicesKey);
        break;
      case LandingPageItemType.howItWorks:
        scrollToSection(howItWorksKey);
        break;
      case LandingPageItemType.faq:
        scrollToSection(faqKey);
        break;
      case LandingPageItemType.testimonials:
        scrollToSection(testimonialsKey);
        break;
      case LandingPageItemType.contactUs:
        scrollToSection(contactUsKey);
        break;
      default:
        break;
    }
  }

  void navigateToDashboard() {
    final userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    if (userData?.intermediateStep == 0) {
      NavigationServiceImpl.getInstance()!.doNavigation(
        context,
        routeName: RouteName.createProfile,
        pathParameters: {
          Params.type: userData?.role?.getString() ?? '',
        },
        useGo: true,
      );
      return;
    }

    bool isClient = userData?.role == UserType.client;
    bool isVoice = userData?.role == UserType.voice;
    bool isAncillary = userData?.role == UserType.ancillaryService;

    if ((isClient || isAncillary) && userData?.intermediateStep == 1) {
      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
      return;
    }
    if (isVoice && userData?.intermediateStep == 1) {
      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.vocalCharacteristics, useGo: true);
      return;
    }
    if (isVoice && userData?.intermediateStep == 2) {
      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.uploadSamples, useGo: true);
      return;
    }
    if (isVoice && userData?.intermediateStep == 3) {
      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.projectType, useGo: true);
      return;
    }
    if (isVoice && userData?.intermediateStep == 4) {
      NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      drawer: MobileAppDrawer(
        onSectionTap: onSectionTap,
        onTapNavigateToDashboard: navigateToDashboard,
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          children: [
            LandingPageAppBar(
              onSectionTap: onSectionTap,
              isOnLandingPage: true,
            ),
            BestVoiceWidget(
              key: aboutKey,
              onNavigateToDashboard: navigateToDashboard,
              onFindVoicePressed: () {
                scrollToSection(exploreVoicesKey);
              },
            ),
            const VoiceNeedsCard(),
            BlocProvider(
              create: (context) => ExplorePublicVoicesCubit(),
              child: ExploreVoicesWidget(key: exploreVoicesKey),
            ),
            HireOrGetHiredWidget(onNavigateToDashboard: navigateToDashboard),
            EnrollPlatformWidget(onNavigateToDashboard: navigateToDashboard),
            HowPlatformWorks(key: howItWorksKey, onNavigateToDashboard: navigateToDashboard),
            FaqWidget(key: faqKey),
            UserTestimonials(key: testimonialsKey),
            ContactUsWidget(key: contactUsKey),
            LandingPageFooter(onSectionTap: onSectionTap),
          ],
        ),
      ),
    );
  }
}
