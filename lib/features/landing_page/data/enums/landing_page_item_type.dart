import '../../../../utils/string_constants/app_strings.dart';

enum LandingPageItemType {
  about(name: EnumStrings.about),
  exploreVoices(name: EnumStrings.exploreVoices),
  howItWorks(name: EnumStrings.howItWorks),
  faq(name: EnumStrings.faq),
  contactUs(name: EnumStrings.contactUs),
  testimonials(name: EnumStrings.testimonials),
  hireOrGetHired(name: EnumStrings.hireOrGetHired);

  const LandingPageItemType({required this.name});

  final String name;

  @override
  String toString() => name;

  static LandingPageItemType? fromString(String name) {
    switch (name) {
      case EnumStrings.about:
        return LandingPageItemType.about;
      case EnumStrings.exploreVoices:
        return LandingPageItemType.exploreVoices;
      case EnumStrings.howItWorks:
        return LandingPageItemType.howItWorks;
      case EnumStrings.faq:
        return LandingPageItemType.faq;
      case EnumStrings.contactUs:
        return LandingPageItemType.contactUs;
      case EnumStrings.testimonials:
        return LandingPageItemType.testimonials;
      case EnumStrings.hireOrGetHired:
        return LandingPageItemType.hireOrGetHired;
      default:
        return null;
    }
  }
}
