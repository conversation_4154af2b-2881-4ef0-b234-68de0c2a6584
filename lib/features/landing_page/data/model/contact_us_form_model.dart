class ContactUsFormModel {
  final String name;
  final String email;
  final String title;
  final String message;

  const ContactUsFormModel({
    required this.name,
    required this.email,
    required this.title,
    required this.message,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      'name': name,
      'email': email,
      'title': title,
      'message': message,
    };
  }
}
