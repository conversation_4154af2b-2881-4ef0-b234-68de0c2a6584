class PlayerStateModel {
  final String url;
  final bool isPlaying;
  final bool isLoading;
  final Duration position;
  final Duration duration;

  const PlayerStateModel({
    required this.url,
    required this.isPlaying,
    required this.isLoading,
    required this.position,
    required this.duration,
  });

  PlayerStateModel copyWith({
    String? url,
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
  }) {
    return PlayerStateModel(
      url: url ?? this.url,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      position: position ?? this.position,
      duration: duration ?? this.duration,
    );
  }
}
