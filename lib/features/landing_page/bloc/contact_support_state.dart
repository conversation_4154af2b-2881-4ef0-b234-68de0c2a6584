part of 'contact_support_cubit.dart';

sealed class ContactSupportState extends Equatable {
  const ContactSupportState();
}

class ContactSupportInitialState extends ContactSupportState {
  const ContactSupportInitialState();

  @override
  List<Object> get props => [];
}

class ContactSupportLoadingState extends ContactSupportState {
  const ContactSupportLoadingState();

  @override
  List<Object> get props => [];
}

class ContactSupportSuccessState extends ContactSupportState {

  const ContactSupportSuccessState();

  @override
  List<Object> get props => [];
}

class ContactSupportErrorState extends ContactSupportState {
  final String errorMsg;

  const ContactSupportErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
