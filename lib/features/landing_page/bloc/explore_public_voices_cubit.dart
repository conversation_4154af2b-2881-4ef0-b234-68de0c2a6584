import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../../explore_artists/data/artist_filter_selection.dart';
import '../../explore_artists/data/artist_filter_type.dart';

part 'explore_public_voices_state.dart';

class ExplorePublicVoicesCubit extends Cubit<ExplorePublicVoicesState> {
  @override
  ExplorePublicVoicesCubit() : super(ExplorePublicVoicesInitialState());

  ArtistFilterSelection filters = ArtistFilterSelection();
  String? searchQuery;

  void updateFilter(ArtistFilterType type, int selectedId) {
    switch (type) {
      case ArtistFilterType.gender:
        filters = filters.copyWith(genderIds: _updateList(filters.genderIds, selectedId));
        break;
      case ArtistFilterType.projectType:
        filters = filters.copyWith(projectTypeIds: _updateList(filters.projectTypeIds, selectedId));
        break;
      case ArtistFilterType.ageRange:
        filters = filters.copyWith(ageRangeIds: _updateList(filters.ageRangeIds, selectedId));
        break;
      case ArtistFilterType.language:
        filters = filters.copyWith(languageIds: _updateList(filters.languageIds, selectedId));
        break;
      case ArtistFilterType.accent:
        filters = filters.copyWith(accentIds: _updateList(filters.accentIds, selectedId));
        break;
      case ArtistFilterType.experience:
        filters = filters.copyWith(experienceIds: _updateList(filters.experienceIds, selectedId));
        break;
      case ArtistFilterType.services:
        filters = filters.copyWith(servicesIds: _updateList(filters.servicesIds, selectedId));
        break;
    }
    fetchArtists();
  }

  List<int> _updateList(List<int>? currentList, int id) {
    List<int> newList = List.from(currentList ?? []);
    if (newList.contains(id)) {
      newList.remove(id);
    } else {
      newList.add(id);
    }
    return newList;
  }

  void updateSearchQuery(String? query) {
    searchQuery = query;
    fetchArtists();
  }

  Future<List<UserDataModel>> searchPublicVoices(String query) async {
    try {
      final response = await ApiService.instance.explorePublicVoices(searchName: query);

      if (response.success) {
        final List<UserDataModel> artistsList = (response.data as List)
            .map((e) => UserDataModel.fromJson(e))
            .toList();

        emit(ExplorePublicVoicesSuccessState(artistsList: artistsList));
        return artistsList;
      } else {
        emit(ExplorePublicVoicesErrorState(response.message ?? AppStrings.genericErrorMsg));
        return [];
      }
    } catch (e) {
      emit(ExplorePublicVoicesErrorState(AppStrings.genericErrorMsg));
      return [];
    }
  }

  void resetFilters() {
    if (filters.isAnyFilterApplied) {
      filters = ArtistFilterSelection();
      fetchArtists();
    }
  }

  void initFetchArtists() {
    searchQuery = null;
    filters = ArtistFilterSelection();
    fetchArtists();
  }

  Future<void> fetchArtists() async {
    if (state is! ExplorePublicVoicesInitialState) {
      emit(ExplorePublicVoicesLoadingState());
    }

    try {
      final response = await ApiService.instance.explorePublicVoices(
        searchName: searchQuery,
        filters: filters,
      );

      if (response.success) {
        final List<UserDataModel> artistsList = (response.data as List)
            .map((e) => UserDataModel.fromJson(e))
            .toList();

        emit(ExplorePublicVoicesSuccessState(artistsList: artistsList));
      } else {
        emit(ExplorePublicVoicesErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ExplorePublicVoicesErrorState(AppStrings.genericErrorMsg));
    }
  }

  Future<void> fetchTopPublicVoices() async {
    emit(ExplorePublicVoicesLoadingState());

    try {
      final response = await ApiService.instance.topPublicVoices();

      if (response.success) {
        final List<UserDataModel> artistsList = (response.data as List)
            .map((e) => UserDataModel.fromJson(e))
            .toList();

        emit(ExplorePublicVoicesSuccessState(artistsList: artistsList));
      } else {
        emit(ExplorePublicVoicesErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ExplorePublicVoicesErrorState(AppStrings.genericErrorMsg));
    }
  }
}
