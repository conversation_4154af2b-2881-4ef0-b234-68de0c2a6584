import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/contact_us_form_model.dart';

part 'contact_support_state.dart';

class ContactSupportCubit extends Cubit<ContactSupportState> {
  @override
  ContactSupportCubit() : super(ContactSupportInitialState());

  Future<void> sendMessage(ContactUsFormModel contactUsFormModel) async {
    emit(ContactSupportLoadingState());

    try {
      final response = await ApiService.instance.contactSupport(contactUsFormModel: contactUsFormModel);

      if (response.success) {
        emit(ContactSupportSuccessState());
      } else {
        emit(ContactSupportErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ContactSupportErrorState(AppStrings.genericErrorMsg));
    }
  }
}
