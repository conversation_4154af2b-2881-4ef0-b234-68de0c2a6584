import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';

part 'audio_player_state.dart';

class AudioPlayerCubit extends Cubit<AudioPlayerState> {
  AudioPlayerCubit() : super(const AudioPlayerState());

  void addPlayer(AudioPlayer player) {
    if (state.players.contains(player)) return;

    if (state.players.isEmpty) {
      emit(state.copyWith(players: [player]));
      return;
    }

    emit(state.copyWith(players: [...state.players, player]));
  }

  Future<void> play(AudioPlayer player) async {
    for (var player in state.players) {
      await player.pause();
    }
    await player.play();
  }

  Future<void> pause(AudioPlayer player) async {
    await player.pause();
  }

  @override
  Future<void> close() async {
    for (var player in state.players) {
      await player.dispose();
    }
    return super.close();
  }
}
