part of 'explore_public_voices_cubit.dart';

sealed class ExplorePublicVoicesState extends Equatable {
  const ExplorePublicVoicesState();
}

class ExplorePublicVoicesInitialState extends ExplorePublicVoicesState {
  @override
  List<Object> get props => [];
}

class ExplorePublicVoicesLoadingState extends ExplorePublicVoicesState {
  @override
  List<Object> get props => [];
}

class ExplorePublicVoicesSuccessState extends ExplorePublicVoicesState {
  final List<UserDataModel> artistsList;

  const ExplorePublicVoicesSuccessState({
    required this.artistsList,
  });

  @override
  List<Object> get props => [artistsList];
}

class ExplorePublicVoicesErrorState extends ExplorePublicVoicesState {
  final String errorMsg;
  const ExplorePublicVoicesErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
