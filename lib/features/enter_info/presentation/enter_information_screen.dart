import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_state.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/enter_info/bloc/enter_info_bloc.dart';
import 'package:the_voice_directory_flutter/features/enter_info/bloc/enter_info_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/media_info_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/email_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/mobile_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/pick_image.dart';

class EnterInformationScreen extends StatefulWidget {
  final String? email;
  const EnterInformationScreen({super.key, this.email});

  @override
  State<EnterInformationScreen> createState() => _EnterInformationScreenState();
}

class _EnterInformationScreenState extends State<EnterInformationScreen> {
  late TextEditingController _emailController;
  late TextEditingController _fNameController;
  late TextEditingController _lNameController;
  late TextEditingController _phoneNoController;
  MediaInfoModel? _userImage;
  int role = 0;
  final _formKey = GlobalKey<FormState>();
  String? countryAbbr;
  String? countryCode;
  String? countryName;

  bool isSocialImageAvailable = false;
  UserDataModel? userDataModel;

  @override
  void initState() {
    CountryCode country = CountryCode(
      code: "IN",
      dialCode: '91',
      name: 'India',
    );
    countryAbbr = country.code;
    countryCode = country.dialCode;
    countryName = country.name;
    _emailController = TextEditingController();
    _fNameController = TextEditingController();
    _lNameController = TextEditingController();
    _phoneNoController = TextEditingController();
    _emailController.text = widget.email ?? "";
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      userDataModel = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
      if (userDataModel != null) {
        _fNameController.text = userDataModel?.firstName ?? "";
        _lNameController.text = userDataModel?.lastName ?? "";
        if (userDataModel?.profilePic != null && Uri.parse(userDataModel!.profilePic!).isAbsolute) {
          isSocialImageAvailable = true;
        }
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneNoController.dispose();
    _fNameController.dispose();
    _lNameController.dispose();
    super.dispose();
  }

  pickImage() async {
    ImagePickerService imagePickerService = ImagePickerService();
    final pickedImage = await imagePickerService.pickImageWithOptions(
      context: context,
    );
    if (pickedImage != null) {
      setState(() {
        _userImage = pickedImage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: Row(
          children: [
            if (Responsive.isDesktop(context))
              const Expanded(child: TVDBrandingScreen()),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: !Responsive.isDesktop(context)
                      ? EdgeInsets.zero
                      : EdgeInsets.all(44.0.h),
                  child: Card(
                    color: Theme.of(context).colorScheme.white,
                    elevation: !Responsive.isDesktop(context) ? 0 : 8,
                    shadowColor: Theme.of(context).colorScheme.lightGreyD9D9D9,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal:
                              !Responsive.isDesktop(context) ? 16.h : 52.h,
                          vertical:
                              !Responsive.isDesktop(context) ? 24.h : 48.h),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: !Responsive.isDesktop(context)
                              ? CrossAxisAlignment.start
                              : CrossAxisAlignment.center,
                          children: [
                            32.ph,
                            const TextDisplayLarge36And26(
                                AppStrings.enterInformation),
                            !Responsive.isDesktop(context) ? 16.ph : 20.ph,
                            const TextTitle14(AppStrings
                                .pleaseFillBelowInformationToCreateYourProfile),
                            !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                            Column(
                              children: [
                                Center(
                                  child: SizedBox(
                                    width: 80.h,
                                    height: 80.h,
                                    child: Stack(
                                      children: [
                                        CircleAvatar(
                                          radius: 60.r,
                                          backgroundColor:
                                              colorScheme.blackE8E8E8,
                                          backgroundImage: _userImage != null
                                              ? MemoryImage(_userImage!.bytes)
                                              : isSocialImageAvailable
                                                  ? NetworkImage(userDataModel!.profilePic!)
                                                  : null,
                                          child: _userImage != null
                                              ? null
                                              : isSocialImageAvailable
                                                  ? null
                                                  : SvgPicture.asset("assets/images/user_icon.svg"),
                                        ),
                                        if (!kIsWeb)
                                          Positioned(
                                            top: 0,
                                            right: 0,
                                            child: Container(
                                              height: 32.h,
                                              width: 32.h,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color:
                                                      colorScheme.blackE8E8E8,
                                                  width: 2.0,
                                                ),
                                              ),
                                              child: CircleAvatar(
                                                backgroundColor:
                                                    colorScheme.white,
                                                child: IconButton(
                                                  iconSize: 18,
                                                  padding: EdgeInsets.zero,
                                                  icon: SvgPicture.asset(
                                                      AppImages.cameraIcon),
                                                  onPressed: () {
                                                    pickImage();
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                                16.ph,
                                if (kIsWeb)
                                  InkWell(
                                    onTap: () => pickImage(),
                                    child: TextTitle18And14(
                                      AppStrings.browseImage,
                                      style:
                                          theme.textTheme.titleMedium!.copyWith(
                                        color: colorScheme.secondary,
                                        decoration: TextDecoration.underline,
                                        decorationColor: colorScheme.secondary,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 40.ph,
                            AppTextFormField(
                              controller: _fNameController,
                              titleText: AppStrings.fistName,
                              hintText: AppStrings.enterFirstName,
                              validator: (str) {
                                return Validator.firstNameValidator(
                                    str!.toLowerCase());
                              },
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(50),
                                FilteringTextInputFormatter.allow(
                                    RegExp(r'[a-zA-Z\s]'))
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            AppTextFormField(
                              controller: _lNameController,
                              titleText: AppStrings.lastName,
                              hintText: AppStrings.enterLastName,
                              validator: (str) {
                                return Validator.lastNameValidator(
                                    str!.toLowerCase());
                              },
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(50),
                                FilteringTextInputFormatter.allow(
                                    RegExp(r'[a-zA-Z\s]')),
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            EmailTextField(
                              controller: _emailController,
                              readonly: widget.email != null ||
                                  widget.email!.isNotEmpty,
                              filled: widget.email != null ||
                                  widget.email!.isNotEmpty,
                              fillColor: widget.email != null ||
                                      widget.email!.isNotEmpty
                                  ? colorScheme.lightGreyD9D9D9
                                  : colorScheme.white,
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            MobileTextfield(
                              controller: _phoneNoController,
                              onCountryChanged: (country) {
                                countryAbbr = country.code;
                                countryCode = country.dialCode;
                                countryName = country.name;
                                setState(() {});
                              },
                            ),
                            !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                            const Align(
                              alignment: Alignment.centerLeft,
                              child: TextTitle14(
                                AppStrings.selectRole,
                              ),
                            ),
                            !Responsive.isDesktop(context) ? 4.ph : 8.ph,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          fixedSize: Size(
                                              double.infinity,
                                              !Responsive.isDesktop(context)
                                                  ? 44.h
                                                  : 56.h),
                                          backgroundColor: role == 1
                                              ? colorScheme.secondary
                                              : colorScheme.white,
                                          foregroundColor: role == 1
                                              ? colorScheme.white
                                              : colorScheme.secondary,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12.r),
                                            side: BorderSide(
                                              color: role == 1
                                                  ? colorScheme.secondary
                                                  : colorScheme.lightGreyD9D9D9,
                                              width: 1.5.h,
                                            ),
                                          ),
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            role = 1;
                                          });
                                        },
                                        child: TextTitle14(
                                          AppStrings.client,
                                          color: role == 1
                                              ? colorScheme.white
                                              : colorScheme.secondary,
                                        ),
                                      ),
                                    ),
                                    !Responsive.isDesktop(context)
                                        ? 19.pw
                                        : 12.pw,
                                    Expanded(
                                      child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            fixedSize: Size(
                                                double.infinity,
                                                !Responsive.isDesktop(context)
                                                    ? 44.h
                                                    : 56.h),
                                            backgroundColor: role == 2
                                                ? colorScheme.secondary
                                                : colorScheme.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              side: BorderSide(
                                                color: role == 2
                                                    ? colorScheme.secondary
                                                    : colorScheme.lightGreyD9D9D9,
                                                width: 1.5.h,
                                              ),
                                            ),
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              role = 2;
                                            });
                                          },
                                          child: TextTitle14(
                                            AppStrings.voice,
                                            color: role == 2
                                                ? colorScheme.white
                                                : colorScheme.secondary,
                                          )),
                                    ),
                                  ],
                                ),
                                !Responsive.isDesktop(context) ? 12.ph : 24.ph,
                                 Row(
                                   children: [
                                     Expanded(
                                       child: ElevatedButton(
                                           style: ElevatedButton.styleFrom(
                                             fixedSize: Size(
                                                 double.infinity,
                                                !Responsive.isDesktop(context)
                                                     ? 44.h
                                                     : 56.h),
                                             backgroundColor: role == 3
                                                 ? colorScheme.secondary
                                                 : colorScheme.white,
                                             shape: RoundedRectangleBorder(
                                               borderRadius:
                                                   BorderRadius.circular(12),
                                               side: BorderSide(
                                                 color: role == 3
                                                     ? colorScheme.secondary
                                                     : colorScheme.lightGreyD9D9D9,
                                                 width: 1.5.h,
                                               ),
                                             ),
                                           ),
                                           onPressed: () {
                                             setState(() {
                                               role = 3;
                                             });
                                           },
                                           child: TextTitle14(
                                             AppStrings.ancillaryServices,
                                             color: role == 3
                                                 ? colorScheme.white
                                                 : colorScheme.secondary,
                                           )),
                                     ),
                                    !Responsive.isDesktop(context)
                                        ? 19.pw
                                        : 12.pw,
                                     Expanded(child: SizedBox()),
                                   ],
                                 ),
                              ],
                            ),
                            !Responsive.isDesktop(context) ? 24.ph : 44.ph,
                            MultiBlocListener(
                              listeners: [
                                BlocListener<EnterInfoBloc, EnterInfoState>(
                                  listener: (context, state) {
                                    if (state is EnterInfoLoadingState) {
                                      Dialogs.showOnlyLoader(context);
                                    }
                                    if (state is EnterInfoSuccessState) {
                                      // Show Success Message
                                      CustomToast.show(
                                          context: context,
                                          message: AppStrings.otpSentOnPhone,
                                          isSuccess: true);
                                      // Navigate to OTP Screen
                                      context.pop();
                                      NavigationServiceImpl.getInstance()!
                                          .doNavigation(context,
                                              routeName: RouteName.verifyOtp,
                                              pathParameters: {
                                            Params.email: "ND",
                                            Params.phoneNumber:
                                                _phoneNoController.text,
                                          });
                                    }
                                    if (state is EnterInfoErrorState) {
                                      // Show Error Message
                                      context.pop();
                                      CustomToast.show(
                                          context: context,
                                          message: state.errorMsg,
                                          isSuccess: true);
                                    }
                                  },
                                ),
                                BlocListener<UploadMediaBloc, UploadMediaState>(
                                  listener: (context, state) {
                                    if (state is UploadMediaLoadingState) {
                                      Dialogs.showOnlyLoader(context);
                                    }
                                    if (state is UploadMediaSuccessState) {
                                      context.pop();
                                      context.read<EnterInfoBloc>().submitEnterInfo(
                                          userInfoRequestModel:
                                              UserInfoRequestModel(
                                                  profilePic:
                                                      state
                                                          .getPreSignedUrlModel
                                                          ?.presignedUrls
                                                          ?.profilePicture?[0]
                                                          .path,
                                                  countryAbbr: countryAbbr,
                                                  countryCode: countryCode,
                                                  countryName: countryName,
                                                  email:
                                                      _emailController
                                                          .text
                                                          .trim()
                                                          .toLowerCase(),
                                                  firstName: Validator.formatName(_fNameController.text),
                                                  lastName: Validator.formatName(_lNameController.text),
                                                  phoneNumber:
                                                      _phoneNoController.text
                                                          .trim(),
                                                  role: switch (role) {
                                                    1 => UserType.client.getString(),
                                                    2 => UserType.voice.getString(),
                                                    3 => UserType.ancillaryService.getString(),
                                                    _ => UserType.unknown.getString(), // Default case
                                                  },
                                              ),
                                          );
                                      return;
                                    }
                                    if (state is UploadMediaErrorState) {
                                      // Show Error Message
                                      context.pop();
                                      CustomToast.show(
                                          context: context,
                                          message: state.errorMsg,
                                          isSuccess: true);
                                    }
                                  },
                                ),
                              ],
                              child: PrimaryButton(
                                onPressed: () {
                                  if (_userImage == null && !isSocialImageAvailable) {
                                    CustomToast.show(
                                        context: context,
                                        message: ValidationMsg.profilePic);
                                    return;
                                  }
                                  if (role == 0) {
                                    CustomToast.show(
                                        context: context,
                                        message: ValidationMsg.selectRole);
                                    return;
                                  }
                                  if (_formKey.currentState!.validate()) {
                                    if (isSocialImageAvailable) {
                                      context.read<EnterInfoBloc>().submitEnterInfo(
                                          userInfoRequestModel:
                                              UserInfoRequestModel(
                                                countryAbbr: countryAbbr,
                                                countryCode: countryCode,
                                                countryName: countryName,
                                                email: _emailController.text.trim().toLowerCase(),
                                                firstName: Validator.formatName(_fNameController.text.trim()),
                                                lastName: Validator.formatName(_lNameController.text.trim()),
                                                phoneNumber: _phoneNoController.text.trim(),
                                                role: switch (role) {
                                                  1 => UserType.client.getString(),
                                                  2 => UserType.voice.getString(),
                                                  3 => UserType.ancillaryService.getString(),
                                                  _ => UserType.unknown.getString(), // Default case
                                                },
                                              ),
                                          );
                                      return;
                                    }
                                    context
                                        .read<UploadMediaBloc>()
                                        .getPreSignedUrl(
                                          contentType: "image/${_userImage?.ext}",
                                            preSignedUrlReqModel:
                                                PreSignedUrlReqModel(
                                                    profilePicture: [
                                                  MediaDetails(
                                                    extn: _userImage?.ext,
                                                    fileName: _userImage?.name,
                                                    fileType: "image",
                                                  )
                                                ]),
                                            profilePhoto: _userImage?.bytes);
                                  }
                                },
                                buttonText: AppStrings.continues,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
