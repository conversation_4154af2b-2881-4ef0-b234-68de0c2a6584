import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/login/bloc/login_bloc.dart';
import 'package:the_voice_directory_flutter/features/login/bloc/login_state.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/apple_signin_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/google_signin_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/email_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../../../../core/services/hive/hive_keys.dart';
import '../../../../core/services/hive/hive_storage_helper.dart';
import '../../../common/social_sign_in/bloc/social_login_bloc.dart';
import '../../../common/user_data/data/user_data_model.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  late TextEditingController _emailController;
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController();
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      body: SafeArea(
          child: Row(
        children: [
          if (Responsive.isDesktop(context))
            const Expanded(child: TVDBrandingScreen()),
          Expanded(
            child: Padding(
              padding: !Responsive.isDesktop(context)
                  ? EdgeInsets.zero
                  : EdgeInsets.all(44.0.h),
              child: Card(
                color: colorScheme.white,
                elevation: !Responsive.isDesktop(context) ? 0 : 8,
                shadowColor: colorScheme.lightGreyD9D9D9,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: !Responsive.isDesktop(context) ? 16.h : 52.h,
                      vertical: !Responsive.isDesktop(context) ? 24.h : 48.h),
                  child: Form(
                    key: formKey,
                    child: Column(
                      crossAxisAlignment: !Responsive.isDesktop(context)
                          ? CrossAxisAlignment.start
                          : CrossAxisAlignment.center,
                      children: [
                        const TextDisplayLarge36And26(AppStrings.login),
                        16.ph,
                        const TextTitle14(AppStrings.loginToContinue),
                        !Responsive.isDesktop(context) ? 24.ph : 52.ph,
                        EmailTextField(
                          controller: _emailController,
                        ),
                        !Responsive.isDesktop(context) ? 24.ph : 36.ph,
                      
                        BlocListener<LoginBloc, LoginState>(
                            listener: (context, state) {
                              if (state is LoginLoadingState) {
                                Dialogs.showOnlyLoader(context);
                              }
                              if (state is LoginSuccessState) {
                                // Show Success Message
                                CustomToast.show(
                                    context: context,
                                    message: AppStrings.otpSentOnEmail,
                                    isSuccess: true);
                                // Navigate to OTP Screen
                                context.pop();
                                  NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.verifyOtp,
                                    pathParameters: {
                                      Params.email:
                                          _emailController.text.trim().toLowerCase().toLowerCase(),
                                      Params.phoneNumber: "ND",
                                    });
                              }
                              if (state is LoginErrorState) {
                                // Show Error Message
                                context.pop();
                                CustomToast.show(
                                    context: context,
                                    message: state.errorMsg,
                                    isSuccess: false);
                              }
                            },
                            child: PrimaryButton(
                                onPressed: () {
                                  if (formKey.currentState!.validate()) {
                                    context.read<LoginBloc>().login(
                                        email: _emailController.text.trim().toLowerCase());
                                  }
                                },
                                buttonText: AppStrings.continueTxt)),
                     
                        !Responsive.isDesktop(context) ? 24.ph : 32.ph,
                        BlocConsumer<SocialLoginBloc, SocialLoginState>(
                          listener: (context, state) {
                            if (state is SocialLoginLoading) {
                              Dialogs.showOnlyLoader(context);
                            }
                            if (state is SocialLoginSuccess) {
                              context.pop();
                              if (!(state.userDataModel.isPhoneVerified ?? true)) {
                                NavigationServiceImpl.getInstance()!.doNavigation(
                                  context,
                                  routeName: RouteName.enterInformation,
                                  pathParameters: {
                                    Params.email: state.userDataModel.email ?? '',
                                  },
                                  useGo: true,
                                );
                                return;
                              }
                              bool isClient = state.userDataModel.role == UserType.client;
                              bool isVoice = state.userDataModel.role == UserType.voice;
                              bool isAncillary = state.userDataModel.role == UserType.ancillaryService;
                              if (state.userDataModel.intermediateStep == 0) {
                                NavigationServiceImpl.getInstance()!.doNavigation(
                                  context,
                                  routeName: RouteName.createProfile,
                                  pathParameters: {
                                    Params.type: isClient
                                        ? UserType.client.getString()
                                        : isAncillary ? UserType.ancillaryService.getString() : UserType.voice.getString(),
                                  },
                                  useGo: true,
                                );
                                return;
                              }
                              if (isClient && state.userDataModel.intermediateStep == 1) {
                                HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 1) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.vocalCharacteristics, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 2) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.uploadSamples, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 3) {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.projectType, useGo: true);
                                return;
                              }
                              if (isVoice && state.userDataModel.intermediateStep == 4) {
                                HiveStorageHelper.saveData<bool>(HiveBoxName.user, HiveKeys.isUserLoggedIn, true);
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.dashboard, useGo: true);
                                return;
                              }
                            }
                            if (state is SocialLoginError) {
                              context.pop();
                              CustomToast.show(
                                context: context,
                                message: state.errorMessage,
                                isSuccess: false,
                              );
                            }
                            if (state is SocialLoginInitial) {
                              context.pop();
                            }
                          },
                          builder: (context, state) {
                            return GoogleSignInButton(
                              buttonText: AppStrings.continueWithGoogle,
                              onPressed: () {
                                context.read<SocialLoginBloc>().signInWithGoogle();
                              },
                            );
                          },
                        ),
                        if (!kIsWeb && Platform.isIOS)...[
                          24.ph,
                          AppleSignInButton(
                            buttonText: AppStrings.continueWithApple,
                            onPressed: () {
                              context.read<SocialLoginBloc>().signInWithApple();
                            },
                          ),
                        ],
                        24.ph,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const TextTitle18And14(AppStrings.dontHaveAcc),
                            8.pw,
                            InkWell(
                              onTap: () {
                                NavigationServiceImpl.getInstance()!.doNavigation(context, routeName: RouteName.signUp,);
                              },
                              child: TextTitle18And14(
                                AppStrings.signUp,
                                color: colorScheme.secondary,
                              ),
                            ),
                          ],
                        ),
                        if (!kIsWeb) ...[
                          24.ph,
                          Center(
                            child: GestureDetector(
                              onTap: () {
                                NavigationServiceImpl.getInstance()?.doNavigation(
                                  context,
                                  routeName: RouteName.explorePublicVoices,
                                );
                              },
                              child: Text(
                                AppStrings.continueAsGuest,
                                style: textTheme.titleMedium?.copyWith(
                                  color: colorScheme.hyperlinkBlueColor,
                                  decoration: TextDecoration.underline,
                                  decorationColor: colorScheme.hyperlinkBlueColor,
                                  decorationThickness: 1,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      )),
    );
  }
}
