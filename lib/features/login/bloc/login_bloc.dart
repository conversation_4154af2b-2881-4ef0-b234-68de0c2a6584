import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/login/bloc/login_state.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class LoginBloc extends Cubit<LoginState> {
  @override
  LoginBloc() : super(LoginInitState());

  Future<void> login({required String email}) async {
  emit(LoginLoadingState());
    
  try {
  final response = await ApiService.instance.login(email: email);
    if (response.success) {
      emit(LoginSuccessState(response.message ?? ""));
    } else {
      emit(LoginErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  } catch (e) {
    emit(LoginErrorState(AppStrings.genericErrorMsg));
  }
}
}
