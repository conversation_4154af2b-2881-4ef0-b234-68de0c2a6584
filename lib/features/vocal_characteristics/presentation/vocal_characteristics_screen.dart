import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_state.dart';
import 'package:the_voice_directory_flutter/features/vocal_characteristics/bloc/vocal_char_bloc.dart';
import 'package:the_voice_directory_flutter/features/vocal_characteristics/bloc/vocal_char_state.dart';
import 'package:the_voice_directory_flutter/utils/common_models/dropdown_data_menu_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/chips.dart';
import 'package:the_voice_directory_flutter/widgets/custom_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/custom_multiselect_dropdown.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/widgets/tvd_branding_screen.dart';

import '../../common/user_data/data/user_data_model.dart';

class VocalCharacteristicScreen extends StatefulWidget {
  const VocalCharacteristicScreen({super.key});

  @override
  State<VocalCharacteristicScreen> createState() =>
      _VocalCharacteristicScreenState();
}

class _VocalCharacteristicScreenState extends State<VocalCharacteristicScreen> {
  List<DropdownData> selectedVoiceGender = [];
  List<DropdownData> selectedVoiceType = [];
  List<DropdownData> selectedVoiceChar = [];
  List<AgeRange> selectedVoiceAge = [];
  List<DropdownData> selectedLanguages = [];
  List<DropdownData> selectedAccent = [];
  DropdownData? selectedExperience;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: Row(
          children: [
            if (Responsive.isDesktop(context))
              const Expanded(child: TVDBrandingScreen()),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: !Responsive.isDesktop(context)
                      ? EdgeInsets.zero
                      : EdgeInsets.all(44.0.h),
                  child: Card(
                    color: colorScheme.white,
                    elevation: !Responsive.isDesktop(context) ? 0 : 8,
                    shadowColor: colorScheme.lightGreyD9D9D9,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal:
                            !Responsive.isDesktop(context) ? 16.h : 52.h,
                        vertical: !Responsive.isDesktop(context) ? 16.h : 48.h,
                      ),
                      child: Column(
                        crossAxisAlignment: !Responsive.isDesktop(context)
                            ? CrossAxisAlignment.start
                            : CrossAxisAlignment.center,
                        children: [

                          if (!Responsive.isDesktop(context)) ...[
                            const Align(
                                alignment: Alignment.centerLeft,
                                child: CustomBackButton()),
                            16.ph,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const TextDisplayLarge36And26(
                                    AppStrings.vocalCharacteristics),
                                RichText(
                                    text: TextSpan(
                                        text: '02',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(fontSize: 16.sp),
                                        children: [
                                      TextSpan(
                                        text: '/05',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleSmall!
                                            .copyWith(
                                                fontWeight: FontWeight.w400),
                                      )
                                    ])),
                              ],
                            ),
                          ],
                          if (Responsive.isDesktop(context)) ...[
                            Align(
                                alignment: Alignment.centerRight,
                              child: RichText(
                                  text: TextSpan(
                                      text: '02',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyLarge!
                                          .copyWith(fontSize: 20.sp),
                                      children: [
                                    TextSpan(
                                      text: '/05',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyLarge!
                                          .copyWith(fontSize: 14.sp),
                                    )
                                  ])),
                            ),
                            const TextDisplayLarge36And26(
                                AppStrings.vocalCharacteristics),
                          ],
                          !Responsive.isDesktop(context) ? 16.ph : 52.ph,

                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<DropdownData>? gender = [];
                              if (state is StaticDataDropdownSuccessState) {
                                gender.addAll(
                                    state.dropDownResponseModel?.gender ?? []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                titleText: AppStrings.voiceGender,
                                hintText: AppStrings.selectVoiceGender,
                                items: gender,
                                selectedValues: selectedVoiceGender,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedVoiceGender = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedVoiceGender.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedVoiceGender,
                              onRemove: (item) {
                                selectedVoiceGender.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<DropdownData>? voiceType = [];
                              if (state is StaticDataDropdownSuccessState) {
                                voiceType.addAll(
                                    state.dropDownResponseModel?.voiceType ??
                                        []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                titleText: AppStrings.voiceType,
                                hintText: AppStrings.selectVoiceType,
                                items: voiceType,
                                selectedValues: selectedVoiceType,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedVoiceType = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedVoiceType.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedVoiceType,
                              onRemove: (item) {
                                selectedVoiceType.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<DropdownData>? voiceCharacter = [];
                              if (state is StaticDataDropdownSuccessState) {
                                voiceCharacter.addAll(state
                                        .dropDownResponseModel
                                        ?.voiceCharacter ??
                                    []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                titleText: AppStrings.voiceCharacter,
                                hintText: AppStrings.selectVoiceCharacter,
                                items: voiceCharacter,
                                selectedValues: selectedVoiceChar,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedVoiceChar = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedVoiceChar.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedVoiceChar,
                              onRemove: (item) {
                                selectedVoiceChar.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                                BlocBuilder<StaticDataDropdownBloc,
                                    StaticDataDropdownState>(
                                  builder: (context, state) {
                                    List<DropdownData>? experience = [];
                                    if (state
                                        is StaticDataDropdownSuccessState) {
                                      experience.addAll(
                                          state.dropDownResponseModel?.experience ??
                                              []);
                                    }
                                    return CustomDropDownWidget<DropdownData>(
                                        isLoading: state
                                            is StaticDataDropdownLoadingState,
                                        isError: state
                                            is StaticDataDropdownErrorState,
                                        hintText: AppStrings.selectExperience,
                                        titleText: AppStrings.experience,
                                        selectedValue: selectedExperience?.name,
                                        items: experience
                                            .map((item) => DropdownMenuItem(
                                                value: item,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    TextTitle18And14(
                                                      item.name ?? "",
                                                    ),
                                                    if (item.id ==
                                                        selectedExperience?.id)
                                                      const Icon(Icons.check),
                                                  ],
                                                )))
                                            .toList(),
                                        onChanged: (value) {
                                          selectedExperience = value;
                                          setState(() {});
                                        },
                                        value: selectedExperience);
                                  },
                                ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<AgeRange>? ageRange = [];
                              if (state is StaticDataDropdownSuccessState) {
                                ageRange.addAll(
                                    state.dropDownResponseModel?.ageRange ??
                                        []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                titleText: AppStrings.voiceAge,
                                hintText: AppStrings.selectVoiceAge,
                                items: ageRange,
                                selectedValues: selectedVoiceAge,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedVoiceAge = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedVoiceAge.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedVoiceAge,
                              onRemove: (item) {
                                selectedVoiceAge.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<DropdownData>? voiceLanguage = [];
                              if (state is StaticDataDropdownSuccessState) {
                                voiceLanguage.addAll(state
                                        .dropDownResponseModel?.voiceLanguage ??
                                    []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                hintText: AppStrings.selectLanguage,
                                titleText: AppStrings.language,
                                items: voiceLanguage,
                                selectedValues: selectedLanguages,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedLanguages = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedLanguages.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedLanguages,
                              onRemove: (item) {
                                selectedLanguages.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          !Responsive.isDesktop(context) ? 16.ph : 24.ph,
                          BlocBuilder<StaticDataDropdownBloc,
                              StaticDataDropdownState>(
                            builder: (context, state) {
                              List<DropdownData>? accent = [];
                              if (state is StaticDataDropdownSuccessState) {
                                accent.addAll(state
                                        .dropDownResponseModel?.accent ??
                                    []);
                              }
                              return CustomMultiselectDropdown(
                                isLoading:
                                    state is StaticDataDropdownLoadingState,
                                isError: state is StaticDataDropdownErrorState,
                                hintText: AppStrings.selectAccent,
                                titleText: AppStrings.accent,
                                items: accent,
                                selectedValues: selectedAccent,
                                onSelectionChanged: (newSelection) {
                                  setState(() {
                                    selectedAccent = newSelection;
                                  });
                                },
                              );
                            },
                          ),
                          if (selectedAccent.isNotEmpty) 16.ph,
                          Align(
                            alignment: Alignment.centerLeft,
                            child: ChipsWidget(
                              items: selectedAccent,
                              onRemove: (item) {
                                selectedAccent.remove(item);
                                setState(() {});
                              },
                            ),
                          ),
                          44.ph,
                          BlocListener<VocalCharBloc, VocalCharState>(
                            listener: (context, state) {
                              if (state is VocalCharLoadingState) {
                                Dialogs.showOnlyLoader(context);
                              }
                              if (state is VocalCharSuccessState) {
                                // Show Success Message
                                showMessage(
                                    message: AppStrings.vocalCharSubmitSuccess,
                                    isSuccess: true);
                                // Navigate to OTP Screen
                                context.pop();
                                NavigationServiceImpl.getInstance()!
                                    .doNavigation(
                                  context,
                                  routeName: RouteName.uploadSamples,
                                );
                              }
                              if (state is VocalCharErrorState) {
                                // Show Error Message
                                context.pop();
                                showMessage(
                                  message: state.errorMsg,
                                );
                              }
                            },
                            child: PrimaryButton(
                              onPressed: () {
                                if (selectedVoiceGender.isEmpty) {
                                  showMessage(
                                      message:
                                          ValidationMsg.plsSelectVoiceGender);
                                  return;
                                }
                                if (selectedVoiceType.isEmpty) {
                                  showMessage(
                                      message:
                                          ValidationMsg.plsSelectVoiceType);
                                  return;
                                }
                                if (selectedVoiceChar.isEmpty) {
                                  showMessage(
                                      message:
                                          ValidationMsg.plsSelectVoiceChar);
                                  return;
                                }
                                if (selectedExperience == null) {
                                      showMessage( message:ValidationMsg.plsSelect("experience"));
                                    return;
                                }
                                if (selectedVoiceAge.isEmpty) {
                                  showMessage(
                                      message: ValidationMsg.plsSelectVoiceAge);
                                  return;
                                }
                                if (selectedLanguages.isEmpty) {
                                  showMessage(
                                      message:
                                          ValidationMsg.plsSelectVoiceLang);
                                  return;
                                }
                                if (selectedAccent.isEmpty) {
                                  showMessage(
                                      message:
                                          ValidationMsg.plsSelectVoiceAccent);
                                  return;
                                }
                                context.read<VocalCharBloc>().submitVocalChar(
                                        userInfoRequestModel:
                                            UserInfoRequestModel(
                                      voiceCharacter: selectedVoiceChar
                                          .map((e) => e.id)
                                          .toList(),
                                      voiceLanguage: selectedLanguages
                                          .map((e) => e.id)
                                          .toList(),
                                      voiceType: selectedVoiceType
                                          .map((e) => e.id)
                                          .toList(),
                                      voiceGender: selectedVoiceGender
                                          .map((e) => e.id)
                                          .toList(),
                                      ageRange: selectedVoiceAge
                                          .map((e) => e.id)
                                          .toList(),
                                      accent: selectedAccent
                                          .map((e) => e.id)
                                          .toList(),
                                      voiceExperience: selectedExperience?.id,
                                      intermediateStep: 2,
                                    ));
                              },
                              buttonText: AppStrings.continueBtnText,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  showMessage({required String message, bool isSuccess = false}) {
    CustomToast.show(context: context, message: message, isSuccess: isSuccess);
  }
}
