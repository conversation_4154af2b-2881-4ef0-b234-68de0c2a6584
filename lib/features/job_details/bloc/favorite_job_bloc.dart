import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import 'favorite_job_state.dart';

class FavoriteJobBloc extends Cubit<FavoriteJobState> {
  FavoriteJobBloc() : super(FavoriteJobInitState());

  Future<void> apiFavoriteJob(int?  jobIds) async {
    final jobId = jobIds ?? -1;
    emit(FavoriteJobLoadingState(jobId));
    
    try {
      final response = await ApiService.instance.favoriteJob(jobId);
      
      if (response.success) {        
        emit(FavoriteJobSuccessState());
      } else {
        emit(FavoriteJobErrorState(
          jobId,
          response.message ?? AppStrings.genericErrorMsg,
        ));
      }
    } catch (e) {
      emit(FavoriteJobErrorState(jobId, AppStrings.genericErrorMsg));
    }
  }
}
