import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/invited_user_state.dart';


class InvitedUserCubit extends Cubit<InvitedUserState> {
  InvitedUserCubit() : super(const InvitedUserState());

  Future<void> fetchInvitedUsers(int jobId) async {
    emit(state.copyWith(isLoading: true, errorMsg: null));
    try {
      final response = await ApiService.instance.getInvitedUserList(jobId);
      if (response.success) {
        final List<dynamic> ids = response.data as List<dynamic>;
        final List<int> invitedUserIds = ids.map((e) => e as int).toList();
        emit(state.copyWith(invitedUserIds: invitedUserIds, isLoading: false));
      } else {
        emit(state.copyWith(isLoading: false, errorMsg: response.message ?? "Failed to fetch invitees"));
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMsg: "Failed to fetch invitees"));
    }
  }
}
