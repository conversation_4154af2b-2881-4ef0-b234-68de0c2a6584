import 'package:equatable/equatable.dart';

class InvitedUserState extends Equatable {
  final List<int> invitedUserIds;
  final bool isLoading;
  final String? errorMsg;

  const InvitedUserState({
    this.invitedUserIds = const [],
    this.isLoading = false,
    this.errorMsg,
  });

  InvitedUserState copyWith({
    List<int>? invitedUserIds,
    bool? isLoading,
    String? errorMsg,
  }) {
    return InvitedUserState(
      invitedUserIds: invitedUserIds ?? this.invitedUserIds,
      isLoading: isLoading ?? this.isLoading,
      errorMsg: errorMsg ?? this.errorMsg,
    );
  }

  @override
  List<Object?> get props => [invitedUserIds, isLoading, errorMsg];
}