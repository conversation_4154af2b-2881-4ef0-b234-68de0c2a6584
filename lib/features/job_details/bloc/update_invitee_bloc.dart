import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/update_invitee_state.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/model/users_list.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class UpdateInviteesCubit extends Cubit<UpdateInviteeState> {
  UpdateInviteesCubit() : super(const UpdateInviteeState());

  void clearUserList() {
    emit(state.copyWith(usersList: UsersList(users: [])));
  }

  void setSelectedUsers(List<User> users) {
    emit(state.copyWith(selectedUsers: UsersList(users: users)));
  }

  Future<void> searchInvitees({required String searchName}) async {
    emit(state.copyWith(isSearchLoading: true));
    try {
      final response = await ApiService.instance.getUsersList(searchName: searchName);
      if (response.success) {
        emit(state.copyWith(usersList: UsersList.fromJson(response.data)));
      } else {
        emit(state.copyWith(errorMsg: response.message ?? "Error"));
      }
    } catch (e) {
      emit(state.copyWith(errorMsg: "Error"));
    } finally {
      emit(state.copyWith(isSearchLoading: false));
    }
  }

  Future<void> updateInvitees({required int jobId, required List<int> inviteeIds}) async {
    emit(state.copyWith(isSearchLoading: true, errorMsg: null));
    try {
      final response = await ApiService.instance.updateJobInvitees(id: jobId, inviteeIds: inviteeIds);
      if (response.success) {
        emit(state.copyWith(
          isSearchLoading: false,
          errorMsg: null,
          successMsg: AppStrings.inviteSentSuccessfully,
        ));
      } else {
        emit(state.copyWith(isSearchLoading: false, errorMsg: response.message ?? AppStrings.failedToUpdateInvitees));
      }
    } catch (e) {
      emit(state.copyWith(isSearchLoading: false, errorMsg: AppStrings.failedToUpdateInvitees));
    }
  }
}