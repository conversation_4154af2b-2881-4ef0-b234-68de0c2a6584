import 'package:equatable/equatable.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/model/users_list.dart';

class UpdateInviteeState extends Equatable {
  final UsersList? usersList;
  final UsersList? selectedUsers;
  final bool isSearchLoading;
  final String? successMsg;
  final String? errorMsg;

  const UpdateInviteeState({
    this.usersList,
    this.selectedUsers,
    this.isSearchLoading = false,
    this.successMsg,
    this.errorMsg,
  });

  UpdateInviteeState copyWith({
    UsersList? usersList,
    UsersList? selectedUsers,
    bool? isSearchLoading,
    String? successMsg,
    String? errorMsg,
  }) {
    return UpdateInviteeState(
      usersList: usersList ?? this.usersList,
      selectedUsers: selectedUsers ?? this.selectedUsers,
      isSearchLoading: isSearchLoading ?? this.isSearchLoading,
      successMsg: successMsg,
      errorMsg: errorMsg,
    );
  }

  @override
  List<Object?> get props => [usersList, selectedUsers, successMsg, isSearchLoading, errorMsg];
}
