import '../../../utils/common_models/dropdown_data_menu_model.dart';
import '../../common/user_data/data/user_data_model.dart';
import '../../jobs/enums/application_status.dart';
import '../../jobs/enums/job_status.dart';
import '../../post_job/data/enums/sample_script_type.dart';

class JobDetailModel {
  int? id;
  String? clientName;
  List<String>? jobTags;
  String? locationType;
  SampleScriptType? sampleScriptType;
  String? budgetType;
  String? jobPostType;
  JobStatus? jobStatus;
  DateTime? createdAt;
  String? updatedAt;
  int? jobUniqueId;
  String? title;
  String? requirement;
  DropdownData? jobCategory;
  String? studioName;
  String? googleAddress;
  String? addressLine1;
  String? addressLine2;
  String? city;
  String? state;
  int? postalCode;
  String? country;
  NameIdModel? vocalCharacters;
  String? otherVocalCharacter;
  NameIdModel? language;
  NameIdModel? accent;
  NameIdModel? voiceGender;
  NameIdModel? voiceAge;
  NameIdModel? experienceLevel;
  String? urlScriptSample;
  String? fileScriptSample;
  int? fixedBudget;
  int? minBudgetRange;
  int? maxBudgetRange;
  String? dubbingDuration;
  String? dubbingDurationSeconds;
  DateTime? projectDeadline;
  DateTime? responseDeadline;
  int? clientId;
  String? clientPicture;
  bool? isCompleted;
  int? applicationId;
  ApplicationStatus? applicationStatus;
  List<AssociatedUsers>? associatedUsers;
  bool? voiceMarkedCompleted;
  bool? isReviewedByVoice;
  bool? isReviewedByClient;
  bool? isClientAddedQouteDetails;
  String? acceptedVoiceName;
  bool? isJobFavorite;
  String? dubbingDurationHours;

  JobDetailModel({
    this.id,
    this.clientName,
    this.jobTags,
    this.locationType,
    this.sampleScriptType,
    this.budgetType,
    this.jobPostType,
    this.jobStatus,
    this.createdAt,
    this.updatedAt,
    this.jobUniqueId,
    this.title,
    this.requirement,
    this.jobCategory,
    this.studioName,
    this.googleAddress,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.vocalCharacters,
    this.otherVocalCharacter,
    this.language,
    this.accent,
    this.voiceGender,
    this.voiceAge,
    this.experienceLevel,
    this.urlScriptSample,
    this.fileScriptSample,
    this.fixedBudget,
    this.minBudgetRange,
    this.maxBudgetRange,
    this.dubbingDuration,
    this.dubbingDurationSeconds,
    this.projectDeadline,
    this.responseDeadline,
    this.clientId,
    this.clientPicture,
    this.isCompleted,
    this.applicationId,
    this.applicationStatus,
    this.associatedUsers,
    this.voiceMarkedCompleted,
    this.isReviewedByVoice,
    this.isReviewedByClient,
    this.isClientAddedQouteDetails,
    this.acceptedVoiceName,
    this.isJobFavorite,
    this.dubbingDurationHours,
  });

  factory JobDetailModel.fromJson(Map<String, dynamic> json) {
    return JobDetailModel(
      id: json['id'],
      clientName: json['client_name'],
      jobTags: List<String>.from(json['job_tags'] ?? []),
      locationType: json['location_type'],
      sampleScriptType: SampleScriptType.getSampleScriptTypeFromString(json['sample_script_type'] as String?),
      budgetType: json['budget_type'],
      jobPostType: json['job_post_type'],
      jobStatus: JobStatus.getJobStatusFromString(json['job_status'] as String?),
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'],
      jobUniqueId: json['job_unique_id'],
      title: json['title'],
      requirement: json['requirement'],
      jobCategory: json['job_category'] != null ? DropdownData.fromJson(json['job_category']) : null,
      studioName: json['studio_name'],
      googleAddress: json['google_address'],
      addressLine1: json['address_line_1'],
      addressLine2: json['address_line_2'],
      city: json['city'],
      state: json['state'],
      postalCode: json['postal_code'],
      country: json['country'],
      vocalCharacters: json['vocal_characters'] != null ? NameIdModel.fromJson(json['vocal_characters']) : null,
      otherVocalCharacter: json['other_vocal_character'],
      language: json['language'] != null ? NameIdModel.fromJson(json['language']) : null,
      accent: json['accent'] != null ? NameIdModel.fromJson(json['accent']) : null,
      voiceGender: json['voice_gender'] != null ? NameIdModel.fromJson(json['voice_gender']) : null,
      voiceAge: json['voice_age'] != null ? NameIdModel.fromJson(json['voice_age']) : null,
      experienceLevel: json['experience_level'] != null ? NameIdModel.fromJson(json['experience_level']) : null,
      urlScriptSample: json['url_script_sample'],
      fileScriptSample: json['file_script_sample'],
      fixedBudget: json['fixed_budget'],
      minBudgetRange: json['min_budget_range'],
      maxBudgetRange: json['max_budget_range'],
      dubbingDuration: json['dubbing_duration'],
      dubbingDurationSeconds: json['dubbing_duration_seconds'],
      projectDeadline: json['project_deadline'] != null ? DateTime.parse(json['project_deadline']) : null,
      responseDeadline: json['response_deadline'] != null ? DateTime.parse(json['response_deadline']) : null,
      clientId: json['client_id'],
      clientPicture: json['client_picture'],
      isCompleted: json['is_completed'],
      applicationId: json['application_id'],
      applicationStatus: ApplicationStatus.getApplicationStatusFromString(json['application_status'] as String?),
      associatedUsers: (json['associated_users'] as List<dynamic>?)?.map((e) => AssociatedUsers.fromJson(e)).toList(),
      voiceMarkedCompleted: json['voice_marked_completed'] as bool?,
      isReviewedByVoice: json['is_reviewed_by_voice'] as bool?,
      isReviewedByClient: json['is_reviewed_by_client'] as bool?,
      isClientAddedQouteDetails: json['is_client_added_qoute_details'] as bool?,
      acceptedVoiceName: json['accepted_voice_name'] as String?,
      isJobFavorite: json['is_job_favorite'] as bool?,
      dubbingDurationHours: json['dubbing_duration_hours'] as String?,
    );
  }
  JobDetailModel copyWith({bool? isJobFavorite}) {
    return JobDetailModel(
      id: id, isJobFavorite: isJobFavorite ?? isJobFavorite,
    );
  }
}

class AssociatedUsers {
  int? id;
  String? fullName;
  String? profilePic;
  String? email;

  AssociatedUsers({this.id, this.fullName, this.profilePic, this.email});

  factory AssociatedUsers.fromJson(Map<String, dynamic> json) {
    return AssociatedUsers(
      id: json['id'],
      fullName: json['full_name'],
      profilePic: json['profile_pic'],
      email: json['email'],
    );
  }
}
