import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/delete_job/bloc/delete_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/delete_job/bloc/delete_job_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/data/job_detail_model.dart';
import 'package:the_voice_directory_flutter/features/job_details/presentation/applicant_listing_screen.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/job_details/presentation/job_details_screen.dart';
import 'package:the_voice_directory_flutter/features/jobs/bloc/jobs_cubit.dart';
import 'package:the_voice_directory_flutter/features/jobs/enums/jobs_list_type.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/back_button_arrow.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';

import '../../jobs/enums/job_status.dart';
import '../bloc/job_detail_bloc.dart';
import '../bloc/job_detail_state.dart';

class JobManagementScreen extends StatefulWidget {
  final int jobId;
  final bool showApplicantTab;

  const JobManagementScreen({
    super.key,
    required this.jobId,
    this.showApplicantTab = false,
  });

  @override
  State<JobManagementScreen> createState() => _JobManagementScreenState();
}

class _JobManagementScreenState extends State<JobManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UserDataModel? userDataModel;
  bool _isJobFavorite = false;
  bool defaultFavorite = false;
  int userId = -1;
  Timer? _debounceTimer;
  JobDetailModel? jobDetailModel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController( length: 2, vsync: this, initialIndex: widget.showApplicantTab == true ? 1 : 0);
    userDataModel = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    userId = userDataModel?.id ?? -1;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (widget.showApplicantTab == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_tabController.index != 1) {
          _tabController.animateTo(1);
        }
      });
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<JobDetailBloc, JobDetailState>(
      listener: (context, state) {
        if (state is JobSuccessState) {
          setState(() {
            jobDetailModel = state.jobDetailModel;
            _isJobFavorite = defaultFavorite = state.jobDetailModel.isJobFavorite ?? false;
          });
        }
      },
      builder: (context, state) {
        ThemeData theme = Theme.of(context);
        ColorScheme colorScheme = theme.colorScheme;

        return Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: !Responsive.isDesktop(context) ? 0.h : 80.h,
                  vertical: !Responsive.isDesktop(context) ? 0.h : 40.h),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: [
                    if (!!Responsive.isDesktop(context)) ...[
                      CustomBackButtonArrow(),
                      24.ph,
                      ],
                      _buildDesktopHeader(colorScheme),
                      if (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService ) ...[
                        _buildTabBar(colorScheme),
                        Expanded(
                          child: TabBarView(
                            controller: _tabController,
                            physics: const NeverScrollableScrollPhysics(),
                            children: [
                              JobDetailScreen(id: widget.jobId),
                              ApplicantListingScreen(jobId: widget.jobId),
                            ],
                          ),
                        ),
                      ] else
                        Expanded(
                          child: JobDetailScreen(id: widget.jobId),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      }
    );
  }

  Widget _buildDesktopHeader(ColorScheme colorScheme) {
    return BlocBuilder<JobDetailBloc, JobDetailState>(
      builder: (context, state) {
        return Padding(
        padding: EdgeInsets.all(!Responsive.isDesktop(context) ? 16.h : 0.h),
          child: Row(
            children: [
            if (!Responsive.isDesktop(context))
                const Align(
                  alignment: Alignment.centerLeft,
                  child: CustomBackButton(),
                ),
            if (!!Responsive.isDesktop(context)) ...[
                const TextDisplayLarge36And26(AppStrings.jobDetails),
                const Spacer(),
              ] else
                const Expanded(
                  child: Center(
                    child: TextDisplayLarge36And26(AppStrings.jobDetails),
                  ),
                ),
                if( state is JobSuccessState && userDataModel?.role == UserType.voice)...[
                  if(state.jobDetailModel.applicationStatus.toString() != JobStatus.selcted.toString() && context.read<JobsCubit>().state.jobStatusType != JobsListType.history)
                  _buildFavoriteButton(state.jobDetailModel.id),
              !Responsive.isDesktop(context) ? 0.pw : 30.pw
            ],
            if (state is JobSuccessState &&
                (userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                (state.jobDetailModel.jobStatus == JobStatus.open ||
                    state.jobDetailModel.jobStatus == JobStatus.deciding)) ...[
                InkWell(
                  onTap: () {
                    showDeleteDialog(context, widget.jobId);
                  },
                  child: BlocListener<DeleteJobBloc, DeleteJobState>(
                    listener: (context, state) {
                      if (state is DeleteJobLoadingState) {
                        Dialogs.showOnlyLoader(context);
                      }
                      if (state is DeleteJobSuccessState) {
                        context.pop();
                        CustomToast.show(
                          context: context,
                          message: AppStrings.jobDeletedSuccessfully,
                          isSuccess: true,
                        );
                        context.read<JobsCubit>().fetchJobs();
                        context.pop();
                      }
                      if (state is DeleteJobErrorState) {
                        context.pop();
                        CustomToast.show(
                          context: context,
                          message: state.errorMsg,
                        );
                      }
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppImages.trash,
                          color: colorScheme.red,
                        ),
                        10.pw,
                        TextTitle18And20(AppStrings.delete, color: colorScheme.red),
                      ],
                    ),
                  ),
                )
              ]
            ],
          ),
        );
      }
    );
  }

  Widget _buildTabBar(ColorScheme colorScheme) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: !Responsive.isDesktop(context) ? 16.h : 0.h),
      child: TabBar(
        controller: _tabController,
        indicatorColor: colorScheme.primary,
        labelColor: colorScheme.hyperlinkBlueColor,
        unselectedLabelColor: colorScheme.black,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        padding: EdgeInsets.zero,
        indicatorWeight: 4,
        labelPadding: EdgeInsets.only(right: 32.w),
        dividerColor: colorScheme.lightGreyD9D9D9,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: [
          Padding(
            padding: EdgeInsets.only(bottom: 11.h, top: 40),
            child: TextTitle18And14(
              AppStrings.jobDetails,
              color: _tabController.index == 0
                  ? colorScheme.hyperlinkBlueColor
                  : colorScheme.black,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 11.h, top: 40),
            child: TextTitle18And14(
              AppStrings.applicants,
              color: _tabController.index == 1
                  ? colorScheme.hyperlinkBlueColor
                  : colorScheme.black,
            ),
          ),
        ],
      ),
    );
  }

  void showDeleteDialog(BuildContext context, int id) {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.areYouSureYouWantToDeleteThisJob,
      message: AppStrings.yesDeleDeletingThisWillPermanently,
      primaryButtonText: AppStrings.delete,
      primaryButtonColor: const Color(0xFF525252),
      onPrimaryButtonTap: () {
        context.read<DeleteJobBloc>().deleteJob(id);
        context.pop();
      },
    );
  }

    void _handleFavoriteToggle(BuildContext context) {
    _debounceTimer?.cancel();
    if (userId != -1) {
      setState(() {
        _isJobFavorite = !_isJobFavorite;
      });

      if (jobDetailModel != null) {
        jobDetailModel = jobDetailModel!.copyWith(isJobFavorite: _isJobFavorite);
      }
      
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        if (defaultFavorite == _isJobFavorite) return;
        context.read<FavoriteJobBloc>().apiFavoriteJob(widget.jobId);
      });
    }
  }

  Widget _buildFavoriteButton(int ? jobId) {
    return BlocConsumer<FavoriteJobBloc, FavoriteJobState>(
      listener: (context, state) {
        if (state is FavoriteJobSuccessState) {
          final jobsCubit = context.read<JobsCubit>();
          final jobsState = jobsCubit.state;
          context.read<JobsCubit>().fetchJobs(
            jobStatusType: jobsState.jobStatusType,
            jobsSortOrder: jobsState.jobsSortOrder,
            searchName: jobsState.searchName,
          );
          setState(() {
            defaultFavorite = _isJobFavorite;
          });
        }
        if (state is FavoriteJobErrorState) {
          if (state.jobId == jobId) {
            setState(() {
              _isJobFavorite = !_isJobFavorite;
              if (jobDetailModel != null) {
                jobDetailModel = jobDetailModel!.copyWith(isJobFavorite: _isJobFavorite);
              }
            });
            
            CustomToast.show(context: context, message: state.errorMsg, isSuccess: false);
          }
        }
      },
      builder: (context, state) {
        final bool isLoading = state is FavoriteJobLoadingState && state.jobId == jobId;
        
        return CommonCircleIcon(
          removeHoverEffect: true,
          iconPath: _isJobFavorite ? AppImages.filledHeartIcon : AppImages.heartIcon,
          onTap: isLoading ? null : () => _handleFavoriteToggle(context),
        );
      },
    );
  }
}
