import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/applicant_list_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/applicant_list_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/job_detail_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/data/applicant_list_model.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/common_circle_icon.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../utils/custom_toast.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/get_profile_image.dart';
import '../../chat/bloc/chat_bloc.dart';
import '../../jobs/enums/application_status.dart';
import '../bloc/job_detail_bloc.dart';
import '../data/job_detail_model.dart';

class ApplicantListingScreen extends StatefulWidget {
  final int jobId;
  const ApplicantListingScreen({super.key, required this.jobId});

  @override
  State<ApplicantListingScreen> createState() => _ApplicantListingScreenState();
}

class _ApplicantListingScreenState extends State<ApplicantListingScreen> {
  UserDataModel? userDataModel;
  int filterId = 0;
  bool showFilters = true;

  @override
  void initState() {
    super.initState();
    userDataModel = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    context.read<ApplicantListBloc>().getApplicantList(widget.jobId, filterId);
    context.read<JobDetailBloc>().getJobDetail(widget.jobId);
  }

  void _handleFilterChange(int newFilterId) {
    setState(() {
      filterId = newFilterId;
    });
    context.read<ApplicantListBloc>().getApplicantList(widget.jobId, filterId);
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        if (showFilters) _buildFilterTabs(colorScheme),
        Expanded(child: _buildApplicantListContent()),
      ],
    );
  }

  Widget _buildFilterTabs(ColorScheme colorScheme) {
    return Padding(
      padding: EdgeInsets.only(top: 24.h),
      child: Row(
        mainAxisAlignment: !Responsive.isDesktop(context)
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        children: [
          _buildSortingTab(
            colorScheme: colorScheme,
            isSelected: filterId == 0,
            label: AppStrings.all,
            onTap: () => _handleFilterChange(0),
          ),
          8.pw,
          _buildSortingTab(
            colorScheme: colorScheme,
            isSelected: filterId == 2,
            label: AppStrings.shortlisted,
            onTap: () => _handleFilterChange(2),
          ),
          8.pw,
          _buildSortingTab(
            colorScheme: colorScheme,
            isSelected: filterId == 1,
            label: AppStrings.applied,
            onTap: () => _handleFilterChange(1),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicantListContent() {
    return BlocBuilder<JobDetailBloc, JobDetailState>(
      builder: (context, jobDetailState) {
        return BlocBuilder<ApplicantListBloc, ApplicantListState>(
          builder: (context, state) {
            if (state is ApplicantListLoadingState ||
                jobDetailState is JobLoadingState) {
              return const Center(child: CircularProgressIndicator());
            }
            if (state is ApplicantListErrorState ||
                jobDetailState is JobErrorState) {
              return ErrorScreen(
                onRetry: () {
                  context
                      .read<ApplicantListBloc>()
                      .getApplicantList(widget.jobId, filterId);
                  context.read<JobDetailBloc>().getJobDetail(widget.jobId);
                },
              );
            }
            if (state is ApplicantListSuccessState &&
                jobDetailState is JobSuccessState) {
              return _buildApplicantList(context,
                  applicants: state.applicantList,
                  job: jobDetailState.jobDetailModel);
            }
            return const SizedBox();
          },
        );
      },
    );
  }

  Widget _buildApplicantList(BuildContext context,
      {required List<ApplicantListModel> applicants,
      required JobDetailModel job}) {
    if (applicants.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppImages.applicantEmpty,
              height: 100.h,
              width: 100.w,
            ),
            16.ph,
            const TextTitle18And20(
              AppStrings.noApplicantsYet,
            ),
          ],
        ),
      );
    }

    return !Responsive.isDesktop(context)
        ? _buildMobileApplicantList(applicants: applicants, job: job)
        : _buildDesktopApplicantList(applicants: applicants, job: job);
  }

  Widget _buildMobileApplicantList(
      {required List<ApplicantListModel> applicants,
      required JobDetailModel job}) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(vertical: 28.h),
      itemCount: applicants.length,
      separatorBuilder: (_, __) => SizedBox(height: 16.h),
      itemBuilder: (_, index) =>
          _buildApplicantCard(context, applicants[index], job: job),
    );
  }

  Widget _buildDesktopApplicantList(
      {required List<ApplicantListModel> applicants,
      required JobDetailModel job}) {
    return GridView.builder(
      padding: EdgeInsets.symmetric(vertical: 28.h),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 6.5,
        crossAxisSpacing: 24.w,
        mainAxisSpacing: 28.h,
      ),
      itemCount: applicants.length,
      itemBuilder: (_, index) =>
          _buildApplicantCard(context, applicants[index], job: job),
    );
  }

  Widget _buildApplicantCard(BuildContext context, ApplicantListModel applicant,
      {required JobDetailModel job}) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      onTap: () async {
        await NavigationServiceImpl.getInstance()!.doNavigation(
          context,
          routeName: RouteName.applicationDetail,
          pathParameters: {Params.id: applicant.id.toString()},
        ).then((val) {
          if (val == true) {
            context
                .read<ApplicantListBloc>()
                .getApplicantList(widget.jobId, filterId);
          }
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: colorScheme.lightShadowF3F3F3,
            width: 1.5.w,
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 18.h),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildAvatar(applicant, colorScheme),
              8.pw,
              _buildApplicantInfo(applicant, colorScheme),
              const Spacer(),
              if (applicant.applicantStatus != ApplicationStatus.applied)
                CommonCircleIcon(
                  iconPath: AppImages.messageChatIc,
                  onTap: () {
                    final idFrom = userDataModel?.id;
                    final idTo = applicant.voiceId;
                    final jobId = job.id;
                    if (idFrom == null || idTo == null || jobId == null) {
                      CustomToast.show(
                          context: context,
                          message: AppStrings.genericErrorMsg);
                      return;
                    }
                    String groupId = '';
                    if (idFrom < idTo) {
                      groupId = "${jobId}_${idFrom}_$idTo";
                    } else {
                      groupId = "${jobId}_${idTo}_$idFrom";
                    }
                    context.read<ChatBloc>().updateChatState(
                          selectedChat: groupId,
                          isFromChatList: false,
                          isJobChat: true,
                        );
                    NavigationServiceImpl.getInstance()!.doNavigation(
                      context,
                      routeName: RouteName.chat,
                      pathParameters: {
                        Params.jobName: job.title ?? '',
                        Params.groupId: groupId,
                        Params.jobId: jobId.toString(),
                        Params.idFrom: idFrom.toString(),
                        Params.idTo: idTo.toString(),
                        Params.myName:
                            '${userDataModel?.firstName ?? ''} ${userDataModel?.lastName ?? ''}',
                        Params.myProfileImg: ProfileImage.getProfileImage(
                            userDataModel?.profilePic),
                        Params.name: applicant.fullName ?? '',
                        Params.profileImg:
                            ProfileImage.getProfileImage(applicant.profilePic),
                        Params.needBackBtn: 'true',
                      },
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(ApplicantListModel applicant, ColorScheme colorScheme) {
    return SizedBox(
      height: !Responsive.isDesktop(context) ? 44.h : 62.h,
      width: !Responsive.isDesktop(context) ? 44.h : 62.h,
      child: CircleAvatar(
        radius: 60.r,
        backgroundColor: colorScheme.blackE8E8E8,
        backgroundImage: applicant.profilePic?.isNotEmpty == true
             ? Uri.parse(applicant.profilePic ?? '').isAbsolute
             ? NetworkImage(applicant.profilePic ??'')
             : NetworkImage("${EnvironmentConfig.imageBaseUrl}${applicant.profilePic}") : null,
        child: applicant.profilePic?.isNotEmpty == true
            ? null
            : SvgPicture.asset(
                "assets/images/user_icon.svg",
                height: 20.h,
                width: 20.w,
              ),
      ),
    );
  }

  Widget _buildApplicantInfo(
      ApplicantListModel applicant, ColorScheme colorScheme) {
    return SizedBox(
      width: 220.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              TextTitle18And14(applicant.fullName ?? '', maxLines: 1),
              12.pw,
              if (applicant.applicantStatus?.toString() == 'Selected' ||
                  applicant.applicantStatus?.toString() == 'Completed') ...[
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: colorScheme.green00843E.withOpacity(.70),
                    borderRadius: BorderRadius.circular(40.r),
                  ),
                  child: TextBodySmall12(AppStrings.selected,
                      color: colorScheme.white),
                ),
              ]
            ],
          ),
          TextTitle14(applicant.createdAt != null
              ? DateFormat('d MMM, yyyy').format(applicant.createdAt!)
              : ''),
        ],
      ),
    );
  }

  Widget _buildSortingTab({
    required ColorScheme colorScheme,
    required bool isSelected,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 9.h),
        decoration: BoxDecoration(
          color:
              isSelected ? colorScheme.hyperlinkBlueColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color:
                isSelected ? Colors.transparent : colorScheme.lightGreyD9D9D9,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextTitle18And20(
              label,
              color: isSelected ? colorScheme.white : colorScheme.black,
            ),
          ],
        ),
      ),
    );
  }
}
