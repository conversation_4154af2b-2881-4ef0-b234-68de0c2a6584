import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/delete_job/bloc/delete_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/job_detail_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/job_detail_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/update_invitee_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/presentation/privatly_invite_user.dart';
import 'package:the_voice_directory_flutter/features/jobs/bloc/jobs_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/enums/budget_type_enum.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/error_screen.dart';
import 'package:the_voice_directory_flutter/utils/common_audio_player.dart';
import 'package:the_voice_directory_flutter/utils/common_card_widget.dart';
import 'package:the_voice_directory_flutter/utils/common_shadow_container.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/utils/validations.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../../../core/navigation/navigation_service_impl.dart';
import '../../../core/routes/route_names.dart';
import '../../../utils/environment_config.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../jobs/enums/application_status.dart';
import '../../jobs/enums/job_status.dart';
import '../../post_job/data/enums/sample_script_type.dart';
import '../../post_job/models/job_post_model.dart';

class JobDetailScreen extends StatefulWidget {
  final int id;
  const JobDetailScreen({super.key, required this.id});

  @override
  State<JobDetailScreen> createState() => _JobDetailScreenState();
}

class _JobDetailScreenState extends State<JobDetailScreen>
    with SingleTickerProviderStateMixin {
  UserDataModel? userDataModel;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    context.read<JobDetailBloc>().getJobDetail(widget.id);
    userDataModel = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
  }

  @override
  dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void refreshData(BuildContext context) {
    context.read<JobDetailBloc>().getJobDetail(widget.id);
    context.read<JobsCubit>().fetchJobs();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return BlocBuilder<JobDetailBloc, JobDetailState>(
        builder: (context, state) {
      if (state is JobLoadingState) {
        return const Center(child: Loader());
      }
      if (state is JobErrorState) {
        return ErrorScreen(
            onRetry: () { context.read<JobDetailBloc>().getJobDetail(widget.id);
            },
            errorMessage: state.errorMsg,
            imageWidget: SvgPicture.asset(AppImages.snapMomentIcon, height: 200, width: 100));
      }
      if (state is JobSuccessState) {
        return SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              vertical: !Responsive.isDesktop(context) ? 0 : 40.h,
              horizontal: !Responsive.isDesktop(context) ? 16.w : 0.w),
          child:Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextDisplayLarge24And16(
              state.jobDetailModel.title ?? '', fontWeight: FontWeight.w600),
          Responsive.isMobile(context) ? 16.ph : 18.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
                TextTitle18And14('Job ID: #${state.jobDetailModel.jobUniqueId}', color: colorScheme.hyperlinkBlueColor),
                if(userDataModel?.role != UserType.voice)
                inviteVoicesButton(colorScheme, context, jobId: state.jobDetailModel.id ?? -1, disabled: state.jobDetailModel.jobStatus != JobStatus.open),
              ],
            ),
          Responsive.isMobile(context) ? 20.ph : 24.ph,
          Column(
              crossAxisAlignment:
                  !Responsive.isDesktop(context)
                      ? CrossAxisAlignment.center
                      : CrossAxisAlignment.start,
              children: [
                if (userDataModel?.role == UserType.voice) ...[
                  CommonShadowContainer(
                      child: Padding(
                    padding: EdgeInsets.all(
                          !Responsive.isDesktop(context)
                            ? 12.h
                            : 28.h),
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        TextDisplayLarge24And16(
                          AppStrings.clientDetails.toUpperCase(),
                          style: theme
                              .textTheme.titleMedium!
                              .copyWith(
                            color: colorScheme.black,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                          !Responsive.isDesktop(context)
                            ? 12.ph
                            : 28.ph,
                        InkWell(
                          onTap: () {
                            if (state.jobDetailModel.clientId != null) {
                              NavigationServiceImpl.getInstance()!.doNavigation(
                                context,
                                routeName: RouteName.profile,
                                pathParameters: {
                                  Params.id: state.jobDetailModel.clientId.toString(),
                                },
                              );
                            }
                          },
                          child: Row(
                            children: [
                              SizedBox(
                                height: !Responsive.isDesktop(
                                        context)
                                    ? 44.h
                                    : 62.h,
                                width: !Responsive.isDesktop(
                                        context)
                                    ? 44.h
                                    : 62.h,
                                child: CircleAvatar(
                                  radius: 60.r,
                                  backgroundColor:
                                      colorScheme
                                          .blackE8E8E8,
                                  backgroundImage: state
                                              .jobDetailModel
                                              .clientPicture
                                              ?.isNotEmpty ==
                                          true
                                      ? Uri.parse(state
                                                      .jobDetailModel
                                                      .clientPicture ??
                                                  '')
                                              .isAbsolute
                                          ? NetworkImage(state
                                                  .jobDetailModel
                                                  .clientPicture ??
                                              '')
                                          : NetworkImage(
                                              "${EnvironmentConfig.imageBaseUrl}${state.jobDetailModel.clientPicture}")
                                      : null,
                                  child: state
                                              .jobDetailModel
                                              .clientPicture
                                              ?.isNotEmpty ==
                                          true
                                      ? null
                                      : SvgPicture.asset(
                                          "assets/images/user_icon.svg"),
                                ),
                              ),
                              16.pw,
                              Expanded(
                                child: TextDisplayLarge24And16(
                                  state.jobDetailModel.clientName ?? "",
                                  fontWeight: FontWeight.w700,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              5.pw,
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 15,
                                color: colorScheme.black,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )),
                    !Responsive.isDesktop(context)
                      ? 20.ph
                      : 28.ph,
                ],
                CommonShadowContainer(
                  child: Padding(
                    padding: EdgeInsets.all(
                          !Responsive.isDesktop(context)
                            ? 12.h
                            : 28.h),
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment:
                              MainAxisAlignment.center,
                          children: [
                            TextDisplayLarge24And16(
                              AppStrings.requirement,
                              style: theme
                                  .textTheme.titleMedium!
                                  .copyWith(
                                color: colorScheme.black,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const Spacer(),
                            if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                                (state.jobDetailModel.jobStatus == JobStatus.open ||
                                    state.jobDetailModel.jobStatus == JobStatus.deciding)) ...[
                              InkWell(
                                onTap: () async {
                                  // Editing Requirement
                                  HiveStorageHelper.deleteKeyInBox(
                                    boxName: HiveBoxName.user,
                                    key: HiveKeys.editJobData,
                                  );
                                  final job = state.jobDetailModel;
                                  final jobPostModel = JobPostModel(
                                    id: job.id,
                                    title: job.title,
                                    requirement: job.requirement,
                                    jobCategory: job.jobCategory,
                                    jobTags: job.jobTags,
                                  );
                          
                                  await HiveStorageHelper.saveData<JobPostModel>(
                                    HiveBoxName.user,
                                    HiveKeys.editJobData,
                                    jobPostModel,
                                  );
                          
                                  NavigationServiceImpl.getInstance()!.doNavigation(
                                    context,
                                    routeName: RouteName.editJobRequirement,
                                  ).then((_) {
                                    refreshData(context);
                                  });
                                },
                                child: TextTitle18And14(
                                  AppStrings.edit,
                                  color: colorScheme
                                      .hyperlinkBlueColor,
                                ),
                              ),
                            ]
                          ],
                        ),
                          !Responsive.isDesktop(context)
                            ? 12.ph
                            : 28.ph,
                
                  ClickableText(
                      text: state.jobDetailModel.requirement.toString(),
                       fontSizeMobile: 16,
                       color: colorScheme.primaryGrey,
                       fontWeightMobile: FontWeight.w500,
                       fontSizeWeb: 20,
                       fontWeightWeb: FontWeight.w500,
                       maxLines: null,
                           ),
                        4.ph,
                        const TextTitle14(
                            AppStrings.jobRequirement),
                          !Responsive.isDesktop(context)
                            ? 24.ph
                            : 28.ph,
                        TextTitle18And20(state
                                .jobDetailModel
                                .jobPostType ??
                            ''),
                        4.ph,
                        const TextTitle14(
                            AppStrings.invitation),
                      ],
                    ),
                  ),
                ),
                  !Responsive.isDesktop(context)
                    ? 20.ph
                    : 28.ph,
                SizedBox(
                  width: double.infinity,
                  child: CommonShadowContainer(
                    child: Padding(
                      padding: EdgeInsets.all(
                                !Responsive.isDesktop(context)
                              ? 12.h
                              : 28.h),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment:
                            CrossAxisAlignment.start,
                        children: [
                          TextTitle18And14(
                            AppStrings.vocalCharacterstics,
                            style: theme
                                .textTheme.titleMedium!
                                .copyWith(
                              color: colorScheme.black,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                                !Responsive.isDesktop(context)
                              ? 12.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .jobCategory
                                  ?.name ??
                              ''),
                          4.ph,
                          const TextTitle14(
                              AppStrings.type),
                                !Responsive.isDesktop(context)
                              ? 24.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .vocalCharacters
                                  ?.name ??
                              ''),
                          4.ph,
                          TextTitle14(AppStrings
                              .voiceCharacter
                              .substring(
                                  0,
                                  AppStrings.voiceCharacter
                                          .length -
                                      1)),
                           if(state.jobDetailModel.experienceLevel?.name != null)...[
                                  !Responsive.isDesktop(context)
                                      ? 12.ph
                                      : 28.ph,
                            TextTitle18And20(state.jobDetailModel.experienceLevel?.name ?? ''),
                           4.ph,
                           TextTitle14(AppStrings.experience.substring(0, AppStrings.experience.length - 1)),
                            ],
                                !Responsive.isDesktop(context)
                              ? 24.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .language
                                  ?.name ??
                              ''),
                          4.ph,
                          const TextTitle14(
                              AppStrings.scriptLanguage),
                                !Responsive.isDesktop(context)
                              ? 24.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .accent
                                  ?.name ??
                              ''),
                          4.ph,
                          TextTitle14(AppStrings.accent
                              .substring(
                                  0,
                                  AppStrings.accent.length -
                                      1)),
                                !Responsive.isDesktop(context)
                              ? 24.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .voiceGender
                                  ?.name ??
                              ''),
                          4.ph,
                          TextTitle14(AppStrings.voiceGender
                              .substring(
                                  0,
                                  AppStrings.voiceGender
                                          .length -
                                      1)),
                                !Responsive.isDesktop(context)
                              ? 24.ph
                              : 28.ph,
                          TextTitle18And20(state
                                  .jobDetailModel
                                  .voiceAge
                                  ?.name ??
                              ''),
                          4.ph,
              TextTitle14(AppStrings.voiceAge.substring( 0,AppStrings.voiceAge.length - 1)),
              if(state.jobDetailModel.urlScriptSample != null ||  state.jobDetailModel.fileScriptSample != null)...[
                                  !Responsive.isDesktop(context)
                                      ? 12.ph
                                      : 28.ph,
              if (state.jobDetailModel.sampleScriptType == SampleScriptType.urlSampleScript)
              ClickableText(
                text: state.jobDetailModel.urlScriptSample.toString(),
                fontSizeMobile: 16,
                color: colorScheme.primaryGrey,
                fontWeightMobile: FontWeight.w500,
                fontSizeWeb: 20,
                fontWeightWeb: FontWeight.w500,
                maxLines: null,
              )
              else if (state.jobDetailModel.sampleScriptType == SampleScriptType.fileSampleScript) ...[
                CommonAudioPlayer( audioUrl: state.jobDetailModel .fileScriptSample ??'',colorScheme: colorScheme,
                  )
                  ] else ...[
                  const TextTitle18And20( AppStrings.noAudio)],
                          4.ph,
                  const TextTitle14(AppStrings.sampleScript),
                         ],
              ],
                ))),
                ),
                  !Responsive.isDesktop(context)
                    ? 20.ph
                    : 28.ph,
                CommonShadowContainer(
                  child: Padding(
                    padding: EdgeInsets.all(
                          !Responsive.isDesktop(context)
                            ? 12.h
                            : 28.h),
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment:
                              MainAxisAlignment.center,
                          children: [
                            TextDisplayLarge24And16(
                              AppStrings.highlights,
                              style: theme
                                  .textTheme.titleMedium!
                                  .copyWith(
                                color: colorScheme.black,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const Spacer(),
                            if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                                (state.jobDetailModel.jobStatus == JobStatus.open ||
                                    state.jobDetailModel.jobStatus == JobStatus.deciding)) ...[
                              InkWell(
                                onTap: () async {
                                  // Editing Budget
                                  HiveStorageHelper.deleteKeyInBox(
                                    boxName: HiveBoxName.user,
                                    key: HiveKeys.editJobData,
                                  );
                                  final job = state.jobDetailModel;
                                  final jobPostModel = JobPostModel(
                                    id: job.id,
                                    budgetType: BudgetType.getBudgetTypeFromString(job.budgetType ?? ''),
                                    fixedBudget: job.fixedBudget?.toString(),
                                    maxBudgetRange: job.maxBudgetRange?.toString(),
                                    minBudgetRange: job.minBudgetRange?.toString(),
                                  );
                          
                                  await HiveStorageHelper.saveData<JobPostModel>(
                                    HiveBoxName.user,
                                    HiveKeys.editJobData,
                                    jobPostModel,
                                  );
                          
                                  NavigationServiceImpl.getInstance()!.doNavigation(
                                    context,
                                    routeName: RouteName.editJobBudget,
                                  ).then((_) {
                                    refreshData(context);
                                  });
                                },
                                child: TextTitle18And14(
                                  AppStrings.edit,
                                  color: colorScheme
                                      .hyperlinkBlueColor,
                                ),
                              ),
                            ]
                          ],
                        ),
                          !Responsive.isDesktop(context)
                            ? 12.ph
                            : 28.ph,
                        TextTitle18And20(
                          state.jobDetailModel.budgetType ==
                                  AppStrings.fixed
                              ? '₹${state.jobDetailModel.fixedBudget ?? ''}'
                              : '₹${state.jobDetailModel.minBudgetRange ?? ''} - ₹${state.jobDetailModel.maxBudgetRange ?? ''}',
                        ),
                        4.ph,
                        const TextTitle14(
                            AppStrings.jobBudget),
                          !Responsive.isDesktop(context)
                            ? 24.ph
                            : 28.ph,
                        TextTitle18And20((state
                                    .jobDetailModel
                                    .locationType ==
                                AppStrings.remote)
                            ? AppStrings.remote
                            : '${state.jobDetailModel.studioName}, ${state.jobDetailModel.addressLine1}${state.jobDetailModel.addressLine2?.isNotEmpty == true ? ', ${state.jobDetailModel.addressLine2}' : ''}, ${state.jobDetailModel.city}, ${state.jobDetailModel.state}, ${state.jobDetailModel.postalCode}, ${state.jobDetailModel.country}'),
                        4.ph,
                        const TextTitle14(
                            AppStrings.address),
                      ],
                    ),
                  ),
                ),
                  !Responsive.isDesktop(context)
                    ? 20.ph
                    : 28.ph,
                CommonShadowContainer(
                  child: Padding(
                    padding: EdgeInsets.all(
                          !Responsive.isDesktop(context)
                            ? 12.h
                            : 28.h),
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment:
                              MainAxisAlignment.center,
                          children: [
                            TextDisplayLarge24And16(
                              AppStrings.timeLine,
                              style: theme
                                  .textTheme.titleMedium!
                                  .copyWith(
                                color: colorScheme.black,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const Spacer(),
                            if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&
                                (state.jobDetailModel.jobStatus == JobStatus.open ||
                                    state.jobDetailModel.jobStatus == JobStatus.deciding)) ...[
                              InkWell(
                                onTap: () async {
                                  // Editing Timeline
                                  HiveStorageHelper.deleteKeyInBox(
                                    boxName: HiveBoxName.user,
                                    key: HiveKeys.editJobData,
                                  );
                                  final job = state.jobDetailModel;
                                  final jobPostModel = JobPostModel(
                                    id: job.id,
                                    dubbingDurationHours: job.dubbingDurationHours,
                                    dubbingDuration: job.dubbingDuration,
                                    dubbingDurationSeconds: job.dubbingDurationSeconds,
                                    responseDeadline: job.responseDeadline,
                                    projectDeadline: job.projectDeadline,
                                  );
                          
                                  await HiveStorageHelper.saveData<JobPostModel>(
                                    HiveBoxName.user,
                                    HiveKeys.editJobData,
                                    jobPostModel,
                                  );
                          
                                  NavigationServiceImpl.getInstance()!.doNavigation(
                                    context,
                                    routeName: RouteName.editJobTimeline,
                                  ).then((_) {
                                    refreshData(context);
                                  });
                                },
                                child: TextTitle18And14(
                                  AppStrings.edit,
                                  color: colorScheme
                                      .hyperlinkBlueColor,
                                ),
                              ),
                            ]
                          ],
                        ),
                        if (userDataModel?.role ==
                            UserType.voice) ...[
                            !Responsive.isDesktop(context)
                              ? 12.ph
                              : 28.ph,
                          TextTitle18And20(state
                                      .jobDetailModel
                                      .createdAt !=
                                  null
                              ? DateFormat('d MMM, yyyy')
                                  .format(state
                                      .jobDetailModel
                                      .createdAt!)
                              : 'No Deadline'),
                          4.ph,
                          const TextTitle14(
                              AppStrings.datePosted)
                        ],
                          !Responsive.isDesktop(context)
                            ? 12.ph
                            : 28.ph,
                        TextTitle18And20(Validator.formatDuration(state.jobDetailModel.dubbingDurationHours,state.jobDetailModel.dubbingDuration, state.jobDetailModel.dubbingDurationSeconds)),
                        4.ph,
                        TextTitle14(AppStrings.duration.removeLastChar()),
                          !Responsive.isDesktop(context)
                            ? 24.ph
                            : 28.ph,
                        TextTitle18And20(state.jobDetailModel.projectDeadline != null
                            ? DateFormat('d MMM, yyyy').format(state.jobDetailModel.projectDeadline!)
                            : 'No Deadline'),
                        4.ph,
                        TextTitle14(AppStrings.projectDeadline.substring(0,AppStrings.projectDeadline.length -1)),
                
                          !Responsive.isDesktop(context)
                            ? 24.ph
                            : 28.ph,
                
                        TextTitle18And20(state.jobDetailModel.responseDeadline != null
                            ? DateFormat('d MMM, yyyy').format(state.jobDetailModel.responseDeadline!)
                            : 'No Deadline'),
                        4.ph,
                        const TextTitle14(
                            AppStrings.responseDeadline),
                      ],
                    ),
                  ),
                ),
                  !Responsive.isDesktop(context)
                    ? 20.ph
                    : 28.ph,
               if (state.jobDetailModel.jobTags?.isNotEmpty == true) ...[
                SizedBox(
                  width: double.infinity,
                  child: CommonShadowContainer(
                    child: Padding(
                      padding: EdgeInsets.all(
                              !Responsive.isDesktop(context)
                              ? 12.h
                              : 28.h),
                      child: Column(
                        crossAxisAlignment:
                            CrossAxisAlignment.start,
                        children: [
                          TextTitle18And14(
                            AppStrings.keywords
                                .toUpperCase(),
                            style: theme
                                .textTheme.titleMedium!
                                .copyWith(
                              color: colorScheme.black,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                              !Responsive.isDesktop(context)
                              ? 12.ph
                              : 28.ph,
                          CommonCardWidget<String>(
                            items: state.jobDetailModel
                                    .jobTags ??
                                [],
                            itemBuilder: (context, item) {
                              return TextTitle14(
                                item.toString(),
                                color: colorScheme
                                    .hyperlinkBlueColor,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                    !Responsive.isDesktop(context)
                    ? 20.ph
                    : 28.ph,
                ],
              ]),
          if (state.jobDetailModel.associatedUsers?.isNotEmpty == true) ...[
            CommonShadowContainer(
              child: Padding(
                padding: EdgeInsets.all(
                      !Responsive.isDesktop(context)
                        ? 12.h
                        : 28.h),
                child: Column(
                  crossAxisAlignment:
                      CrossAxisAlignment.start,
                  children: [
                    TextDisplayLarge24And16(
                      AppStrings.invitees,
                      style: theme.textTheme.titleMedium!
                          .copyWith(
                        color: colorScheme.black,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                      !Responsive.isDesktop(context)
                        ? 20.ph
                        : 28.ph,
                    ListView.builder(
                      shrinkWrap: true,
                      physics:
                          const NeverScrollableScrollPhysics(),
                      itemCount: state.jobDetailModel
                          .associatedUsers?.length,
                      padding: const EdgeInsets.symmetric(
                          vertical: 8),
                      itemBuilder: (context, index) {
                        final invitees = state
                            .jobDetailModel
                            .associatedUsers?[index];
                        return Padding(
                          padding:
                              const EdgeInsets.symmetric(
                                  vertical: 6.0,
                                  horizontal: 12.0),
                          child: CommonShadowContainer(
                            blurRadius: 9,
                            child: Padding(
                              padding: const EdgeInsets.all(
                                  12.0),
                              child: Row(
                                crossAxisAlignment:
                                    CrossAxisAlignment
                                        .center,
                                children: [
                                  SizedBox(
                                    height:
                                        !Responsive.isDesktop(
                                                context)
                                            ? 44.h
                                            : 62.h,
                                    width:
                                        !Responsive.isDesktop(
                                                context)
                                            ? 44.h
                                            : 62.h,
                                    child: CircleAvatar(
                                      radius: 60.r,
                                      backgroundColor:
                                          colorScheme
                                              .blackE8E8E8,
                                      backgroundImage: invitees?.profilePic?.isNotEmpty == true
                                          ? Uri.parse(invitees?.profilePic ?? '').isAbsolute
                                              ? NetworkImage(invitees?.profilePic ?? '')
                                              : NetworkImage("${EnvironmentConfig.imageBaseUrl}${invitees?.profilePic}")
                                          : null,
                                      child: invitees?.profilePic?.isNotEmpty == true
                                          ? null
                                          : SvgPicture.asset("assets/images/user_icon.svg"),
                                    ),
                                  ),
                                  14.pw,
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment
                                            .start,
                                    children: [
                                      TextTitle14(invitees
                                              ?.fullName ??
                                          ''),
                                      4.ph,
                                      TextBodySmall12(
                                          invitees?.email ??
                                              ''),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
          ],
                
        if ((userDataModel?.role == UserType.client || userDataModel?.role == UserType.ancillaryService) &&state.jobDetailModel.jobStatus == JobStatus.inProgress) ...[
              if (!!Responsive.isDesktop(context)) ...[
                Divider(
                    color: colorScheme.lightGreyD9D9D9,
                    thickness: 1),
                20.ph,
              ],
              Center(
                child: PrimaryButton(
                    width: !Responsive.isDesktop(context)
                      ? null
                      : MediaQuery.of(context).size.width * .6,
                  buttonText: AppStrings.markComplete,
                  onPressed: () {
                    if (state.jobDetailModel
                            .voiceMarkedCompleted ==
                        false) {
                    CustomToast.show(
                     context: context,
                     message: AppStrings.theVoiceNeedsToComplete,
                    isSuccess: true,
                     );
                    } else {
                      // if (state.jobDetailModel.isReviewedByClient == true) {
                      //   NavigationServiceImpl.getInstance()!.doNavigation(
                      //     context,
                      //     routeName: RouteName.jobPayment,
                      //     pathParameters: {
                      //       Params.id: state.jobDetailModel.id?.toString() ?? '-1',
                      //     },
                      //   );
                      // } else {
                        NavigationServiceImpl.getInstance()!.doNavigation(
                          context,
                          routeName: RouteName.jobReview,
                          pathParameters: {
                            Params.id: state.jobDetailModel.id?.toString() ?? '-1',
                            Params.name: state.jobDetailModel.acceptedVoiceName?.toString() ?? '',
                          },
                        );
                      }
                    }
                  //},
                ),
              ),
              40.ph
            ],
                
            if (userDataModel?.role == UserType.voice && state.jobDetailModel.voiceMarkedCompleted != true &&
                (state.jobDetailModel.acceptedVoiceName == null || state.jobDetailModel.applicationId != null)) ...[
              if (!!Responsive.isDesktop(context)) ...[
              Divider(
                  color: colorScheme.lightGreyD9D9D9,
                  thickness: 1),
              20.ph,
            ],
            Center(
              child: PrimaryButton(
                  width: !Responsive.isDesktop(context)
                    ? null
                    : MediaQuery.of(context).size.width *
                        .6,
                buttonText: state.jobDetailModel.applicationStatus == ApplicationStatus.applied || state.jobDetailModel.applicationStatus == ApplicationStatus.shortlisted
                    ? AppStrings.viewApplication
                    : state.jobDetailModel.applicationStatus == ApplicationStatus.selected
                        ? AppStrings.markComplete
                        : AppStrings.apply,
                onPressed: () {
                    if (state.jobDetailModel.applicationStatus == ApplicationStatus.applied || state.jobDetailModel.applicationStatus == ApplicationStatus.shortlisted) {
                      NavigationServiceImpl.getInstance()!.doNavigation(
                        context,
                        routeName: RouteName.applicationDetail,
                        pathParameters: {
                          Params.id: state.jobDetailModel.applicationId.toString()
                        },
                      ).then((_) {
                        refreshData(context);
                      });
                    } else if (state.jobDetailModel.applicationStatus == ApplicationStatus.selected) {
                      // if (state.jobDetailModel.isReviewedByVoice == true) {
                      //   NavigationServiceImpl.getInstance()!.doNavigation(
                      //     context,
                      //     routeName: RouteName.jobPayment,
                      //     pathParameters: {
                      //       Params.id: state.jobDetailModel.id?.toString() ?? '-1',
                      //     },
                      //   );
                      // } else {
                        NavigationServiceImpl.getInstance()!.doNavigation(
                          context,
                          routeName: RouteName.jobReview,
                          pathParameters: {
                            Params.id: state.jobDetailModel.id?.toString() ?? '-1',
                            Params.name: state.jobDetailModel.clientName?.toString() ?? '',
                          },
                        );
                     // }
                    } else {
                      NavigationServiceImpl.getInstance()!.doNavigation(
                        context,
                        routeName: RouteName.applyJob,
                        pathParameters: {
                          Params.id: widget.id.toString()
                        },
                      ).then((_) {
                        refreshData(context);
                      });
                    }
                  },
                ),
              ),
            80.ph
          ],
          80.ph,
        ]),
        );
      }
      return const SizedBox();
    });
  }

  void showDeleteDialog(BuildContext context, int id) {
    Dialogs.showCommonDialog(
      context: context,
      title: AppStrings.areYouSureYouWantToDeleteThisJob,
      message: AppStrings.yesDeleDeletingThisWillPermanently,
      primaryButtonText: AppStrings.delete,
      primaryButtonColor: const Color(0xFF525252),
      onPrimaryButtonTap: () {
        context.read<DeleteJobBloc>().deleteJob(id);
        Navigator.pop(context);
      },
    );
  }
}

Widget inviteVoicesButton(ColorScheme colorScheme, BuildContext context, {required int jobId, bool disabled = false}) {
  return Opacity(
    opacity: disabled ? 0.5 : 1.0,
    child: InkWell(
      onTap: disabled ? null : () {
        Responsive.isMobile(context)
            ? Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider.value(
                    value: BlocProvider.of<UpdateInviteesCubit>(context),
                    child: PrivatelyInviteUser(jobId: jobId),
                  ),
                ),
              )
            : showDialog(
                context: context,
                builder: (_) {
                  return BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: EdgeInsets.symmetric(
                        horizontal: 286.w,
                        vertical: 86.h,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: BlocProvider.value(
                          value: BlocProvider.of<UpdateInviteesCubit>(context),
                          child: PrivatelyInviteUser(jobId: jobId),
                        ),
                      ),
                    ),
                  );
                },
              );
      },
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Responsive.isMobile(context) ? 6.h : 11.h,
          horizontal: Responsive.isMobile(context) ? 8.h : 16.h,
        ),
        decoration: BoxDecoration(
          color: colorScheme.hyperlinkBlueColor,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                color: colorScheme.white,
              ),
              child: Icon(
                Icons.add,
                size: 16,
                color: colorScheme.darkGrey525252,
              ),
            ),
            14.pw,
            TextTitle18And20(
              AppStrings.privateInvite.replaceFirstMapped(RegExp(r' (\w)'),
              (match) => ' ${match.group(1)!.toUpperCase()}'),
              color: colorScheme.white,
            ),
          ],
        ),
      ),
    ),
  );
}
