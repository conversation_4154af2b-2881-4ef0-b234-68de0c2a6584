import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/invited_user_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/invited_user_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/update_invitee_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/update_invitee_state.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/job_detail_bloc.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/model/users_list.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/widgets/user_invitee_tile.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/responsive.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/back_button.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../../../../widgets/texts/app_text.dart';

class PrivatelyInviteUser extends StatefulWidget {
  final int jobId;

  const PrivatelyInviteUser({
    super.key,
    required this.jobId,
  });

  @override
  State<PrivatelyInviteUser> createState() => _PrivatelyInviteUserState();
}

class _PrivatelyInviteUserState extends State<PrivatelyInviteUser> {
  late TextEditingController searchController = TextEditingController();
  Timer? _debounce;
  List<User> _selectedUsers = [];

  @override
  void initState() {
    super.initState();
    context.read<InvitedUserCubit>().fetchInvitedUsers(widget.jobId);
    context.read<UpdateInviteesCubit>().clearUserList();
    _selectedUsers = List.from(context.read<UpdateInviteesCubit>().state.selectedUsers?.users ?? []);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    searchController.dispose();
    super.dispose();
  }

  _onSearchChanged(BuildContext context) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (searchController.text.isNotEmpty) {
        context.read<UpdateInviteesCubit>().searchInvitees(searchName: searchController.text);
      } else {
        context.read<UpdateInviteesCubit>().clearUserList();
      }
    });
  }

  void _toggleUserSelection(int userId) {
    if (userId == -1) return;
    final isSelected = _selectedUsers.any((user) => user.id == userId);
    if (isSelected) {
      setState(() {
        _selectedUsers.removeWhere((user) => user.id == userId);
      });
    } else {
      final userToAdd = (context.read<UpdateInviteesCubit>().state.usersList?.users ?? [])
          .where((user) => user.id == userId)
          .cast<User?>()
          .firstOrNull;
      if (userToAdd != null) {
        setState(() {
          _selectedUsers.add(userToAdd);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      body: Container(
        color: colorScheme.white,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            if (Responsive.isMobile(context)) ...[
              20.ph,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const CustomBackButton(),
                      12.pw,
                      const Text24And20SemiBold(AppStrings.privateInvite),
                    ],
                  ),
                  TextButton(
                    onPressed: _selectedUsers.isNotEmpty ? () async {
                      final inviteeIds = _selectedUsers.map((u) => u.id!).toList();
                      await context.read<UpdateInviteesCubit>().updateInvitees( jobId: widget.jobId, inviteeIds: inviteeIds);
                      Navigator.pop(context);
                    } : null,
                    child: TextTitle18And14(
                      AppStrings.done,
                      color: _selectedUsers.isNotEmpty
                          ? colorScheme.secondary
                          : colorScheme.lightGreyB2B2B2,
                    ),
                  ),
                ],
              ),
            ],
            if (Responsive.isDesktop(context)) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: _selectedUsers.isNotEmpty ? () async {
                      final inviteeIds = _selectedUsers.map((u) => u.id!).toList();
                      await context.read<UpdateInviteesCubit>().updateInvitees( jobId: widget.jobId, inviteeIds: inviteeIds);
                      Navigator.pop(context);
                    } : null,
                    child: TextTitle18And14(
                      AppStrings.done,
                      color: _selectedUsers.isNotEmpty
                          ? colorScheme.secondary
                          : colorScheme.lightGreyB2B2B2,
                    ),
                  ),
                  const CustomBackButton(icon: Icons.close),
                ],
              ),
              20.ph,
              const Align(
                alignment: Alignment.centerLeft,
                child: Text24And20SemiBold(AppStrings.privateInvite),
              ),
            ],
            Responsive.isMobile(context) ? 16.ph : 36.ph,
            AppTextFormField(
              controller: searchController,
              hintText: AppStrings.search,
              prefixIcon: Icon(
                Icons.search,
                color: colorScheme.lightGreyD9D9D9,
              ),
              onChanged: (_) {
                setState(() {});
                _onSearchChanged(context);
              },
              suffixIcon: searchController.text.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        searchController.clear();
                        context.read<UpdateInviteesCubit>().clearUserList();
                        setState(() {});
                      },
                      child: Padding(
                        padding: EdgeInsets.all(8.0.h),
                        child: SvgPicture.asset(AppImages.closeSquareIc),
                      ),
                    )
                  : null,
            ),
            BlocListener<UpdateInviteesCubit, UpdateInviteeState>(
              listener: (context, state) {
                if (state.successMsg != null) {
                  CustomToast.show(context: context, message: state.successMsg!, isSuccess: true);
                  context.read<JobDetailBloc>().getJobDetail(widget.jobId);
                }
              },
              child: Flexible(
                child: BlocBuilder<UpdateInviteesCubit, UpdateInviteeState>(
                  builder: (context, state) {
                    if (state.isSearchLoading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state.errorMsg != null) {
                      return Center(child: Text('Error: ${state.errorMsg}'));
                    } else if (searchController.text.isNotEmpty && state.usersList != null &&
                        state.usersList!.users != null &&
                        state.usersList!.users!.isNotEmpty) {
                      // Show search results
                      final users = state.usersList!.users!;
                      return BlocBuilder<InvitedUserCubit, InvitedUserState>(
                        builder: (context, invitedState) {

                          final selectedUserIds = _selectedUsers.map((u) => u.id).toSet();
                          final alreadyInvitedIds = invitedState.invitedUserIds.toSet();
                          final selectedUsersOnTop = _selectedUsers.where((user) => !alreadyInvitedIds.contains(user.id)).toList();
                          final otherUsers = users.where((user) => !selectedUserIds.contains(user.id) && !alreadyInvitedIds.contains(user.id)).toList();
                          final invitedUsers = users.where((user) => alreadyInvitedIds.contains(user.id)).toList();

                          final displayUsers = [
                            ...selectedUsersOnTop,
                            ...otherUsers,
                            ...invitedUsers,
                          ];
                          return ListView.builder(
                            padding: const EdgeInsets.symmetric(vertical: 20),
                            itemCount: displayUsers.length,
                            itemBuilder: (context, index) {
                              final user = displayUsers[index];
                              final isAlreadyInvited = invitedState.invitedUserIds.contains(user.id);
                              final isSelected = _selectedUsers.any((u) => u.id == user.id) || isAlreadyInvited;

                              return UserInviteeTile(
                                user: user,
                                checkBoxValue: isSelected,
                                enabled: !isAlreadyInvited,
                                onChanged: isAlreadyInvited
                                    ? null
                                    : (value) {
                                        if (value != null) {
                                          _toggleUserSelection(user.id ?? -1);
                                        }
                                      },
                              );
                            },
                          );
                        },
                      );
                    } else if (searchController.text.isEmpty && _selectedUsers.isNotEmpty) {
                      // Show only selected users when no search is active
                      return BlocBuilder<InvitedUserCubit, InvitedUserState>(
                        builder: (context, invitedState) {
                          final alreadyInvitedIds = invitedState.invitedUserIds.toSet();
                          final selectedUsersToShow = _selectedUsers.where((user) => !alreadyInvitedIds.contains(user.id)).toList();
                          
                          return ListView.builder(
                            padding: const EdgeInsets.symmetric(vertical: 20),
                            itemCount: selectedUsersToShow.length,
                            itemBuilder: (context, index) {
                              final user = selectedUsersToShow[index];
                              final isAlreadyInvited = invitedState.invitedUserIds.contains(user.id);
                              final isSelected = _selectedUsers.any((u) => u.id == user.id) || isAlreadyInvited;

                              return UserInviteeTile(
                                user: user,
                                checkBoxValue: isSelected,
                                enabled: !isAlreadyInvited,
                                onChanged: isAlreadyInvited
                                    ? null
                                    : (value) {
                                        if (value != null) {
                                          _toggleUserSelection(user.id ?? -1);
                                        }
                                      },
                              );
                            },
                          );
                        },
                      );
                    } else {
                      if (searchController.text.isNotEmpty) {
                        return Center(
                          child: TextTitle14(
                            AppStrings.noUserFound,
                            textAlign: TextAlign.center,
                            fontWeight: FontWeight.w700,
                            fontSize: Responsive.isMobile(context) ? 14.sp : 18.sp,
                          ),
                        );
                      } else {
                        return Center(
                          child: TextTitle14(
                            AppStrings.searchAnArtist,
                            textAlign: TextAlign.center,
                            fontWeight: FontWeight.w700,
                            fontSize: Responsive.isMobile(context) ? 14.sp : 18.sp,
                          ),
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
