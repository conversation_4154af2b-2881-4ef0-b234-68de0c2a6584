import 'dart:developer';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';



class PushNotificationService {
  static PushNotificationService? _instance;

  static PushNotificationService? getInstance() {
    _instance ??= PushNotificationService._();
    return _instance;
  }

  PushNotificationService._();

  Future<void> setupNotifications() async {
    await OneSignal.Debug.setLogLevel(OSLogLevel.verbose);

    OneSignal.initialize(SecretKeys.oneSignalAppId);
    await OneSignal.Notifications.requestPermission(true);

    try {
      final playerId = HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.playerId);
      if (playerId == null || playerId.isEmpty) {
        final deviceState = OneSignal.User.pushSubscription;
        if (deviceState.id != null) {
          final String playerId = deviceState.id!;
          log('Initial player id: $playerId');
          await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.playerId, playerId);
        } else {
          for (int i = 1; i <= 10; i++) {
            await Future.delayed(const Duration(seconds: 2));
            final subscription = OneSignal.User.pushSubscription;
            if (subscription.id != null) {
              final String playerId = subscription.id!;
              log('Player id obtained after $i attempts: $playerId');
              await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.playerId, playerId);
              break;
            } else {
              log('Attempt $i failed to get player id');
            }
          }
        }
      }
    } catch (e) {
      log('Error getting player id: $e');
    }
  }
}
