import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/features/chat/presentation/pages/messages_screen.dart';
import 'package:the_voice_directory_flutter/features/google_calender/presentation/google_calender_screen.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/privacy_policy/privacy_and_terms_screen.dart';
import '../../features/dashboard/bloc/dashboard_cubit.dart';
import '../../features/dashboard/presentation/pages/dashboard.dart';
import '../../features/explore_public_voices/presentation/explore_public_voices_screen.dart';
import '../../features/jobs/bloc/jobs_cubit.dart';
import '../../features/landing_page/bloc/audio_player_cubit.dart';
import '../../features/landing_page/bloc/explore_public_voices_cubit.dart';
import '../../features/landing_page/presentation/landing_page_screen.dart';
import '../../features/public_voice_profile/bloc/public_voice_profile_cubit.dart';
import '../../features/public_voice_profile/presentation/public_voice_profile_screen.dart';
import '../../widgets/dashboard_header.dart';
import '../dependency_injection/service_locator.dart';
import '../navigation/navigation_service.dart';
import '../services/hive/hive_keys.dart';
import '../services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/route_observer/route_observer_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/accept_candidate_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/applicant_list_bloc.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/bloc/apply_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/presentation/voice_apply_job.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/bloc/upload_media_bloc.dart';
import 'package:the_voice_directory_flutter/features/delete_job/bloc/delete_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/edit_profile_details/Presentation/edit_profile_details.dart';
import 'package:the_voice_directory_flutter/features/enter_info/bloc/enter_info_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/presentation/job_management_screen.dart';
import 'package:the_voice_directory_flutter/features/login/presentation/pages/login_screen.dart';
import 'package:the_voice_directory_flutter/features/application_detail/bloc/application_details_bloc.dart';
import 'package:the_voice_directory_flutter/features/application_detail/presentation/application_detail_screen.dart';
import 'package:the_voice_directory_flutter/features/notification/presentation/notification_list_screen.dart';
import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/presentation/post_a_job_screen.dart';
import 'package:the_voice_directory_flutter/features/prefered_project_type/bloc/static_data_dropdown_bloc.dart';
import 'package:the_voice_directory_flutter/features/create_profile/bloc/create_profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/create_profile/presentation/create_profile_screen.dart';
import 'package:the_voice_directory_flutter/features/reviews/bloc/review_bloc.dart';
import 'package:the_voice_directory_flutter/features/enter_info/presentation/enter_information_screen.dart';
import 'package:the_voice_directory_flutter/features/signup/presentation/pages/signup_screen.dart';
import 'package:the_voice_directory_flutter/features/splash/splash_screen.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/project_type_submit_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/bloc/upload_samples_bloc.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/presentation/preferred_project_type.dart';
import 'package:the_voice_directory_flutter/features/upload_audio/presentation/upload_audio.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/presentation/verify_otp_screen.dart';
import 'package:the_voice_directory_flutter/features/vocal_characteristics/bloc/vocal_char_bloc.dart';
import 'package:the_voice_directory_flutter/features/vocal_characteristics/presentation/vocal_characteristics_screen.dart';
import '../../features/application_detail/bloc/shortlist_candidate_bloc.dart';
import '../../features/explore_artists/bloc/explore_artists_bloc.dart';
import '../../features/job_payment/bloc/job_payment_bloc.dart';
import '../../features/job_payment/presentation/job_payment_screen.dart';
import '../../features/job_review/bloc/job_review_bloc.dart';
import '../../features/job_review/presentation/job_review_screen.dart';
import '../../features/common/user_data/data/user_data_model.dart';
import '../../features/edit_job/bloc/edit_job_cubit.dart';
import '../../features/edit_job/presentation/edit_job_budget.dart';
import '../../features/edit_job/presentation/edit_job_requirement.dart';
import '../../features/edit_job/presentation/edit_job_timeline.dart';
import '../../features/reviews/presentation/review_list_screen.dart';
import '../../features/upload_audio/bloc/upload_audio_video_cubit.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/bloc/profile_bloc.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/edit_create_profile.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/edit_preferred_profile.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/edit_vocal_characteristics.dart';
import 'package:the_voice_directory_flutter/features/voice/profile/presentation/profile_screen.dart';
import '../../features/edit_audio_file/presentation/edit_upload_audio.dart';

final router = GoRouter(
  navigatorKey: serviceLocator<NavigationService>().getGlobalKey(),
  initialLocation: kIsWeb ? RoutePath.landingPage : RoutePath.splash,
  observers: [MyRouteObserver.getInstance()],
  // redirect: authMiddleware,
  routes: [
    GoRoute(
      name: RouteName.landingPage,
      path: RoutePath.landingPage,
      pageBuilder: (context, state) {
        final scrollTo = state.extra is String ? state.extra as String : null;
        return MaterialPage(
          child: LandingPageScreen(scrollTo: scrollTo),
        );
      },
    ),
    GoRoute(
      name: RouteName.voiceProfile,
      path: RoutePath.voiceProfile,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: BlocProvider(
            create: (_) => PublicVoiceProfileCubit(),
            child: PublicVoiceProfileScreen(
              id: int.tryParse(state.pathParameters[Params.id] ?? ''),
            ),
          ),
        );
      },
    ),
    GoRoute(
      name: RouteName.explorePublicVoices,
      path: RoutePath.explorePublicVoices,
      pageBuilder: (context, state) => MaterialPage(
        child: MultiBlocProvider(
          providers: [
            BlocProvider(create: (_) => AudioPlayerCubit()),
            BlocProvider(create: (_) => ExplorePublicVoicesCubit()),
          ],
          child: const ExplorePublicVoicesScreen(),
        ),
      ),
    ),
    GoRoute(
      name: RouteName.login,
      path: RoutePath.login,
      pageBuilder: (context, state) {
        return const MaterialPage(
          child: LoginScreen(),
        );
      },
      redirect: (_, state) async {
        if (HiveStorageHelper.getData<bool>(
                HiveBoxName.user, HiveKeys.isUserLoggedIn) ??
            false) {
          return RoutePath.dashboard;
        } else {
          return state.matchedLocation;
        }
      },
    ),

    // Shell route for authenticated pages
    ShellRoute(
      navigatorKey: serviceLocator<NavigationService>().getShellNavigatorKey(),
      pageBuilder: (context, state, child) {
        final bool isUserLoggedIn = HiveStorageHelper.getData<bool>(
                HiveBoxName.user, HiveKeys.isUserLoggedIn) ??
            false;
        final UserDataModel? userData =
            HiveStorageHelper.getData<UserDataModel>(
                HiveBoxName.user, HiveKeys.userData);

        if (!isUserLoggedIn) {
          return const NoTransitionPage(child: LoginScreen());
        } else {
          return NoTransitionPage(
            child: MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => DashboardCubit(
                      hasClientTab: userData?.role == UserType.client || userData?.role == UserType.ancillaryService),
                ),
                BlocProvider(create: (_) => JobsCubit()),
              ],
              child: Scaffold(
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const DashboardHeader(),
                    Expanded(child: child),
                  ],
                ),
              ),
            ),
          );
        }
      },
      routes: [
        GoRoute(
          name: RouteName.dashboard,
          path: RoutePath.dashboard,
          pageBuilder: (context, state) => MaterialPage(
            child: MultiBlocProvider(
              providers: [
                BlocProvider(create: (_) => ExploreArtistsBloc()),
              ],
              child: const Dashboard(),
            ),
          ),
          redirect: (_, state) async {
            if (HiveStorageHelper.getData<bool>(
                    HiveBoxName.user, HiveKeys.isUserLoggedIn) ??
                false) {
              return state.matchedLocation;
            } else {
              return RoutePath.login;
            }
          },
        ),
        GoRoute(
          name: RouteName.editProfileDetails,
          path: RoutePath.editProfileDetails,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => ProfileBloc(),
                  ),
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                ],
                child: const EditCreateProfile(),
              ),
            );
          },
        ),
        GoRoute(
            name: RouteName.profile,
            path: RoutePath.profile,
            pageBuilder: (context, state) {
              return MaterialPage(
                child: ProfileScreen(
                  id: int.tryParse(state.pathParameters[Params.id] ?? ''),
                ),
              );
            }),
        GoRoute(
            name: RouteName.editBasicDetails,
            path: RoutePath.editBasicDetails,
            pageBuilder: (context, state) {
              return MaterialPage(
                  child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => ProfileBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadMediaBloc(),
                  ),
                  BlocProvider(
                    create: (context) => ResendOtpBloc(),
                  ),
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                ],
                child: const EditProfileDetails(),
              ));
            }),
        GoRoute(
          name: RouteName.editPreferredProjectType,
          path: RoutePath.editPreferredProjectType,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => ProfileBloc(),
                  ),
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                ],
                child: const EditPreferredProjectType(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.editAudio,
          path: RoutePath.editAudio,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (_) => UploadAudioVideoCubit(),
                  ),
                  BlocProvider(
                    create: (context) => UploadMediaBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadSamplesBloc(),
                  ),
                ],
                child: const EditUploadAudioScreen(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.editVocalCharacteristics,
          path: RoutePath.editVocalCharacteristics,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => ProfileBloc(),
                  ),
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                ],
                child: const EditVocalCharacteristicScreen(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.googleCalender,
          path: RoutePath.googleCalender,  // This should be '/google_calendar/:id'
          pageBuilder: (context, state) {
            return MaterialPage(
              child: GoogleCalenderScreen(
                events: const [], 
                accessToken: HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.googleAccessToken) ?? '',
                userId: int.tryParse(state.pathParameters[Params.id] ?? ''),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.applicationDetail,
          path: RoutePath.applicationDetail,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => ApplicationDetailBloc()),
                  BlocProvider(create: (_) => ShortlistCandidateBloc()),
                  BlocProvider(create: (_) => AcceptCandidateBloc()),
                ],
                child: ApplicationDetailScreen(
                  applicationId:
                      int.tryParse(state.pathParameters[Params.id] ?? '') ?? 0,
                ),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.jobDetail,
          path: RoutePath.jobDetail,
          pageBuilder: (context, state) => MaterialPage(
            child: MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => ApplicantListBloc(),
                ),
                BlocProvider(
                  create: (context) => DeleteJobBloc(),
                ),
                BlocProvider(
                  create: (context) => FavoriteJobBloc(),
                ),
              ],
              child: JobManagementScreen(
                  jobId:
                      int.tryParse(state.pathParameters[Params.id] ?? '') ?? 0,
                  showApplicantTab: state.pathParameters[Params.showApplicantTab] == 'true'),
            ),
          ),
        ),
        GoRoute(
          name: RouteName.postJob,
          path: RoutePath.postJob,
          pageBuilder: (context, state) {
            return MaterialPage(
              name: RouteName.postJob,
              maintainState: true,
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => PostAJobCubit(isEditing: false)),
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadMediaBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadAudioVideoCubit(),
                  ),
                ],
                child: const PostAJobScreen(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.editJobRequirement,
          path: RoutePath.editJobRequirement,
          pageBuilder: (context, state) {
            return MaterialPage(
              name: RouteName.editJobRequirement,
              maintainState: true,
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => EditJobCubit()),
                  BlocProvider(create: (_) => StaticDataDropdownBloc()),
                ],
                child: const EditJobRequirement(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.editJobTimeline,
          path: RoutePath.editJobTimeline,
          pageBuilder: (context, state) {
            return MaterialPage(
              name: RouteName.editJobTimeline,
              maintainState: true,
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => EditJobCubit()),
                ],
                child: const EditJobTimeline(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.editJobBudget,
          path: RoutePath.editJobBudget,
          pageBuilder: (context, state) {
            return MaterialPage(
              name: RouteName.editJobBudget,
              maintainState: true,
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(create: (_) => EditJobCubit()),
                ],
                child: const EditJobBudget(),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.applyJob,
          path: RoutePath.applyJob,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => ApplyJobBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadMediaBloc(),
                  ),
                  BlocProvider(
                    create: (context) => UploadAudioVideoCubit(),
                  ),
                ],
                child: VoiceJobApplyScreen(
                    jobId:
                        int.tryParse(state.pathParameters[Params.id] ?? '') ??
                            0),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.jobReview,
          path: RoutePath.jobReview,
          pageBuilder: (context, state) {
            final name = state.pathParameters[Params.name]?.toString() ?? '';
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (_) => JobReviewBloc(),
                  ),
                ],
                child: JobReviewScreen(
                  jobId:
                      int.tryParse(state.pathParameters[Params.id] ?? '') ?? 0,
                  name: Uri.decodeComponent(name),
                ),
              ),
            );
          },
        ),
        GoRoute(
          name: RouteName.jobPayment,
          path: RoutePath.jobPayment,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (_) => JobPaymentBloc(),
                  ),
                ],
                child: JobPaymentScreen(
                  jobId:
                      int.tryParse(state.pathParameters[Params.id] ?? '') ?? 0,
                ),
              ),
            );
          },
        ),
        GoRoute(
          path: RoutePath.notification,
          name: RouteName.notification,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: NotificationListScreen(
                isPrivate: state.pathParameters[Params.isPrivate] == 'true',
              ),
            );
          },
        ),
        GoRoute(
          path: RoutePath.reviews,
          name: RouteName.reviews,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: BlocProvider(
                create: (context) => ReviewBloc(),
                child: ReviewsListScreen(
                    id: int.tryParse(state.pathParameters[Params.id] ?? '') ??
                        0),
              ),
            );
          },
        ),
        GoRoute(
          path: RoutePath.chat,
          name: RouteName.chat,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MessagesScreen(
                jobId: state.pathParameters[Params.jobId],
                groupID: state.pathParameters[Params.groupId],
                idFrom: state.pathParameters[Params.idFrom],
                idTo: state.pathParameters[Params.idTo],
                myName: state.pathParameters[Params.myName],
                myProfileImg: state.pathParameters[Params.myProfileImg],
                needBackBtn: state.pathParameters[Params.needBackBtn] == 'true',
                name: state.pathParameters[Params.name],
                profileImg: state.pathParameters[Params.profileImg],
                jobName: state.pathParameters[Params.jobName],
              ),
            );
          },
        ),
      ],
    ),
    GoRoute(
      name: RouteName.splash,
      path: RoutePath.splash,
      pageBuilder: (context, state) => const MaterialPage(child: SplashScreen()),
    ),
    GoRoute(
      name: RouteName.signUp,
      path: RoutePath.signUp,
      pageBuilder: (context, state) {
        return const MaterialPage(
          child: SignUpScreen(),
        );
      },
      redirect: (_, state) async {
        if (HiveStorageHelper.getData<bool>(
                HiveBoxName.user, HiveKeys.isUserLoggedIn) ??
            false) {
          return RoutePath.dashboard;
        } else {
          return state.matchedLocation;
        }
      },
    ),
    GoRoute(
      name: RouteName.enterInformation,
      path: RoutePath.enterInformation,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => EnterInfoBloc(),
              ),
              BlocProvider(
                create: (context) => UploadMediaBloc(),
              ),
            ],
            child: EnterInformationScreen(
              email: state.pathParameters[Params.email],
            ),
          ),
        );
      },
      redirect: (_, state) async {
        UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
            HiveBoxName.user, HiveKeys.userData);
        if (userDataModel != null ||
            !(userDataModel!.isPhoneVerified ?? true)) {
          return state.matchedLocation;
        } else {
          return RoutePath.login;
        }
      },
    ),
    GoRoute(
      name: RouteName.verifyOtp,
      path: RoutePath.verifyOtp,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => VerifyOtpBloc(),
              ),
              BlocProvider(
                create: (context) => ResendOtpBloc(),
              ),
            ],
            child: OtpVerificationScreen(
              email: state.pathParameters[Params.email],
              phoneNumber: state.pathParameters[Params.phoneNumber],
            ),
          ),
        );
      },
      // redirect: (context, state) async {
      //   UserDataModel? userDataModel = await HiveStorageHelper.getData<UserDataModel>("user", "user_data",);
      //   if (userDataModel == null || !(userDataModel.isPhoneVerified ?? true)) {
      //     return state.path;
      //   }
      // },
    ),
     GoRoute(
          name: RouteName.termsAndConditions,
          path: RoutePath.termsAndConditions,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: const PrivacyAndTermsScreen(isTerms: true),
            );
          },
        ),
     GoRoute(
          name: RouteName.privacyPolicy,
          path: RoutePath.privacyPolicy,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: const PrivacyAndTermsScreen(isTerms: false),
            );
          },
      ),
    GoRoute(
      name: RouteName.createProfile,
      path: RoutePath.createProfile,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => CreateProfileBloc(),
              ),
              BlocProvider(
                create: (context) => StaticDataDropdownBloc(),
              ),
            ],
            child: CreateProfileScreen(
              userType: state.pathParameters[Params.type],
            ),
          ),
        );
      },
      redirect: (_, state) async {
        UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
            HiveBoxName.user, HiveKeys.userData);
        if (userDataModel != null ||
            (userDataModel!.isPhoneVerified ?? false)) {
          return state.matchedLocation;
        } else {
          return RoutePath.login;
        }
      },
    ),
    GoRoute(
      name: RouteName.projectType,
      path: RoutePath.projectType,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => StaticDataDropdownBloc(),
              ),
              BlocProvider(
                create: (context) => ProjectTypeSubmitBloc(),
              ),
            ],
            child: const PreferredProjectTypeScreen(),
          ),
        );
      },
      redirect: (_, state) async {
        UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
            HiveBoxName.user, HiveKeys.userData);
        if (userDataModel != null || (userDataModel!.intermediateStep == 3)) {
          return state.matchedLocation;
        } else {
          return RoutePath.login;
        }
      },
    ),
    GoRoute(
          name: RouteName.vocalCharacteristics,
          path: RoutePath.vocalCharacteristics,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: MultiBlocProvider(
                providers: [
                  BlocProvider(
                    create: (context) => StaticDataDropdownBloc(),
                  ),
                  BlocProvider(
                    create: (context) => VocalCharBloc(),
                  ),
                ],
                child: const VocalCharacteristicScreen(),
              ),
            );
          },
          redirect: (_, state) async {
            UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
                HiveBoxName.user, HiveKeys.userData);
            if (userDataModel != null || (userDataModel!.intermediateStep == 1)) {
              return state.matchedLocation;
            } else {
              return RoutePath.login;
            }
          },
    ),
    GoRoute(
      name: RouteName.uploadSamples,
      path: RoutePath.uploadSamples,
      pageBuilder: (context, state) {
        return MaterialPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => UploadMediaBloc(),
              ),
              BlocProvider(
                create: (context) => UploadSamplesBloc(),
              ),
            ],
            child: const UploadAudioScreen(),
          ),
        );
      },
      redirect: (_, state) async {
        UserDataModel? userDataModel = HiveStorageHelper.getData<UserDataModel>(
            HiveBoxName.user, HiveKeys.userData);
        if (userDataModel != null || (userDataModel!.intermediateStep == 2)) {
          return state.matchedLocation;
        } else {
          return RoutePath.login;
        }
      },
    ),
  ],
);
