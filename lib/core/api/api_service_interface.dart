import 'dart:typed_data';

import 'package:the_voice_directory_flutter/features/apply_job/voice_apply_job/data/apply_job_rq_model.dart';
import 'package:the_voice_directory_flutter/features/common/upload_media/data/pre_signed_url_req_model.dart';
import 'package:the_voice_directory_flutter/features/google_calender/data/request_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/api_response_model.dart';
import 'package:the_voice_directory_flutter/utils/common_models/user_info_req_model.dart';

import '../../features/explore_artists/data/artist_filter_selection.dart';
import '../../features/landing_page/data/model/contact_us_form_model.dart';
import '../../features/post_job/models/job_post_model.dart';
import '../../utils/common_models/send_notification_req_model.dart';

abstract class ApiServiceInterface {
  Future<ApiResponse> signUp(
      {required String email,
      String? deviceId,
      String? registrationId,
      String? deviceName,
      String? deviceOS});

  Future<ApiResponse> login(
      {required String email,
      String? deviceId,
      String? registrationId,
      String? deviceName,
      String? deviceOS});

  Future<ApiResponse> loginWithGoogle({
    //  required String accessToken,
    // required String? idToken,
    required String? serverAuthCode,
    String? deviceId,
    String? registrationId,
    String? deviceName,
    String? deviceOS,
  });

  Future<ApiResponse> loginWithApple({
    required String? token,
    String? firstName,
    String? lastName,
    String? deviceId,
    String? registrationId,
    String? deviceName,
    String? deviceOS,
  });

  Future<ApiResponse> verifyOtp({
    required int otp,
    String? email,
    required String type,
    String? phoneNumber,
  });

  Future<ApiResponse> resendOtp({
    String? email,
    String? phoneNumber,
  });

  Future<ApiResponse> getUserData({int? id});

  Future<ApiResponse> submitUserInfoData(
      {required UserInfoRequestModel userInfoRequestModel});

  Future<ApiResponse> dropDowns({bool? isFlexible});

  Future<ApiResponse> getPreSignedUrl({
    required PreSignedUrlReqModel preSignedUrlReqModel,
  });

  Future<ApiResponse> getUsersList({required String searchName});

  Future<ApiResponse> uploadMedia({
    required String fileUrl,
    required Uint8List uploadFile,
    required String contentType,
  });

  Future<ApiResponse> getApplicationData(int? id);

  Future<ApiResponse> logout();

  Future<ApiResponse> getJobDetail(int? id);

  Future<ApiResponse> postJob({required JobPostModel jobPostModel});

  Future<ApiResponse> getAllJobs({
    String? searchName,
    String? filter,
    String? sortOrder,
    required bool isClient,
  });

  Future<ApiResponse> updateJobDetails({
    required int id,
    required JobPostModel jobPostModel,
  });
  
  Future<ApiResponse> getApplicantList(int jobId, int filterId);

  Future<ApiResponse> deleteJob(int? id);

  Future<ApiResponse> applyJobData(
    {required ApplyJobRequestModel applyJobRequestModel});

  Future<ApiResponse> shortlistCandidateApplication(int applicantId);

  Future<ApiResponse> postJobReview({
    required int jobId,
    required int rating,
    required String feedback,
  });

  Future<ApiResponse> paymentMethod(int jobId,{String? amount});

  Future<ApiResponse> acceptShortlistedApplication(int applicantId);

  Future<ApiResponse> getArtists({
    String? searchName,
    required ArtistFilterSelection filters,
    required bool isFavorite,
  });

  Future<ApiResponse> getNotificationList(bool? isPrivate);

  Future<ApiResponse> readNotification(int? id, String? isReadType);

  Future<ApiResponse> getNotificationCount();

  Future<ApiResponse> getReviewList(int? id);

  Future<ApiResponse> deleteAccount();
  
  Future<ApiResponse> favoriteArtist(int artistId, bool isFavorite);

  Future<ApiResponse> favoriteJob(int jobId);
  
  Future<ApiResponse> getCalenderEvent({
    required String userId,
    required String startDate,
    required String endDate,
  });

  Future<ApiResponse> bookCalenderEvent({required RequestModel requestModel});

  Future<ApiResponse> getServerAuthCode({required String? serverAuthCode, String? deviceType});

  Future<ApiResponse> disconnectCalendar();

  Future<ApiResponse> googleAddress(String? address);

  Future<ApiResponse> sendNotification({required SendNoticationReqModel sendNoticationReqModel});

  Future<ApiResponse> report(int id, String comment);

  Future<ApiResponse> updateJobInvitees({
    required int id,
    required List<int> inviteeIds,
  });

  Future<ApiResponse> getInvitedUserList(int? id);

  Future<ApiResponse> explorePublicVoices({String? searchName, required ArtistFilterSelection filters});

  Future<ApiResponse> topPublicVoices();

  Future<ApiResponse> getPublicVoiceProfileData({int? id});

  Future<ApiResponse> contactSupport({required ContactUsFormModel contactUsFormModel});
}
