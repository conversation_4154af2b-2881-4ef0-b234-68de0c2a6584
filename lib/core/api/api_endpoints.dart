class ApiEndpoints {
  static String signUp() {
    return "/account/signup/";
  }

  static String login() {
    return "/account/login/";
  }

  static String socialLogin() {
    return '/account/social/';
  }

  static String verifyOtp() {
    return "/account/verify-otp/";
  }

  static String resendOtp() {
    return "/account/resend-otp/";
  }

  static String user({int? id}) {
    return id != null ? "/account/profile/$id/" : "/account/profile/";
  }

  static String dropDowns() {
    return "/account/dropdowns/";
  }

  static String getPreSignedUrl() {
    return "/account/generate-presigned-url/";
  }

  static String getUsersList() {
    return "/account/users-list/";
  }

  static String logout() {
    return "/account/logout/";
  }

  static String getJobDetails(int? id) {
    return "/jobs/$id/";
  }

  static String postJob() {
    return "/jobs/";
  }

  static String getAllJobs({required bool isClient}) {
    return isClient ? '/jobs/' : '/jobs/voice/';
  }

  static String applicantList(int jobId) {
    return "/jobs/applicants/$jobId/";
  }

  static String applicationDetail(int? id) {
    return "/jobs/voice-application/$id/";
  }

  static String deleteJob(int? id) {
    return "/jobs/$id/";
  }

  static String applyJob() {
    return "/jobs/voice-application/";
  }

  static String shortlistCandidateApplication(int? id) {
    return '/jobs/voice-application/$id/';
  }

  static String jobReview() {
    return '/jobs/review/';
  }
  static String paymentMethod({String? amount}) {
    return amount != null && amount.isNotEmpty ? '/payments/client-order/' : '/payments/voice-payment/';
  }
    
  static String acceptShortlistedApplication(int? id) {
    return '/jobs/voice-application/$id/';
  }

  static String getVoiceArtists() {
    return '/account/users-list/';
  }

  static String getNotificationList(bool? isPrivate) {
    return isPrivate == true ? '/jobs/notification/?type=private' : '/jobs/notification/';
  }

  static String readNotification(int? id) {
    if (id == null) {
      return '/jobs/notification/';
    }
    return '/jobs/notification/$id/';
  }

  static String getNotificationCount() {
    return '/jobs/notification-count/';
  }

  static String getReviewList(int? id) {
    return '/account/review/$id/';
  }

  static String deleteAccount() {
    return "/account/profile/";
  }
  
  static String favoriteArtist(int artistId) {
    return '/account/favorite/$artistId/';
  }

  static String favoriteJob() {
    return '/jobs/job-favorite/';
  }
    
  static String getCalenderEvent(String userId) {
    return '/account/calendar-events/$userId/';
  }

  static String bookCalenderEvent() {
    return '/account/create-events/';
  }

  static String getServerAuthCode() {
    return '/account/server-auth-code/';
  }

  static String disconnectCalender() {
    return '/account/disconnect-calendar/';
  }

  static String getGoogleAddress() {
    return '/account/location-lookup/';
  }

  static String sendNotification() {
    return '/account/send-notification/';
  }

  static String report(int id) {
    return "/account/report-user/$id/";
  }

  static String getInvitedUserList(int? id) {
    return "/jobs/invited-users/$id/";
  }

  static String explorePublicVoices({int? id}) {
    return id != null ? '/account/voice-users/$id/' : '/account/voice-users/';
  }

  static String topPublicVoices() {
    return '/account/top-voices/';
  }

  static String contactSupport() {
    return '/account/contact-support/';
  }
}
