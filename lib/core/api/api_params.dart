class Params {
  static const email = "email";
  static const deviceId = "device_id";
  static const registrationId = "registration_id";
  static const deviceName = "device_name";
  static const deviceType = "device_type";
  static const deviceOS = "device_os";
  static const otp = "otp";
  static const type = "type";
  static const phoneNumber = "phone_number";
  static const id = 'id';
  static const name = 'name';
  static const authorization = 'Authorization';
  static const profilePicture = 'profile_picture';
  static const cameFrom = 'came_from';
  static const jobId = 'job_id';
  static const applicationStatus = 'application_status';
  static const job = 'job';
  static const rating = 'rating';
  static const feedback = 'feedback';
  static const voicePaymentMethod = 'voice_payment_method';
  static const paymentMethod = 'payment_method';
  static const amount = 'amount';
  static const search = 'search';
  static const gender = 'gender';
  static const projectType = 'project_type';
  static const ageRange = 'age_range';
  static const language = 'voice_language';
  static const accent = 'accent_param';
  static const experience = 'voice_experience';
  static const playerId = 'player_id';
  static const isRead = 'is_read';
  static const isFavorite = 'is_favorite';
  static const favorite = 'favorite';
  static const showApplicantTab = 'show_applicant_tab';
  static const startDate = 'start_date';
  static const endDate = 'end_date';
  static const title = 'title';
  static const serverAuthCode = 'server_auth_code';
  static const provider = 'provider';
  static const needBackBtn = 'nbb';
  static const groupId = 'groupId';
  static const idFrom = 'if';
  static const idTo = 'it';
  static const myName = 'mn';
  static const myProfileImg = 'mpi';
  static const profileImg = 'pi';
  static const jobName = 'jn';
  static const address = 'address';
  static const token = 'token';
  static const firstName = 'first_name';
  static const lastName = 'last_name';
  static const description = 'description';
  static const services = 'services';
  static const userType = 'user_type';
  static const lat = 'lat';
  static const lng = 'long';
  static const associatedUsers = 'associated_users';
  static const isPrivate = 'is_private';
  static const scrollTo = 'scroll_to';
  static const isFlexible = 'is_flexible';
}
