import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/api/interceptor/auth_interceptor.dart';

const _connectionTimeout = 60 * 1000;
const _receiveTimeout = 60 * 1000;

class APIConnectivity {
  late Dio dio;

  APIConnectivity({required String baseURL}) {
    dio = Dio()
      ..options.baseUrl = baseURL
      ..options.connectTimeout = const Duration(milliseconds: _connectionTimeout)
      ..options.receiveTimeout = const Duration(milliseconds: _receiveTimeout);
    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        responseBody: true,
        responseHeader: true,
        requestBody: true,
        error: true,
        requestHeader: true,
        request: true,
      ));
      dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
    }

    dio.interceptors.add(AuthInterceptor(dio));
  }

  void setPartnerGatewayToken(String token) {
    dio.options.headers.addAll({
      Params.authorization: token,
    });
  }

  void removeToken() {
    dio.options.headers.clear();
  }
}
