import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTheme {
  static ThemeData lightThemeData =
      getThemeData(_lightColorScheme);

  static ThemeData getThemeData(ColorScheme colorScheme) {
    return ThemeData(
      scaffoldBackgroundColor: colorScheme.white,
      fontFamily: 'NotoSans',
      brightness: colorScheme.brightness,
      hoverColor: colorScheme.primary.withOpacity(0.2),
      splashColor: colorScheme.primary.withOpacity(0.4),
      colorScheme: colorScheme,
      primaryColor: colorScheme.primary,
      textTheme: _textTheme(colorScheme),
      appBarTheme: const AppBarTheme(),
      radioTheme: RadioThemeData(
        fillColor: WidgetStatePropertyAll(
          colorScheme.secondary,
        ),
      ),
      
datePickerTheme: DatePickerThemeData(
        headerForegroundColor: colorScheme.primaryGrey,
        backgroundColor: colorScheme.white,
        yearBackgroundColor: WidgetStatePropertyAll(
          colorScheme.white,
        ),
        headerHeadlineStyle: TextStyle(
          fontSize: 20.sp,
          fontWeight: _bold,
          fontFamily: 'NotoSans-Bold',
          color: colorScheme.primaryGrey,
        ),
        // Weekday Text Style
        weekdayStyle: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          fontFamily: 'NotoSans-Medium',
          color: colorScheme.primaryGrey,
        ),
        // Date Text Style
        dayStyle: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
          fontFamily: 'NotoSans-Medium',
          color: colorScheme.primaryGrey,
        ),
        yearStyle: TextStyle(color: colorScheme.primaryGrey),
        yearForegroundColor: WidgetStatePropertyAll(colorScheme.primaryGrey),
        dayForegroundColor: WidgetStatePropertyAll(colorScheme.primaryGrey),

        // Selected Date Style
        todayForegroundColor: WidgetStatePropertyAll(
          colorScheme.white,
        ),
        todayBackgroundColor: WidgetStatePropertyAll(
          colorScheme.hyperlinkBlueColor,
        ),
        todayBorder:
            BorderSide(color: colorScheme.hyperlinkBlueColor, width: 2),

      ),

      checkboxTheme: CheckboxThemeData(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6.r),
          ),
          fillColor: WidgetStatePropertyAll(colorScheme.secondary)),
      iconTheme: IconThemeData(color: colorScheme.primaryContainer),
      highlightColor: Colors.transparent,
      bottomSheetTheme:
          const BottomSheetThemeData(backgroundColor: Colors.transparent),
    );
  }

  static const ColorScheme _lightColorScheme = ColorScheme(
    primary: Color(0xffD5E04D),
    onPrimary: Colors.white,
    secondary: Color(0xFF1243B5),
    onSecondary: Color(0xff3A393C),
    surface: Color(0xff3A393C),
    onSurface: Color(0xff3A393C),
    error: Color(0xffF04438),
    onError: Colors.white,
    brightness: Brightness.light,
  );

  static const _medium = FontWeight.w500;
  static const _semiBold = FontWeight.w600;
  static const _bold = FontWeight.w700;

  static TextTheme _textTheme(ColorScheme colorScheme) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 36.sp,
        fontWeight: _bold,
        fontFamily: 'NotoSans-Bold',
        color: colorScheme.primaryGrey,
      ),
      displaySmall: TextStyle(
        fontSize: 24.sp,
        fontWeight: _semiBold,
        fontFamily: 'NotoSans-SemiBold',
        color: colorScheme.primaryGrey,
      ),
      bodyLarge: TextStyle(
        fontSize: 26.sp,
        fontWeight: _semiBold,
        fontFamily: 'NotoSans-SemiBold',
        color: colorScheme.primaryGrey,
      ),
      bodyMedium: TextStyle(
        fontSize: 20.sp,
        fontWeight: _semiBold,
        fontFamily: 'NotoSans-SemiBold',
        color: colorScheme.primaryGrey,
      ),
      bodySmall: TextStyle(
        fontSize: 12.sp,
        fontWeight: _semiBold,
        fontFamily: 'NotoSans-SemiBold',
        color: colorScheme.primaryGrey,
      ),
      titleLarge: TextStyle(
        fontSize: 18.sp,
        fontWeight: _medium,
        fontFamily: 'NotoSans-Medium',
        color: colorScheme.primaryGrey,
      ),
      titleMedium: TextStyle(
        fontSize: 16.sp,
        fontWeight: _medium,
        fontFamily: 'NotoSans-Medium',
        color: colorScheme.primaryGrey,
      ),
      titleSmall: TextStyle(
        fontSize: 14.sp,
        fontWeight: _medium,
        fontFamily: 'NotoSans-Medium',
        color: colorScheme.primaryGrey,
      ),
    );
  }
}

extension AppThemeColors on ColorScheme {
  Color get primaryGrey => const Color(0xff3A393C);
  Color get lightGreyD9D9D9 => const Color(0xffD9D9D9);
  Color get primaryLightF5FF75 => const Color(0xffF5FF75);
  Color get primaryLightFAFFB0 => const Color(0xffFAFFB0);
  Color get white => Colors.white;
  Color get black => Colors.black;
  Color get hintTextColor => const Color(0xffB2B2B2);
  Color get textfieldTitleColor => const Color(0xff3A393C);
  Color get hyperlinkBlueColor => const Color(0xff1243B5);
  Color get redSnackBarOuterBorderColor => const Color(0xffFF9992);
  Color get blueSnackBarOuterBorderColor => const Color(0xff6291FF);
  Color get redSnackBarFillColor => const Color(0xffFFD0CD);
  Color get blueSnackBarFillColor => const Color(0xffD4E1FF);
  Color get red => Colors.red;
  Color get lightGreyB2B2B2 => const Color(0xffB2B2B2);
  Color get lightGreyF2F2F2 => const Color(0xffF2F2F2);
  Color get lightGreyA9ADB3 => const Color(0xffA9ADB3);
  Color get darkGrey525252 => const Color(0xff525252);
  Color get blackE8E8E8 => const Color(0xffE8E8E8);
  Color get lightGreenFDFFDA => const Color(0xffFDFFDA);
  Color get darkgrey494949 => const Color(0xff494949);
  Color get lightShadowF3F3F3 => const Color(0xfff3f3f3);
  Color get lightBlueEBF1FF => const Color(0xffEBF1FF);
  Color get lightBlueDFE9FF => const Color(0xffDFE9FF);
  Color get lightGreyF5F5F5 => const Color(0xffF5F5F5);
  Color get lightBlueECEFFF => const Color(0xffECEFFF);
  Color get lightBlueD2D9FF => const Color(0xffD2D9FF);
  Color get lightGreyE8ECF4 => const Color(0xffE8ECF4);
  Color get lightGreyEDEDED => const Color(0xffEDEDED);


  Color get purple7931FF => const Color(0xFF7931FF);
  Color get orangeFA6400 => const Color(0xFFFA6400);
  Color get green00843E => const Color(0xFF00843E);
  Color get blue0084FF => const Color(0xFF0084FF);
  Color get checkBoxLemonGreen => const Color(0xFFCDD614);
  Color get green13B25D => const Color(0xFF13B25D);
  Color get redFF6257 => const Color(0xFFFF6257);
  Color get yellowFFC500 => const Color(0xFFFFC500);
  Color get black050505 => const Color(0xFF050505);
  Color get redFD3503 => const Color(0xFFFD3503);
  Color get lightGreyF0F0F0 => const Color(0xFFF0F0F0);
  Color get lightGrey5D5D5D => const Color(0xFF5D5D5D);
  Color get lightGreyC8C8C8 => const Color(0xFFC8C8C8);
  Color get blue0A2FFF => const Color(0xFF0A2FFF);
  Color get darkGrey333333 => const Color(0xFF333333);
  Color get lightGreyF9F9F9 => const Color(0xFFF9F9F9);
  Color get lightGrey5C5C5C => const Color(0xFF5C5C5C);
  Color get lightGrey656057 => const Color(0xFF656057);
  Color get lightGrey9497A1 => const Color(0xFF9497A1);
  Color get lightGreyA6A6A6 => const Color(0xFFA6A6A6);
  Color get steelBlue25272D => const Color(0xFF25272D);
  Color get steelGrey6C6D6F => const Color(0xFF6C6D6F);
  Color get lightGreen6EAA21 => const Color(0xFF6EAA21);
}
