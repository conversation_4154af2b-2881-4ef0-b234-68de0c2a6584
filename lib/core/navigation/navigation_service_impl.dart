import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service.dart';

import '../api/api_service.dart';
import '../routes/route_names.dart';
import '../services/hive/hive_keys.dart';
import '../services/hive/hive_storage_helper.dart';

class NavigationServiceImpl extends NavigationService {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  final GlobalKey<NavigatorState> _shellNavigatorKey = GlobalKey<NavigatorState>();
  
  static NavigationServiceImpl? _instance;

  static NavigationService? getInstance() {
    _instance ??= NavigationServiceImpl._();
    return _instance;
  }

  NavigationServiceImpl._();

  @override
  GlobalKey<NavigatorState> getGlobalKey() {
    return _navigatorKey;
  }

  @override
  GlobalKey<NavigatorState> getShellNavigatorKey() {
    return _shellNavigatorKey;
  }

  @override
  Future<void> doLogout() async {
    await FirebaseAuth.instance.signOut();
    
    String? playerId = HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.playerId);
    await HiveStorageHelper.clearBox(HiveBoxName.user);
    if (playerId != null) {
      await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.playerId, playerId);
    }
    
    ApiService.instance.removeToken();

    doNavigation(_navigatorKey.currentContext!, routeName: RouteName.login, forceGo: true);
  }

  @override
  Future<dynamic> doNavigation(
    BuildContext context, {
    required String routeName,
    Map<String, String>? pathParameters,
    dynamic extra,
    bool useGo = false,
    bool forceGo = false,
  }) async {
    if (forceGo) {
      return context.goNamed(routeName, pathParameters: pathParameters ?? {}, extra: extra);
    }
    if (kIsWeb) {
      if (useGo) {
        context.goNamed(routeName, pathParameters: pathParameters ?? {}, extra: extra);
        return null;
      } else {
        return await context.pushNamed(routeName, pathParameters: pathParameters ?? {}, extra: extra);
      }
    } else {
      return await context.pushNamed(routeName, pathParameters: pathParameters ?? {}, extra: extra);
    }
  }
}
