import 'package:flutter/cupertino.dart';

abstract class NavigationService {
  Future<dynamic> doNavigation(
    BuildContext context, {
    required String routeName,
    Map<String, String> pathParameters,
    dynamic extra,
    bool useGo = false,
    bool forceGo = false,
  });

  GlobalKey<NavigatorState> getGlobalKey();

  GlobalKey<NavigatorState> getShellNavigatorKey();

  Future<void> doLogout();
}
