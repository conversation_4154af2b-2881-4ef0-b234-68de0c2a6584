import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/loading_dialog.dart';
import 'package:the_voice_directory_flutter/widgets/textfields/app_textfield.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class Dialogs {
  static showOnlyLoader(BuildContext context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            const Center(
              child: Loader(),
            ),
          ],
        );
      },
    );
  }

  static showCommonDialog({
    required BuildContext context,
    required String title,
    required String message,
    String? primaryButtonText = AppStrings.okay,
    required VoidCallback onPrimaryButtonTap,
    Color? primaryButtonColor,
    Color? primaryButtonBgColor,
    String? cancelButtonText = AppStrings.cancel,
    bool showCancelButton = true,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    // final isDesktop = screenWidth > 600; // Typical desktop breakpoint
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity, // Desktop width
                  minWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextDisplayLarge36And26(
                          title,
                          textAlign: TextAlign.center,
                          fontSize: Responsive.isDesktop(context) ? 24 : 16,
                          fontWeight: FontWeight.w700,
                          color: colorScheme.black),
                      35.ph,
                      TextTitle18And14(
                        message,
                        textAlign: TextAlign.center,
                      ),
                      25.ph,
                      PrimaryButton(
                        backgroundColor: primaryButtonBgColor,
                        textColor: primaryButtonColor,
                        onPressed: onPrimaryButtonTap,
                        buttonText: primaryButtonText,
                        fontWeight: FontWeight.w500,
                        ),
                      if (showCancelButton) ...[
                        !Responsive.isDesktop(context) ? 12.ph : 24.ph,
                        PrimaryButton(
                          backgroundColor: colorScheme.lightGreenFDFFDA,
                          onPressed: () => Navigator.of(context).pop(),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40.r),
                            side: BorderSide(
                              color: colorScheme.primary,
                              width: 1.2.h,
                            ),
                          ),
                          child: const TextTitle18And14(
                            AppStrings.cancel,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  
  static showCommonDialogWithComment({
    required BuildContext context,
    required String title,
    required String message,
    required TextEditingController commentController,
    String? commentHint,
    String? primaryButtonText = AppStrings.okay,
    required VoidCallback onPrimaryButtonTap,
    Color? primaryButtonColor,
    Color? primaryButtonBgColor,
    String? cancelButtonText = AppStrings.cancel,
    bool showCancelButton = true,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    bool showWarning = false;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
                  child: Container(
                    color: Colors.black.withOpacity(0.3),
                  ),
                ),
                Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity,
                      minWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextDisplayLarge36And26(
                              title,
                              textAlign: TextAlign.center,
                              fontSize: Responsive.isDesktop(context) ? 24 : 16,
                              fontWeight: FontWeight.w700,
                              color: colorScheme.black),
                          35.ph,
                         
                          AppTextFormField(
                            controller: commentController,
                            hintText: commentHint,
                            decoration: InputDecoration(
                              hintStyle: TextStyle(
                                color: colorScheme.hintTextColor,
                                fontSize: 10,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: EdgeInsets.all(12),
                            ),
                            maxLines: 3,
                            onChanged: (value) {
                              if (showWarning && value.trim().isNotEmpty) {
                                setState(() {
                                  showWarning = false;
                                });
                              }
                            },
                          ),
                          if (showWarning) ...[
                            3.ph,
                            Align(
                              alignment: Alignment.centerLeft,
                              child: TextTitle14(
                                ValidationMsg.plsEnter('reason for reporting'),
                                color: colorScheme.red,
                              ),
                            ),
                          ],
                          !Responsive.isDesktop(context) ? 12.ph : 24.ph,
                          Row(
                            children: [
                                Expanded(
                                child: PrimaryButton(
                                  backgroundColor: colorScheme.primary,
                                  onPressed: () {
                                    context.pop();
                                    commentController.clear();
                                  },
                                  child: const TextTitle18And14(
                                    AppStrings.cancel,
                                  ),
                                ),
                              ),
                              16.pw,
                              Expanded(
                                child: PrimaryButton(
                                  backgroundColor: colorScheme.lightGreenFDFFDA,
                                  textColor: primaryButtonColor,
                                  onPressed: () {
                                    if (commentController.text.trim().isEmpty) {
                                      setState(() {
                                        showWarning = true;
                                      });
                                    } else {
                                      onPrimaryButtonTap();
                                    }
                                  },
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(40.r),
                                    side: BorderSide(
                                      color: colorScheme.primary,
                                      width: 1.2.h,
                                    ),
                                  ),
                                  buttonText: primaryButtonText,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
