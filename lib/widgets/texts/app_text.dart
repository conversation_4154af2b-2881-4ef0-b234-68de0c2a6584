import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:url_launcher/url_launcher.dart';

class TextDisplayLarge36And26 extends StatelessWidget {
  final String data;
  final TextAlign? textAlign;
  final TextStyle? style;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  const TextDisplayLarge36And26(this.data, {super.key, this.style, this.color, this.fontSize, this.fontWeight, this.textAlign});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.bodyLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSize, color: color)
              : textTheme.displayLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSize, color: color)),
    );
  }
}
class Text24And20SemiBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  const Text24And20SemiBold(this.data, {super.key, this.style, this.color, this.fontSize, this.fontWeight, this.maxLines, this.overflow, this.textAlign});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      maxLines: maxLines,
      overflow: overflow,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.bodyMedium?.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize)
              : textTheme.displaySmall?.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize)),
      textAlign: textAlign,
    );
  }
}

class TextTitle18And14 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final TextDecoration? decoration;
  final Color? decorationColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final String? fontFamily;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  const TextTitle18And14(
       this.data,
      {super.key, this.style, this.color, this.fontWeight, this.fontSize, this.decoration, this.decorationColor, this.fontFamily, this.maxLines, this.textAlign, this.overflow});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      maxLines: maxLines,
      textAlign: textAlign,
      overflow: overflow,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.titleSmall!
                  .copyWith(fontFamily: fontFamily, fontWeight: fontWeight, fontSize: fontSize, color: color, decoration: decoration, decorationColor: decorationColor)
              : textTheme.titleLarge!
                  .copyWith(fontFamily: fontFamily, fontWeight: fontWeight, fontSize: fontSize, color: color, decoration: decoration, decorationColor: decorationColor)),
    );
  }
}


 class TextTitle18And20 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final TextDecoration? decoration;
  final Color? decorationColor;
  const TextTitle18And20(this.data,
      {super.key, this.style, this.color, this.decoration, this.decorationColor});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.titleMedium!
                  .copyWith(color: color, decoration: decoration, decorationColor: decorationColor)
              : textTheme.bodyMedium!
                  .copyWith(color: color, decoration: decoration, decorationColor: decorationColor)),
    );
  }
}

class TextDisplayLarge24And16 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final double? fontSizeMobile;
  final double? fontSizeWeb;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final Color? color;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? height;

  const TextDisplayLarge24And16(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontSizeMobile,
    this.fontSizeWeb,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.bodyLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSizeMobile, color: color, height: height)
              : textTheme.displayLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSizeWeb, color: color, height: height)),
    );
  }
}
class CustomTextFontSizes extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final double? fontSizeMobile;
  final double? fontSizeWeb;
  final FontWeight? fontWeightMobile;
  final FontWeight? fontWeightWeb;
  final Color? color;
  final int? maxLines;
  const CustomTextFontSizes(this.data, {super.key, this.style, this.color, this.fontSizeMobile, this.fontSizeWeb, this.fontWeightMobile, this.fontWeightWeb, this.maxLines});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      maxLines: maxLines,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.bodyLarge?.copyWith(fontWeight: fontWeightMobile, fontSize: fontSizeMobile, color: color)
              : textTheme.bodyLarge?.copyWith(fontWeight: fontWeightWeb, fontSize: fontSizeWeb, color: color)),
    );
  }
}


class TextTitle14 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  const TextTitle14(this.data, {super.key, this.style, this.color, this.fontWeight, this.fontSize, this.textAlign, this.overflow, this.maxLines});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ?? textTheme.titleSmall!.copyWith(
        fontSize: fontSize,
        color: color, fontWeight: fontWeight),
    );
  }
}

class TextBodySmall12 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final double? fontSize;
  final TextOverflow? overflow;
  const TextBodySmall12(this.data, {super.key, this.style, this.color, this.fontSize, this.overflow});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      maxLines: 1,
      overflow: overflow ?? TextOverflow.ellipsis,
      style: style ?? textTheme.bodySmall!.copyWith(color: color, fontSize: fontSize),
    );
  }
}

class TextDisplayLarge20And16 extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  const TextDisplayLarge20And16(this.data, { super.key, this.textAlign, this.style, this.color, this.fontSize, this.fontWeight});
  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      style: style ??
          (!Responsive.isDesktop(context)
              ? textTheme.bodyLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSize ?? 16, color: color)
              : textTheme.displayLarge?.copyWith(fontWeight: fontWeight, fontSize: fontSize ?? 20, color: color)),
    );
  }
}


class ClickableText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double? fontSizeMobile;
  final double? fontSizeWeb;
  final FontWeight? fontWeightMobile;
  final FontWeight? fontWeightWeb;
  final Color? color;
  final int? maxLines;
  final TextOverflow overflow;

  const ClickableText({
    super.key,
    required this.text,
    this.style,
    this.fontSizeMobile,
    this.fontSizeWeb,
    this.fontWeightMobile,
    this.fontWeightWeb,
    this.color,
    this.maxLines,
    this.overflow = TextOverflow.clip,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return RichText(
      text: _getClickableText(
        context,
        textTheme,
        text,
      ),
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  TextSpan _getClickableText(
      BuildContext context, TextTheme textTheme, String text) {
    final RegExp urlRegex = RegExp(
      r"(https?:\/\/[^\s]+)", // Match HTTP/HTTPS URLs
      caseSensitive: false,
    );

    List<TextSpan> spans = [];
    int start = 0;

    for (RegExpMatch match in urlRegex.allMatches(text)) {
      if (match.start > start) {
        spans.add(TextSpan(
          text: text.substring(start, match.start),
          style: (!Responsive.isDesktop(context)
              ? textTheme.bodyLarge?.copyWith(
                  fontWeight: fontWeightMobile,
                  fontSize: fontSizeMobile,
                  color: color)
              : textTheme.bodyLarge?.copyWith(
                  fontWeight: fontWeightWeb,
                  fontSize: fontSizeWeb,
                  color: color)),
        ));
      }

      String url = match.group(0)!;
      spans.add(TextSpan(
        text: url,
        style: (!Responsive.isDesktop(context)
            ? textTheme.bodyLarge?.copyWith(
                decoration: TextDecoration.underline,
                decorationColor:
                    Theme.of(context).colorScheme.hyperlinkBlueColor,
                fontWeight: fontWeightMobile,
                fontSize: fontSizeMobile,
                color: Theme.of(context).colorScheme.hyperlinkBlueColor)
            : textTheme.bodyLarge?.copyWith(
                decoration: TextDecoration.underline,
                decorationColor:
                    Theme.of(context).colorScheme.hyperlinkBlueColor,
                fontWeight: fontWeightWeb,
                fontSize: fontSizeWeb,
                color: Theme.of(context).colorScheme.hyperlinkBlueColor)),
        recognizer: TapGestureRecognizer()
          ..onTap = () async {
            if (await canLaunchUrl(Uri.parse(url))) {
              await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
            }
          },
      ));

      start = match.end;
    }

    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: (!Responsive.isDesktop(context)
            ? textTheme.bodyLarge?.copyWith(
                fontWeight: fontWeightMobile,
                fontSize: fontSizeMobile,
                color: color)
            : textTheme.bodyLarge?.copyWith(
                fontWeight: fontWeightWeb,
                fontSize: fontSizeWeb,
                color: color)),
      ));
    }

    return TextSpan(children: spans);
  }
}
