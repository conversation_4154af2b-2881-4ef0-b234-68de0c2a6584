import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class CustomDropDownWidget<T> extends StatefulWidget {
  final List<DropdownMenuItem<T>>? items;
  final T? value;
  final String? selectedValue;
  final bool isLoading;
  final bool isError;
  final String? hintText;
  final ValueChanged<T?>? onChanged;
  final String? titleText;
  final bool isParentField;
  final bool readOnly;

  const CustomDropDownWidget({
    super.key,
    required this.items,
    required this.value,
    this.onChanged,
    this.titleText,
    this.isLoading = false,
    this.isError = false,
    this.hintText,
    this.selectedValue,
    this.isParentField = false,
    this.readOnly = false,
  });

  @override
  State<CustomDropDownWidget<T>> createState() =>
      _CustomDropDownWidgetState<T>();
}

class _CustomDropDownWidgetState<T> extends State<CustomDropDownWidget<T>> {
  bool isOpen = false;
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;
    if (widget.isLoading) {
      return Shimmer.fromColors(
        baseColor: colorScheme.lightGreyF2F2F2,
        highlightColor: colorScheme.white,
        child: Container(
          width: double.infinity,
          height: 56.h,
          decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(12.r)),
        ),
      );
    }
    if (widget.isError) {
      return Column(
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.lightGreyD9D9D9,
                width: 1.2,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            padding: !Responsive.isDesktop(context)
                ? const EdgeInsets.fromLTRB(10, 8, 12, 8)
                : EdgeInsets.all(10.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TextTitle14(
                  color: colorScheme.red,
                  "Error occured while loading data",
                  textAlign: TextAlign.center,
                  
                ),
                5.pw,
                InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    
                  },
                  child: Icon(
                    Icons.refresh,
                    size: 25,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.titleText != null) ...[
          widget.isParentField
              ? Text24And20SemiBold(widget.titleText!)
              : TextTitle14(widget.titleText!),
          widget.isParentField
              ? 16.ph
              : !Responsive.isDesktop(context)
                  ? 4.ph
                  : 8.ph,
        ],
        DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
            customButton: Container(
              width: double.infinity,
              padding: !Responsive.isDesktop(context)
                  ? const EdgeInsets.fromLTRB(12, 8, 12, 8)
                  : EdgeInsets.all(12.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: colorScheme.lightGreyD9D9D9,
                  width: 1.2,
                ),
                color: widget.readOnly ? colorScheme.lightGreyD9D9D9 : null,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextTitle18And14(
                    widget.selectedValue ?? widget.hintText ?? "",
                    color: widget.value != null
                        ? colorScheme.primaryGrey
                        : colorScheme.hintTextColor,
                  ),
                  Icon(
                    isOpen
                        ? Icons.keyboard_arrow_up_rounded
                        : Icons.keyboard_arrow_down_rounded,
                    color: colorScheme.primaryGrey,
                  )
                ],
              ),
            ),
            items: widget.readOnly ? null : widget.items,
            value: widget.value,
            onChanged: widget.readOnly ? null : widget.onChanged,
            buttonStyleData: const ButtonStyleData(
                overlayColor: WidgetStatePropertyAll(Colors.transparent)),
            dropdownStyleData: DropdownStyleData(
              elevation: 0,
              decoration: BoxDecoration(
                color: colorScheme.white,
                border: Border.all(
                  color: colorScheme.lightGreyD9D9D9,
                  width: 1.0,
                  strokeAlign: BorderSide.strokeAlignOutside,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              maxHeight: 300,
            ),
            onMenuStateChange: widget.readOnly
                ? null
                : (isOpen) {
                    this.isOpen = isOpen;
                    setState(() {});
                  },
            menuItemStyleData: MenuItemStyleData(
                height: 56.h,
                overlayColor:
                    WidgetStatePropertyAll(colorScheme.lightGreenFDFFDA)),
          ),
        ),
      ],
    );
  }
}
