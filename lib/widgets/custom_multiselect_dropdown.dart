import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class CustomMultiselectDropdown<T> extends StatefulWidget {
  final List<T> items; // List of objects (e.g., List<SportsModel>)
  final List<T> selectedValues; // Selected items
  final ValueChanged<List<T>>
      onSelectionChanged; // Callback when selection changes
  final bool isLoading;
  final bool isError;
  final String? hintText;
  final String? titleText;
  final bool isParentField;
  const CustomMultiselectDropdown(
      {super.key,
      required this.items,
      required this.selectedValues,
      required this.isLoading,
      required this.isError,
      this.hintText,
      this.titleText,
      this.isParentField =false,
      required this.onSelectionChanged});

  @override
  State<CustomMultiselectDropdown<T>> createState() =>
      _CustomMultiselectDropdownState<T>();
}

class _CustomMultiselectDropdownState<T>
    extends State<CustomMultiselectDropdown<T>> {
  bool isOpen = false;
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;
    if (widget.isLoading) {
      return Shimmer.fromColors(
        baseColor: colorScheme.lightGreyF2F2F2,
        highlightColor: colorScheme.white,
        child: Container(
          width: double.infinity,
          height: 56.h,
          decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(12.r)),
        ),
      );
    }
    if (widget.isError) {
      return Column(
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.lightGreyD9D9D9,
                width: 1.2,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            padding: !Responsive.isDesktop(context)
                ? const EdgeInsets.fromLTRB(10, 8, 12, 8)
                : EdgeInsets.all(10.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TextTitle14(
                  color: colorScheme.red,
                  "Error occured while loading data",
                  textAlign: TextAlign.center,
                  
                ),
                5.pw,
                InkWell(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () {
                    
                  },
                  child: Icon(
                    Icons.refresh,
                    size: 25,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         if (widget.titleText != null) ...[
          widget.isParentField
              ? Text24And20SemiBold(widget.titleText!)
              : TextTitle14(widget.titleText!),
          widget.isParentField
              ? 16.ph
              : !Responsive.isDesktop(context)
                  ? 4.ph
                  : 8.ph,
        ],
        DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
            isExpanded: true,
            customButton: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border:
                    Border.all(color: colorScheme.lightGreyD9D9D9, width: 1.2),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.hintText ?? "Select items",
                    style: TextStyle(color: colorScheme.hintTextColor),
                  ),
                  Icon(
                    isOpen
                        ? Icons.keyboard_arrow_up_rounded
                        : Icons.keyboard_arrow_down_rounded,
                    color: colorScheme.primaryGrey,
                  )
                ],
              ),
            ),
            items: widget.items.map((item) {
              return DropdownMenuItem<T>(
                value: item,
                enabled: false, // Disable default onTap
                child: StatefulBuilder(
                  builder: (context, menuSetState) {
                    final isSelected = widget.selectedValues.contains(item);
                    return InkWell(
                      splashColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          isSelected
                              ? widget.selectedValues.remove(item)
                              : widget.selectedValues.add(item);
                        });
                        menuSetState(() {});
                        widget.onSelectionChanged(widget.selectedValues);
                      },
                      child: SizedBox(
                        height: 56.h,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.h),
                          child: Row(
                            children: [
                              isSelected
                                  ? SvgPicture.asset(
                                      AppImages.selectedCheckboxIc)
                                  : SvgPicture.asset(AppImages.emptyCheckboxIc),
                              const SizedBox(width: 16),
                              TextTitle18And14(
                                (item as dynamic).name ?? "",
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            }).toList(),
            value: widget.selectedValues.isEmpty
                ? null
                : widget.selectedValues.first,
            onChanged: (_) {}, // Not needed since we handle selection manually
            buttonStyleData: const ButtonStyleData(
                overlayColor: WidgetStatePropertyAll(Colors.transparent)),
            menuItemStyleData: MenuItemStyleData(
              padding: EdgeInsets.zero,
              height: 56.h,
              overlayColor:
                  WidgetStatePropertyAll(colorScheme.lightGreenFDFFDA),
            ),
            dropdownStyleData: DropdownStyleData(
              elevation: 0,
              decoration: BoxDecoration(
                color: colorScheme.white,
                border:
                    Border.all(color: colorScheme.lightGreyD9D9D9, width: 1.0),
                borderRadius: BorderRadius.circular(12),
              ),
              maxHeight: 300,
            ),
          ),
        ),
      ],
    );
  }
}
