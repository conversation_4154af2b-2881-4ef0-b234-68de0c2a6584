import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class WelcomeToTVDScreen extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final VoidCallback onButtonPressed;

  const WelcomeToTVDScreen({
    super.key,
    required this.title,
    required this.message,
    required this.buttonText,
    required this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20.r),
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: !Responsive.isDesktop(context)
            ? MainAxisSize.min
            : MainAxisSize.max,
        children: [
          if(Responsive.isDesktop(context)) 80.ph,
          SvgPicture.asset(AppImages.successImg),
          Responsive.isDesktop(context) ? 44.ph : 16.ph,
          TextDisplayLarge36And26(
            title,
            textAlign: TextAlign.center,
            fontSize: !Responsive.isDesktop(context) ? 20.sp : 28.sp,
          ),
          Responsive.isDesktop(context) ? 20.ph : 8.ph,
          TextTitle14(
            message,
            textAlign: TextAlign.center,
            fontSize: !Responsive.isDesktop(context) ? 14.sp : 16.sp,
          ),
          Responsive.isDesktop(context) ? const Spacer() : 20.ph,
            PrimaryButton(
              onPressed: onButtonPressed,
              buttonText: AppStrings.continueBtnText,
            ),
        ],
      ),
    );
  }
}
