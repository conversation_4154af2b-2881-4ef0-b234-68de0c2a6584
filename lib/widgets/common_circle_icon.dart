import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';

class CommonCircleIcon extends StatelessWidget {
  final String iconPath;
  final double? containerSize;
  final double? iconSize;
  final Color? iconColor;
  final VoidCallback? onTap;
  final Color? borderColor;
  final Color? backgroundColor;
  final bool removeHoverEffect;

  const CommonCircleIcon({
    super.key,
    required this.iconPath,
    this.containerSize,
    this.iconSize,
    this.iconColor,
    this.onTap,
    this.borderColor,
    this.backgroundColor,
    this.removeHoverEffect = false, // Default is false - will show normal InkWell effects
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;

    final defaultContainerSize = !Responsive.isDesktop(context) ? 32.h : 40.h;
    final defaultIconSize = !Responsive.isDesktop(context) ? 20.h : 24.h;

    Widget iconWidget = Container(
      height: containerSize ?? defaultContainerSize,
      width: containerSize ?? defaultContainerSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? colorScheme.white,
        border: Border.all(
          color: borderColor ?? colorScheme.lightGreyD9D9D9,
          width: !Responsive.isDesktop(context) ? 1 : 1.5,
        ),
      ),
      child: Center(
        child: SvgPicture.asset(
          iconPath,
          height: iconSize ?? defaultIconSize,
          width: iconSize ?? defaultIconSize,
          colorFilter: iconColor != null
              ? ColorFilter.mode(iconColor!, BlendMode.srcIn)
              : null,
        ),
      ),
    );

    Widget clickableWidget = removeHoverEffect
        ? InkWell(
            onTap: onTap,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            hoverColor: Colors.transparent,
            child: iconWidget,
          )
        : InkWell(
            onTap: onTap,
            child: iconWidget,
          );

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: clickableWidget,
    );
  }
}
