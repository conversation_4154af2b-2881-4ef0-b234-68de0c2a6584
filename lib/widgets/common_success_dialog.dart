import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

void showSuccessDialog({
  required BuildContext context,
  required String imagePath,
  required String description,
  void Function()? onPressed,
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                !Responsive.isDesktop(context) ? 16.r : 20.r),
          ),
          backgroundColor: Theme.of(context).colorScheme.white,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: !Responsive.isDesktop(context) ? 16.h : 36.h,
                vertical: !Responsive.isDesktop(context) ? 16.h : 36.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                !Responsive.isDesktop(context) ? 37.ph : 9.ph,
                SvgPicture.asset(imagePath),
                !Responsive.isDesktop(context) ? 16.ph : 34.ph,
                TextDisplayLarge24And16(
                  description,
                  textAlign: TextAlign.center,
                ),
                !Responsive.isDesktop(context) ? 24.ph : 40.ph,
                PrimaryButton(
                  width: !Responsive.isDesktop(context) ? 263.w : 306.w,
                  onPressed: onPressed ?? () => Navigator.pop(context),
                  buttonText: AppStrings.okay,
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
