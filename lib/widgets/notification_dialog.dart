import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/services/hive/hive_keys.dart';
import '../core/services/hive/hive_storage_helper.dart';
import '../features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/notification/presentation/notification_list_screen.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class NotificationDialog extends StatelessWidget {
  final bool isPrivate;
  const NotificationDialog({super.key, this.isPrivate = false});

  @override
  Widget build(BuildContext context) {
    final userData = HiveStorageHelper.getData<UserDataModel>(
      HiveBoxName.user, HiveKeys.userData
    );
    
    final double menuWidth = 400.0.w;
    final double menuHeight = MediaQuery.of(context).size.height * 0.7.h;
    
    // Fixed position from top
    final double topPosition = 99.h;
    final double rightPosition = 20.w;
    
    return Stack(
      children: [
        // Triangle indicator
        Positioned(
          top: topPosition - 5.h,  // Adjust vertical position
          right: rightPosition + (userData?.role == UserType.client ? 330.w : (isPrivate) ? 175.w : 280.w),  // Changed from 'right' to 'left'
          child: Material(
            elevation: 8,
            color: Colors.transparent,
            shadowColor: Colors.black.withOpacity(0.1),
            child: CustomPaint(
              size: Size(16.w, 10.h),  // Adjust triangle size if needed
              painter: TrianglePainter(
                color: Theme.of(context).colorScheme.textfieldTitleColor,
              ),
            ),
          ),
        ),
        Positioned(
          top: topPosition,
          right: rightPosition,  // Changed from 'right' to 'left'
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: menuWidth,
              height: menuHeight,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.textfieldTitleColor,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10.r,
                    spreadRadius: 2.r,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Notification header
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 16.h,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.r),
                        topRight: Radius.circular(12.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextTitle18And20(
                          isPrivate ? AppStrings.privateInvite : AppStrings.notifications,
                          color: Theme.of(context).colorScheme.white,
                        ),
                        IconButton(
                          padding: EdgeInsets.zero,
                          color: Theme.of(context).colorScheme.white,
                          constraints: const BoxConstraints(),
                          icon: Icon(Icons.close, size: 20.sp),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(12.r),
                        bottomRight: Radius.circular(12.r),
                      ),
                      child: NotificationListScreen(isDialog: true, isPrivate: isPrivate),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(0, size.height)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);

    // Draw shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, shadowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 
