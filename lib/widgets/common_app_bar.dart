import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_keys.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';

import '../core/navigation/navigation_service_impl.dart';
import '../core/routes/route_names.dart';
import '../core/theme/app_theme.dart';
import '../utils/string_constants/app_strings.dart';
import 'common_notification_icon.dart';
import 'texts/app_text.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showNotificationIcon;
  final bool showLoginButton;

  const CommonAppBar({
    super.key,
    required this.title,
    this.showNotificationIcon = false,
    this.showLoginButton = false,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return AppBar(
      automaticallyImplyLeading: false,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      backgroundColor: colorScheme.primary,
      elevation: 0,
      titleSpacing: 0,
      centerTitle: false,
      toolbarHeight: kToolbarHeight + 48.0.h,
      title: Padding(
        padding: EdgeInsets.only(top: 20.h, left: 20.w, right: 20.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            TextDisplayLarge24And16(
              title,
              style: textTheme.bodyLarge?.copyWith(
                fontSize: 26.sp,
              ),
            ),
            if (UserType.voice == HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData)?.role && showNotificationIcon) ...[
              Spacer(),
              const CommonNotificationIcon(isPrivate: true),
              20.pw,
            ],
            if (showNotificationIcon) const CommonNotificationIcon(),
            if (showLoginButton) ...[
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onTap: () {
                  NavigationServiceImpl.getInstance()!.doNavigation(
                    context,
                    routeName: RouteName.login,
                    useGo: true,
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(40.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  child: Text(
                    AppStrings.login,
                    style: textTheme.titleSmall?.copyWith(
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 48.0.h); // Height of AppBar + Tab bar
}
