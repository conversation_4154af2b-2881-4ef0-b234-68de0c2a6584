import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/resend_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_bloc.dart';
import 'package:the_voice_directory_flutter/features/verifyOtp/bloc/verify_otp_state.dart';
import 'package:the_voice_directory_flutter/utils/custom_toast.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/dialogs.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class OtpDialog extends StatefulWidget {
  final String? phoneNumber;

  const OtpDialog({super.key, this.phoneNumber});

  @override
  State<OtpDialog> createState() => _OtpDialogState();
}

class _OtpDialogState extends State<OtpDialog> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController _otpController;
  Timer? _resendTimer;
  int _timeLeft = 30;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _otpController = TextEditingController();
    startResendTimer();
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    super.dispose();
  }

  void startResendTimer() {
    setState(() {
      _timeLeft = 30;
      _canResend = false;
      _otpController.clear();
    });

    if (_resendTimer?.isActive ?? false) {
      _resendTimer?.cancel();
    }

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }

  String get resendText {
    if (_canResend) {
      return 'Resend Code';
    }
    return 'Resend code in ${_timeLeft}s';
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Dialog(
      child: Stack(children: [
        BlocListener<VerifyOtpBloc, VerifyOtpState>(
          listener: (context, state) {
            if (state is VerifyOtpLoadingState) {
              Dialogs.showOnlyLoader(context);
            }
            if (state is VerifyOtpSuccessState) {
              context.pop(true);
              context.pop(true);
            }
            if (state is VerifyOtpErrorState) {
              context.pop();
              CustomToast.show(
                context: context,
                message: state.errorMsg,
                isSuccess: false,
              );
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 335.w),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const TextTitle18And20(AppStrings.verifyOTP),
                      SizedBox(height: 20.h),
                      TextTitle14(
                      AppStrings.pleaseEnter6DigitOtp("phone number")),
                      SizedBox(height: 20.h),
                      PinCodeTextField(
                        appContext: context,
                        length: 6,
                        controller: _otpController,
                        showCursor: true,
                        cursorColor: Theme.of(context).primaryColor,
                        keyboardType: TextInputType.number,
                        errorTextSpace: 20,
                        hintCharacter: '-',
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                        ],
                        textStyle: Theme.of(context).textTheme.bodySmall,
                        pinTheme: PinTheme(
                          shape: PinCodeFieldShape.box,
                          fieldHeight:
                              !Responsive.isDesktop(context) ? 40.h : 44.h,
                          fieldWidth:
                              !Responsive.isDesktop(context) ? 40.w : 44.w,
                          borderRadius: BorderRadius.circular(8.r),
                          selectedColor: Theme.of(context).primaryColor,
                          inactiveColor: Colors.grey,
                          activeColor: Theme.of(context).primaryColor,
                        ),
                      ),
                      20.ph,
                      GestureDetector(
                        onTap: _canResend
                            ? () {
                                context.read<ResendOtpBloc>().resendOtp(
                                      phoneNumber: widget.phoneNumber != "ND"
                                          ? widget.phoneNumber
                                          : null,
                                    );
                                startResendTimer();
                              }
                            : null,
                        child: TextTitle14(
                          resendText,
                          color: _canResend
                              ? Theme.of(context).colorScheme.hyperlinkBlueColor
                              : Theme.of(context).colorScheme.darkgrey494949,
                        ),
                      ),
                      20.ph,
                      PrimaryButton(
                        width: 110.w,
                        buttonText: AppStrings.save,
                        onPressed: () {
                          if (formKey.currentState!.validate()) {
                            context.read<VerifyOtpBloc>().verifyOtp(
                                otp: int.parse(_otpController.text.trim()),
                                type: Params.phoneNumber,
                                phoneNumber: widget.phoneNumber != "ND"
                                    ? widget.phoneNumber
                                    : null);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          top: 8.h,
          right: 8.w,
          child: InkWell(
            onTap: () => context.pop(),
            child: Icon(Icons.close, color: colorScheme.black),
          ),
        ),
      ]),
    );
  }
}
