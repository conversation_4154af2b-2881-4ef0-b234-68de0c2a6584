import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class PrimaryButton extends StatelessWidget {
  final String? buttonText;
  final Widget? child;
  final double? height;
  final Color? backgroundColor;
  final Color? textColor;
  final void Function() onPressed;
  final FontWeight? fontWeight;
  final OutlinedBorder? shape;
  final double? width;
  final int? maxLines;
  final TextOverflow? overflow;
  final EdgeInsets? padding;
  final bool shadowColor;
  final double? elevation;

  const PrimaryButton({
    super.key,
    required this.onPressed,
    this.buttonText,
    this.child,
    this.backgroundColor,
    this.textColor,
    this.shape,
    this.width,
    this.height,
    this.fontWeight,
    this.maxLines,
    this.overflow,
    this.padding,
    this.shadowColor = true,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        shape: shape,
        fixedSize: Size(
          width ?? double.maxFinite,
          height ?? (Responsive.isDesktop(context) ? 56.h : 44.h),
        ),
        backgroundColor: backgroundColor ?? colorScheme.primary,
        elevation: elevation ?? 0,
        padding: padding,
        shadowColor: shadowColor ? null : Colors.transparent,
      ),
      onPressed: onPressed,
      child: child ??
          TextTitle18And14(
            buttonText ?? "",
            color: textColor,
            fontWeight: fontWeight,
            maxLines: maxLines,
            overflow: overflow,
          ),
    );
  }
}
