import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/core/api/api_params.dart';
import 'package:the_voice_directory_flutter/core/navigation/navigation_service_impl.dart';
import 'package:the_voice_directory_flutter/core/routes/route_names.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_count_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_count_state.dart';
import 'package:the_voice_directory_flutter/features/notification/presentation/notification_list_screen.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import '../core/theme/app_theme.dart';
import '../utils/string_constants/app_images.dart';
import '../utils/string_constants/app_strings.dart';
import 'texts/app_text.dart';

class CommonNotificationIcon extends StatefulWidget {
  final double height;
  final double width;
  final VoidCallback? onTap;
  final bool isPrivate;

  const CommonNotificationIcon({
    super.key,
    this.height = 48,
    this.width = 48,
    this.onTap,
    this.isPrivate = false,
  });

  @override
  State<CommonNotificationIcon> createState() => _CommonNotificationIconState();
}

class _CommonNotificationIconState extends State<CommonNotificationIcon> {

  @override
  void initState() {
    super.initState();
    context.read<NotificationCountBloc>().getNotificationCount();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: widget.onTap ??
          () {
            if (Responsive.isMobile(context)) {
              NavigationServiceImpl.getInstance()!.doNavigation(context,routeName: RouteName.notification,
                    pathParameters: { Params.isPrivate: widget.isPrivate.toString() });
            } else {
              NotificationListScreen.showAsDialog(context, isPrivate: widget.isPrivate);
            }
            // context.pushNamed(RouteName.notification);
          },
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: !Responsive.isDesktop(context)
            ? BoxDecoration(
          color: colorScheme.white, shape: BoxShape.circle) : null,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset( widget.isPrivate == true ? AppImages.privateIc : AppImages.notificationIc,
                    width: 28.w, height: 28.h,
                   ),
                  if (!!Responsive.isDesktop(context)) ...[
                    2.ph,
                    TextTitle14(
                      (widget.isPrivate == true) ? AppStrings.privateInvite : AppStrings.notifications,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      overflow: TextOverflow.ellipsis,
                    ),
                    ),
                  ]
                ],
              ),
            ),
            BlocBuilder<NotificationCountBloc, NotificationCountState>(
              builder: (context, state) {
                if (state is NotificationCountSuccessState) {
                  final count = widget.isPrivate
                      ? state.notificationCount.privateUnreadCount
                      : state.notificationCount.othersUnreadCount;
                  return count != null && count > 0
                      ? Positioned(
                          right: Responsive.isMobile(context) ? 6.w : 42.w,
                          top: Responsive.isMobile(context) ? 9.h : 26.h,
                          child: Container(
                            padding: EdgeInsets.all(4.r),
                            decoration: BoxDecoration(
                              color: colorScheme.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                        )
                      : SizedBox();
                }
                return SizedBox();
              },
            ),
          ],
        ),
      ),
    );
  }
}
