import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';
import '../utils/responsive.dart';

class RadioContainer<T> extends StatelessWidget {
  final String title;
  final T value;
  final T? groupValue;
  final void Function(T?) onChanged;
  final FontWeight? fontWeight;
  final double? width;

  const RadioContainer({
    super.key,
    required this.groupValue,
    required this.onChanged,
    required this.value,
    required this.title,
    this.fontWeight,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      onTap: () => onChanged(value),
      child: Container(
          width: width,
          height: !Responsive.isDesktop(context) ? 44.h : 56.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: groupValue == value
                  ? colorScheme.primary
                  : colorScheme.lightGreyD9D9D9,
            ),
          ),
          child: Row(
            children: [
              Radio<T>(
                  value: value, groupValue: groupValue, onChanged: onChanged),
              TextTitle18And14(title, fontWeight: fontWeight),
            ],
          )),
    );
  }
}
