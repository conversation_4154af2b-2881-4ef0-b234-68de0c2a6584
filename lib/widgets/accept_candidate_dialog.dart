import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';
import 'package:the_voice_directory_flutter/widgets/buttons/primary_button.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class AcceptCandidateDialog {
  static showAcceptCandidateDialog({
    required BuildContext context,
    required String title,
    required String message,
    required double amount,
    String? primaryButtonText = AppStrings.okay,
    required VoidCallback onPrimaryButtonTap,
    Color? primaryButtonColor,
    String? cancelButtonText = AppStrings.cancel,
    bool showCancelButton = true,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    // final isDesktop = screenWidth > 600; // Typical desktop breakpoint
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity, // Desktop width
                  minWidth: Responsive.isDesktop(context) ? screenWidth * 0.3 : double.infinity,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextDisplayLarge36And26(
                          title,
                          textAlign: TextAlign.center,
                          fontSize: Responsive.isDesktop(context) ? 24 : 16,
                          fontWeight: FontWeight.w700,
                          color: colorScheme.black),
                      20.ph,
                      TextTitle18And14(
                        'Review quotation: INR $amount',
                        textAlign: TextAlign.center,
                        fontWeight: FontWeight.w400,
                        color: colorScheme.darkGrey525252,
                      ),
                      20.ph,
                      SvgPicture.asset(
                        AppImages.infoCircle,
                        height: 32.h,
                        width: 32.w,
                      ),
                      4.ph,
                      TextTitle18And14(
                        message,
                        color: colorScheme.red,
                        textAlign: TextAlign.center,
                        fontWeight: FontWeight.w500,
                      ),
                      24.ph,
                      PrimaryButton(
                        onPressed: onPrimaryButtonTap,
                        buttonText: primaryButtonText,
                        fontWeight: FontWeight.w500,
                        ),
                      if (showCancelButton) ...[
                        !Responsive.isDesktop(context) ? 12.ph : 24.ph,
                        PrimaryButton(
                          backgroundColor: colorScheme.lightGreenFDFFDA,
                          onPressed: () => Navigator.of(context).pop(),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40.r),
                            side: BorderSide(
                              color: colorScheme.primary,
                              width: 1.2.h,
                            ),
                          ),
                          child: const TextTitle18And14(
                            AppStrings.cancel,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
