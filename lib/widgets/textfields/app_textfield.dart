import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

class AppTextFormField extends StatelessWidget {
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool readOnly;
  final String? labelText;
  final void Function(String)? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final FormFieldValidator<String>? validator;
  final int? inputCharacterLimit;
  final bool isAutovalidateModeOn;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final String? hintText;
  final Color? cursorColor;
  final bool? isenabled;
  final bool isParentField;
  final bool? filled;
  final Color? fillColor;
  final Color? textColor;
  final String? initialValue;
  final Color? enabledBorderColor;
  final int? maxLength;
  final String? suffixText;
  final InputDecoration? decoration;
  final void Function(String str)? onSubmitted;
  final void Function()? onTap;
  final String? titleText;
  final String? indicatorText;
  final FocusNode? focusNode;
  final BoxConstraints? prefixIconConstraints;
  final Color? hintTextColor;
  final FontWeight? hintTextFontWeight;
  final Widget? trailingTitleTextWidget;
  final double? width;
  final TextCapitalization? textCapitalization;
  final TextInputType? textInputType;
  final TextAlignVertical? textAlignVertical;
  final int? minLines;

  const AppTextFormField({
    super.key,
    this.controller,
    this.keyboardType,
    this.readOnly = false,
    this.labelText,
    this.validator,
    this.inputCharacterLimit,
    this.isAutovalidateModeOn = false,
    this.textInputAction,
    this.prefixIcon,
    this.prefixText,
    this.suffixIcon,
    this.onChanged,
    this.inputFormatters,
    this.maxLines,
    this.isenabled,
    this.onSubmitted,
    this.hintText,
    this.cursorColor,
    this.contentPadding,
    this.decoration,
    this.textColor,
    this.filled,
    this.fillColor,
    this.enabledBorderColor,
    this.maxLength = 350,
    this.suffixText,
    this.initialValue,
    this.titleText,
    this.focusNode,
    this.prefixIconConstraints,
    this.isParentField = false,
    this.onTap,
    this.indicatorText,
    this.hintTextColor,
    this.hintTextFontWeight,
    this.trailingTitleTextWidget,
    this.width,
    this.textCapitalization,
    this.textInputType,
    this.textAlignVertical,
    this.minLines,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    TextTheme textTheme = theme.textTheme;
    ColorScheme colorScheme = theme.colorScheme;
    return SizedBox(
      width: width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (titleText != null) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (isParentField) ...[
                  Text(
                    titleText!,
                    style: (!Responsive.isDesktop(context)
                        ? textTheme.bodyMedium
                        : textTheme.displaySmall),
                  ),
                  if (indicatorText != null && indicatorText!.isNotEmpty) ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          indicatorText!,
                          style: (!Responsive.isDesktop(context)
                              ? textTheme.titleSmall!.copyWith(
                                  fontFamily: 'NotoSans-SemiBold',
                                  fontWeight: FontWeight.w600,
                                )
                              : textTheme.titleLarge!.copyWith(
                                  fontFamily: 'NotoSans-SemiBold',
                                  fontWeight: FontWeight.w600,
                                )),
                        ),
                        if (trailingTitleTextWidget != null) ...[
                          6.pw,
                          trailingTitleTextWidget!,
                        ],
                      ],
                    ),
                  ],
                ] else ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TextTitle14(titleText!),
                      if (indicatorText != null &&
                          indicatorText!.isNotEmpty &&
                          trailingTitleTextWidget != null) ...[
                        6.pw,
                        trailingTitleTextWidget!,
                      ],
                    ],
                  ),
                ],
              ],
            ),
            isParentField
                ? 16.ph
                : (!Responsive.isDesktop(context) ? 4.ph : 8.ph),
          ],
          TextFormField(
            focusNode: focusNode,
            initialValue: initialValue,
            enabled: isenabled,
            onTap: onTap,
            controller: controller,
            keyboardType: keyboardType ?? TextInputType.text,
            readOnly: readOnly,
            validator: validator,
            textInputAction: textInputAction ?? TextInputAction.next,
            onChanged: onChanged,
            cursorColor: cursorColor,
            inputFormatters: inputFormatters,
              autovalidateMode: isAutovalidateModeOn
                  ? AutovalidateMode.always
                  : AutovalidateMode.disabled,
            onFieldSubmitted: onSubmitted,
            maxLines: maxLines,
            minLines: minLines,
            maxLength: maxLength,
            style: !Responsive.isDesktop(context)
                ? textTheme.titleSmall
                : textTheme.titleLarge,
            decoration: InputDecoration(
              prefixIconConstraints: prefixIconConstraints,
              hintText: hintText,
              hintMaxLines: 1,
              counterText: "",
              hintStyle: !Responsive.isDesktop(context)
                  ? textTheme.titleSmall!
                      .copyWith(color: hintTextColor ?? colorScheme.hintTextColor, fontWeight: hintTextFontWeight)
                  : textTheme.titleLarge!
                      .copyWith(color: hintTextColor ?? colorScheme.hintTextColor, fontWeight: hintTextFontWeight),
              suffixIcon: suffixIcon,
              suffixText: suffixText,
              prefixIcon: prefixIcon,
              prefixText: prefixText,
              labelText: labelText,
              filled: filled,
              fillColor: fillColor,
              isDense: !Responsive.isDesktop(context) ? true : false,
              errorStyle: textTheme.bodySmall!
                  .copyWith(color: colorScheme.error, fontSize: 9),
              errorMaxLines: 3,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide:
                    BorderSide(color: colorScheme.lightGreyD9D9D9, width: 1.2.w),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide:
                    BorderSide(color: colorScheme.lightGreyD9D9D9, width: 1.2.w),
              ),
              errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: BorderSide(color: colorScheme.red)),
              focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: colorScheme.red, width: 1.2.w),
                  borderRadius: BorderRadius.circular(12.r)),
              enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                      color: enabledBorderColor ?? colorScheme.lightGreyD9D9D9,
                      width: 1.2),
                  borderRadius: BorderRadius.circular(12.r)),
              focusedBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: colorScheme.primary, width: 1.2.w),
                  borderRadius: BorderRadius.circular(12.r)),
            ),
          ),
        ],
      ),
    );
  }
}
