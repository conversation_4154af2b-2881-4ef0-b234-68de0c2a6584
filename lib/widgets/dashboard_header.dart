import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/features/chat/data/convo_model.dart';
import 'package:the_voice_directory_flutter/features/dashboard/bloc/dashboard_cubit.dart';
import 'package:the_voice_directory_flutter/utils/extensions/empty_space_extn.dart';
import '../core/navigation/navigation_service_impl.dart';
import '../core/routes/route_names.dart';
import '../core/services/hive/hive_keys.dart';
import '../core/services/hive/hive_storage_helper.dart';
import '../features/chat/bloc/chat_bloc.dart';
import '../features/common/user_data/data/user_data_model.dart';
import '../features/dashboard/widgets/custom_nav_item.dart';
import '../features/jobs/bloc/jobs_cubit.dart';
import '../utils/responsive.dart';
import '../utils/string_constants/app_images.dart';
import '../utils/string_constants/app_strings.dart';
import 'buttons/primary_button.dart';
import 'common_notification_icon.dart';

class DashboardHeader extends StatelessWidget implements PreferredSizeWidget {
  const DashboardHeader({super.key});

  @override
  Widget build(BuildContext context) {
    if (!Responsive.isDesktop(context)) return const SizedBox.shrink();
    final colorScheme = Theme.of(context).colorScheme;
    final textStyle = Theme.of(context).textTheme;
    final userData = HiveStorageHelper.getData<UserDataModel>(
        HiveBoxName.user, HiveKeys.userData);
    final bool isClient = userData?.role == UserType.client;
    final bool isAncillary = userData?.role == UserType.ancillaryService;
    final bool isVoice = userData?.role == UserType.voice;

    void handleNavigation(BuildContext context, int tabIndex) {
      NavigationServiceImpl.getInstance()!.doNavigation(context,routeName: RouteName.dashboard, useGo: true,
      ).then((_) {
        context.read<ChatBloc>().updateChatState(isFromChatList: false, selectedConvo: Convo(), selectedChat: "");
        context.read<JobsCubit>().clearSessionStorage();
        context.read<DashboardCubit>().changeTab(tabIndex);
      });
    }

    return Material(
      elevation: 8,
      color: colorScheme.onPrimary,
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: 0.h,
          horizontal: 60.w,
        ),
        child: Row(
          children: [
            SizedBox(
              height: 88.h,
              width: 40.w,
              child: InkWell(
                onTap: () => handleNavigation(context, 0),
                child: Image.asset("assets/images/tvd_logo_img.png"),
              ),
            ),
            Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                FittedBox(
                  child: BlocBuilder<DashboardCubit, DashboardState>(
                    builder: (context, state) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          CustomNavItem(
                            icon: AppImages.jobsIc,
                            label: AppStrings.jobs,
                            isSelected: state.selectedIndex == 0,
                            onTap: () => handleNavigation(context, 0),
                            textStyle: textStyle.bodySmall,
                            colorScheme: colorScheme,
                          ),
                          if (isVoice) ...[
                            CustomNavItem(
                              icon: AppImages.voicesIc,
                              label: AppStrings.explore,
                              isSelected: state.selectedIndex == 1,
                              onTap: () => handleNavigation(context, 1),
                              textStyle: textStyle.bodySmall,
                              colorScheme: colorScheme,
                            ),
                            CustomNavItem(
                              icon: AppImages.calendarIc,
                              label: AppStrings.calendar,
                              isSelected: state.selectedIndex == 2,
                              onTap: () => handleNavigation(context, 2),
                              textStyle: textStyle.bodySmall,
                              colorScheme: colorScheme,
                            ),
                          ] else ...[
                            CustomNavItem(
                              icon: (isClient || isAncillary)
                                  ? AppImages.voicesIc
                                  : AppImages.calendarIc,
                              label: (isClient || isAncillary)
                                  ? AppStrings.explore
                                  : AppStrings.calendar,
                              isSelected: state.selectedIndex == 1,
                              onTap: () => handleNavigation(context, 1),
                              textStyle: textStyle.bodySmall,
                              colorScheme: colorScheme,
                            ),
                          ],
                          CustomNavItem(
                            icon: AppImages.chatIc,
                            label: AppStrings.chat,
                            isSelected: isVoice 
                                ? state.selectedIndex == 3
                                : state.selectedIndex == 3,
                            onTap: () => handleNavigation(context, 3),
                            textStyle: textStyle.bodySmall,
                            colorScheme: colorScheme,
                          ),
                          CommonNotificationIcon(
                            width: 100.w,
                            height: 90.h,
                          ),
                          if(isVoice)
                          CommonNotificationIcon(
                            width: 100.w,
                            height: 90.h,
                            isPrivate: true,
                          ),
                          CustomNavItem(
                            icon: AppImages.profileIc,
                            label: AppStrings.account,
                            isSelected: isVoice 
                                ? state.selectedIndex == 4
                                : state.selectedIndex == 4,
                            onTap: () => handleNavigation(context, 4),
                            textStyle: textStyle.bodySmall,
                            colorScheme: colorScheme,
                          ),
                          4.pw,
                          if (isClient || isAncillary)
                            PrimaryButton(
                              onPressed: () {
                                handleNavigation(context, 0);
                                HiveStorageHelper.deleteKeyInBox(
                                  boxName: HiveBoxName.user,
                                  key: HiveKeys.jobPostData,
                                );
                                NavigationServiceImpl.getInstance()!
                                    .doNavigation(
                                  context,
                                  routeName: RouteName.postJob,
                                );
                              },
                              buttonText: AppStrings.postAJob,
                              width: 150.w,
                              height: 50.h,
                            ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(120.h);
}
