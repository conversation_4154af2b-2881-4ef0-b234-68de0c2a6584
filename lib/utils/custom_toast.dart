import 'package:flutter/material.dart';
import 'package:the_voice_directory_flutter/core/dependency_injection/service_locator.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';
import 'package:the_voice_directory_flutter/widgets/texts/app_text.dart';

import '../core/navigation/navigation_service.dart';
class CustomToast {
  static void show({
    required BuildContext context,
    required String message,
    bool isSuccess = false,
    bool needShellNavigatorContext = false,
  }) {
    final navigationService = serviceLocator<NavigationService>();
    final rootNavigatorKey = navigationService.getGlobalKey();
    final shellNavigatorKey = navigationService.getShellNavigatorKey();

    OverlayState? overlay;
    BuildContext? uiPropertiesContext;

    if (needShellNavigatorContext) {
      final rootNavigatorState = rootNavigatorKey.currentState;
      if (rootNavigatorState != null && rootNavigatorState.mounted) {
        overlay = rootNavigatorState.overlay;
        uiPropertiesContext = rootNavigatorKey.currentContext;
      }
    }

    if (overlay == null) {
      final shellNavigatorState = shellNavigatorKey.currentState;
      if (shellNavigatorState != null && shellNavigatorState.mounted) {
        overlay = shellNavigatorState.overlay;
        uiPropertiesContext = shellNavigatorKey.currentContext;
      }
    }

    if (overlay == null) {
      if (context.mounted) {
        overlay = Overlay.of(context, rootOverlay: true);
        uiPropertiesContext = context;
      }
    }

    if (overlay == null) return;

    final screenSize = MediaQuery.of(uiPropertiesContext!).size;
    final isDesktop = Responsive.isDesktop(uiPropertiesContext);
    ColorScheme colorScheme = Theme.of(uiPropertiesContext).colorScheme;
    final overlayEntry = OverlayEntry(
      builder: (globalContext) => Positioned(
        top: MediaQuery.of(globalContext).viewPadding.top + 20,
        right: isDesktop ? 20 : 0,
        width: isDesktop ? screenSize.width * 0.3 : screenSize.width,
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            margin: EdgeInsets.symmetric(
              horizontal: isDesktop ? 0 : 16, // No side margin for desktop
            ),
            decoration: BoxDecoration(
                color: isSuccess
                    ? colorScheme.blueSnackBarFillColor
                    : colorScheme.redSnackBarFillColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: isSuccess
                        ? colorScheme.blueSnackBarOuterBorderColor
                        : colorScheme.redSnackBarOuterBorderColor,
                    width: 1)),
            child: TextTitle18And14(
              message,
              color: colorScheme.black,
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 3), () {
      overlayEntry.remove();
    });
  }
}
