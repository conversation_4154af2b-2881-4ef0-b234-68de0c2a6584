import 'package:intl/intl.dart';

class DateTimeMethods {
  static String getDateFromTimestamp(String dateTimeStamp, bool inChat){
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);


    final dateToCheck = DateTime.fromMillisecondsSinceEpoch(int.parse(dateTimeStamp)).toLocal();
    final aDate = DateTime(dateToCheck.year, dateToCheck.month, dateToCheck.day);
    String day = "${dateToCheck.day}".padLeft(2, "0");
    String month = "${dateToCheck.month}".padLeft(2, "0");
    String hour = "${dateToCheck.hour}".padLeft(2, "0");
    String minute = "${dateToCheck.minute}".padLeft(2, "0");
    if(aDate == today) {
      return "$hour:$minute";
    } else if(aDate == yesterday) {
      return inChat ? "$hour:$minute, $month/$day/${dateToCheck.year}" : "Yesterday";
    } else{
      return inChat ? "$hour:$minute, $month/$day/${dateToCheck.year}" : "$month/$day/${dateToCheck.year}";
    }
  }

  // Helper to check if two DateTime objects represent the same day
static bool isSameDay(DateTime date1, DateTime date2) {
  return date1.year == date2.year &&
         date1.month == date2.month &&
         date1.day == date2.day;
}

// Helper to format the date for the separator
static String formatDateSeparator(DateTime date) {
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = DateTime(now.year, now.month, now.day - 1);

  if (isSameDay(date, today)) {
    return 'Today';
  } else if (isSameDay(date, yesterday)) {
    return 'Yesterday';
  } else {
    // Example format: Jan 1, 2024 - Adjust as needed
    return DateFormat.yMMMd().format(date);
  }
}

// Helper to safely parse the timestamp string
static DateTime? parseTimestamp(String? timestampStr) {
  if (timestampStr == null || timestampStr.isEmpty) return null;
  try {
    final millis = int.parse(timestampStr);
    return DateTime.fromMillisecondsSinceEpoch(millis);
  } catch (e) {
      print("Error parsing timestamp: $timestampStr, Error: $e");
      return null; // Handle error case
    }
  }
}
