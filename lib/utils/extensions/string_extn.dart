extension StringExtension on String {
  String removeLastChar() {
    if (isEmpty) return this; // Return as is if string is empty
    return substring(0, length - 1);
  }

  String capitalizeFirst() {
    if (isEmpty) return '';
    return this[0].toUpperCase() + substring(1);
  }

  String capitalizeEachWord() {
    return split(' ')
        .map((word) => word.capitalizeFirst())
        .join(' ');
  }
}