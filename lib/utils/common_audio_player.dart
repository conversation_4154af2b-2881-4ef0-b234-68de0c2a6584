import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/string_constants/app_images.dart';

import 'environment_config.dart';

class CommonAudioPlayer extends StatefulWidget {
  final String audioUrl;
  final ColorScheme colorScheme;

  const CommonAudioPlayer({
    super.key,
    required this.audioUrl,
    required this.colorScheme,
  });

  @override
  State<CommonAudioPlayer> createState() => _CommonAudioPlayerState();
}

class _CommonAudioPlayerState extends State<CommonAudioPlayer> {
  late final AudioPlayer audioPlayer;
  bool isInitialized = false;

  @override
  void initState() {
    super.initState();
    audioPlayer = AudioPlayer();
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    if (widget.audioUrl.isEmpty) {
      return;
    } 
    String fullUrl;
    if (widget.audioUrl.startsWith('http') && Uri.parse(widget.audioUrl).isAbsolute) {
      fullUrl = widget.audioUrl;
    } else {
      fullUrl = '${EnvironmentConfig.imageBaseUrl}${widget.audioUrl}';
    }
    try {
      await audioPlayer.setSource(UrlSource(fullUrl));
      setState(() {
        isInitialized = true;
      });
    } catch (e) {
      print('Error initializing audio: $e');
    }
  }

  @override
  void dispose() {
    audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 9.h),
              decoration: BoxDecoration(
                color: widget.colorScheme.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: widget.colorScheme.lightGreyD9D9D9),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: SvgPicture.asset(
                      audioPlayer.state == PlayerState.playing
                          ? AppImages.pauseIcon
                          : AppImages.play,
                    ),
                    onPressed: !isInitialized
                        ? null
                        : () async {
                            if (audioPlayer.state == PlayerState.playing) {
                              await audioPlayer.pause();
                            } else {
                              await audioPlayer.resume();
                            }
                            setState(() {});
                          },
                  ),
                  Expanded(
                    child: StreamBuilder<Duration>(
                      stream: audioPlayer.onPositionChanged,
                      builder: (context, snapshot) {
                        final position = snapshot.data ?? Duration.zero;
                        return StreamBuilder<Duration>(
                          stream: audioPlayer.onDurationChanged,
                          builder: (context, snapshot) {
                            final duration = snapshot.data ?? Duration.zero;
                            return Row(
                              children: [
                                Expanded(
                                  child: SliderTheme(
                                    data: SliderThemeData(
                                      thumbShape: RoundSliderThumbShape(
                                        enabledThumbRadius: 6.r,
                                      ),
                                    ),
                                    child: Slider(
                                      thumbColor: widget.colorScheme.white,
                                      activeColor: widget.colorScheme.primary,
                                      inactiveColor:
                                          widget.colorScheme.lightGreyD9D9D9,
                                      value: position.inSeconds.toDouble(),
                                      max: duration.inSeconds.toDouble(),
                                      onChanged: (value) async {
                                        final newPosition =
                                            Duration(seconds: value.toInt());
                                        await audioPlayer.seek(newPosition);
                                      },
                                    ),
                                  ),
                                ),
                                Text(
                                  "${position.inMinutes}:${(position.inSeconds % 60).toString().padLeft(2, '0')} / "
                                  "${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}",
                                  style: TextStyle(fontSize: 12.sp),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}