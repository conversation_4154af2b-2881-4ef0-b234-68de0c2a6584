import 'package:the_voice_directory_flutter/utils/string_constants/app_strings.dart';

class Validator {
  static String? emailValidator(String value) {
    String pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = RegExp(pattern);
    if (!regex.hasMatch(value)) {
      return ValidationMsg.enterValidEmail;
    } else {
      return null;
    }
  }

  static String? firstNameValidator(String value) {
    if (value.length < 3) {
      return ValidationMsg.enterFirstName;
    } else {
      return null;
    }
  }

  static String? lastNameValidator(String value) {
    if (value.length < 3) {
      return ValidationMsg.enterLastName;
    } else {
      return null;
    }
  }

  static String? phoneNumberValidator(String value) {
    if (value.length < 10 || int.parse(value) < 0) {
      return ValidationMsg.enterPhoneNumber;
    } else {
      return null;
    }
  }

  static String? otpValidator(String value) {
    if (value.length < 6) {
      return ValidationMsg.plsEnterValidOtp;
    } else {
      return null;
    }
  }

  static String? Function(String?) companyNameValidator(
      String value, String errorMessage) {
    final alphanumericRegex = RegExp(r'^[a-zA-Z0-9\s]+$');
    return (String? value) {
      if (value == null || value.isEmpty) {
        return ValidationMsg.plsEnterCompanyName; // Mandatory field error
      } else if (!alphanumericRegex.hasMatch(value)) {
        return errorMessage; // Alphanumeric validation error
      } else {
        return null; // Valid input
      }
    };
  }

  static String? emptyValidator(String? value, String errorMessage, {int minCharLimit = 1, bool isNumber = false}) {
    if (value == null || value.trim().isEmpty || value.trim().length < minCharLimit || (isNumber && (int.tryParse(value) == null || int.tryParse(value) == 0))) {
      return errorMessage;
    }
    return null;
  }

  static String? postalCode(String value) {
    if (value.length < 6) {
      return ValidationMsg.plsEntervalidPCode;
    } else {
      return null;
    }
  }

  static String? meetLinkValidator(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    
    final RegExp meetLinkRegex = RegExp(
      r'^https?:\/\/(meet\.google\.com|teams\.microsoft\.com|zoom\.us)\/[a-zA-Z0-9\-_]+',
      caseSensitive: false,
    );
    
    if (!meetLinkRegex.hasMatch(value)) {
      return 'Please enter a valid meeting link (Google Meet, Microsoft Teams, or Zoom)';
    }
    
    return null;
  }

      // Helper method to format duration
  static formatDuration(String? hours, String? minutes, String? seconds) {
    final hrs = int.tryParse(hours ?? '0') ?? 0;
    final mins = int.tryParse(minutes ?? '0') ?? 0;
    final secs = int.tryParse(seconds ?? '0') ?? 0;
    
    if (hrs == 0 && mins == 0 && secs == 0) {
      return "0 seconds";
    }
    
    final List<String> parts = [];
    
    if (hrs > 0) {
      parts.add("$hrs ${hrs == 1 ? 'hour' : 'hours'}");
    }
    if (mins > 0) {
      parts.add("$mins ${mins == 1 ? 'minute' : 'minutes'}");
    }
    
    if (secs > 0) {
      parts.add("$secs ${secs == 1 ? 'second' : 'seconds'}");
    }
    
    return parts.join(' ');
  }

  static formatName(String name) {
    String trimmed = name.trim();
    if (trimmed.isEmpty) return '';
    return trimmed[0].toUpperCase() + trimmed.substring(1);
  }
}
