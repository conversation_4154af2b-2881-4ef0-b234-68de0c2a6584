import 'package:flutter/material.dart';

class Responsive {
  static const int mobileBreakpoint = 650;
  static const int tabletBreakpoint = 1050;

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint;

  static bool isTab(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final orientation = MediaQuery.of(context).orientation;

    // A tablet is considered in "tablet" mode only in portrait orientation
    return size.width >= mobileBreakpoint &&
        size.width < tabletBreakpoint &&
        orientation == Orientation.portrait;
  }

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint && !isMobile(context) && !isTab(context);
}
