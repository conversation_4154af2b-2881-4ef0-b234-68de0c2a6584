import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Assuming you use this for SvgPicture
import 'package:image_picker/image_picker.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import '../widgets/texts/app_text.dart';
import 'common_models/media_info_model.dart';
import 'custom_toast.dart';
import 'string_constants/app_images.dart';
import 'string_constants/app_strings.dart';

class ImagePickerService {
  final ImagePicker _imagePicker = ImagePicker();

  /// Shows a modal bottom sheet to choose between camera and gallery,
  /// handles permissions, and picks an image.
  /// Returns [MediaInfoModel] if an image is successfully picked, otherwise null.
  /// Shows appropriate toasts for errors or permission issues.
  Future<MediaInfoModel?> pickImageWithOptions({
    required BuildContext context,
    Color? bottomSheetBackgroundColor,
  }) async {
    if (kIsWeb) {
      return await _handleImagePicking(
        ImageSource.gallery,
        context, // Pass context for toasts
      );
    }
    // This will be the result returned by showModalBottomSheet
    // when one of the options (camera, gallery, or cancel) is chosen and popped.
    final MediaInfoModel? result = await showModalBottomSheet<MediaInfoModel?>(
      context: context,
      backgroundColor:
          Theme.of(context).colorScheme.white, // Use surface for default
      builder: (bottomSheetContext) {
        // Use a different context name to avoid confusion
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: SvgPicture.asset(
                    AppImages.cameraIcon), // Ensure this asset exists
                title: const TextTitle14(AppStrings.takeAPicture),
                onTap: () async {
                  // Await the image picking and then pop with the result
                  final image = await _handleImagePicking(
                    ImageSource.camera,
                    bottomSheetContext, // Pass context for toasts
                  );
                  Navigator.of(bottomSheetContext)
                      .pop(image); // Pop with the picked image or null
                },
              ),
              ListTile(
                leading: SvgPicture.asset(
                    AppImages.imageIcon), // Ensure this asset exists
                title: const TextTitle14(AppStrings.uploadNewProfile),
                onTap: () async {
                  final image = await _handleImagePicking(
                    ImageSource.gallery,
                    bottomSheetContext,
                  );
                  Navigator.of(bottomSheetContext).pop(image);
                },
              ),
              ListTile(
                title: const Center(child: TextTitle14(AppStrings.cancel)),
                onTap: () {
                  Navigator.of(bottomSheetContext)
                      .pop(null); // Pop with null for cancel
                },
              ),
            ],
          ),
        );
      },
    );
    return result; // This will be the MediaInfoModel or null
  }

  Future<MediaInfoModel?> _handleImagePicking(
    ImageSource source,
    BuildContext context, // Context for showing toasts
  ) async {
    try {
      if (kIsWeb) {
        return await _pickImageForWebInternal(source, context);
      } else {
        return await _checkPermissionAndPickImageInternal(source, context);
      }
    } catch (e) {
      // Generic error catch, though specific errors should be caught earlier
      debugPrint("Error in _handleImagePicking: $e");
      CustomToast.show(
        context: context,
        message: AppStrings.somethingWentWrong,
      );
      return null;
    }
  }

  Future<MediaInfoModel?> _pickImageForWebInternal(
    ImageSource source,
    BuildContext context,
  ) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
        ],
      );
      if (result != null) {
        final pickedFile = result.files.first;
        if (pickedFile.bytes == null ||
            pickedFile.bytes!.isEmpty ||
            pickedFile.extension == null ||
            pickedFile.extension!.isEmpty) {
          return null;
        }
        if (!isFileSizeValid(context,
            extension: pickedFile.extension!, size: pickedFile.bytes!.length)) {
          return null;
        }
        return MediaInfoModel(
          bytes: pickedFile.bytes!,
          ext: pickedFile.name.contains('.')
              ? pickedFile.name
                  .substring(pickedFile.name.lastIndexOf(".") + 1)
                  .toLowerCase()
              : '', // Handle case with no extension
          name: pickedFile.name,
        );
      }
      return null; // User cancelled picker
    } catch (e) {
      debugPrint("Error picking image for web: $e");
      CustomToast.show(
        context: context,
        message: AppStrings.somethingWentWrong,
      );
      return null;
    }
  }

  Future<MediaInfoModel?> _checkPermissionAndPickImageInternal(
    ImageSource source,
    BuildContext context,
  ) async {
    PermissionStatus status;
    // String permissionRationale = "";

    if (Platform.isIOS) {
      if (source != ImageSource.camera) {
        return await _pickFileInternal(source, context);
      }
      status = await Permission.camera.status;
      if (status.isDenied) {
        status = await Permission.camera.request();
      }
    } else if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (source == ImageSource.camera) {
        status = await Permission.camera.request();
        // permissionRationale = AppStrings.cameraPermission;
      } else {
        // For Android 13 (API 33) and above, use specific media permissions
        if (sdkInt >= 33) {
          status = await Permission.photos.request(); // For images and photos
          // If you also need videos: await Permission.videos.request();
        } else {
          // For older Android versions, use storage permission
          status = await Permission.storage.request();
        }
        // permissionRationale = AppStrings.galleryPermission;
      }
    } else {
      // Unsupported platform for permission check, proceed directly
      return await _pickFileInternal(source, context);
    }

    if (status.isGranted) {
      return await _pickFileInternal(source, context);
    } else {
      // Handle various denied states
      String messageToShow;
      if (status.isDenied) {
        messageToShow = AppStrings.permissionDenied;
      } else if (status.isPermanentlyDenied) {
        messageToShow = AppStrings.permissionDeniedPleaseGoToSettingsAndAllow;
        // Optionally, offer to open app settings
        // await openAppSettings();
      } else if (status.isRestricted) {
        // e.g. parental controls
        messageToShow =
            "Access to ${source.name} is restricted on this device.";
      } else {
        // .limited (iOS) or other states
        messageToShow =
            "Limited access to ${source.name}. Please grant full access if needed.";
        // For .limited on iOS, you might still be able to pick, or re-request.
        // For simplicity here, we treat it as not fully granted for picking a new image.
        // If you want to allow picking with limited access, you might call _pickFileInternal here.
      }
      CustomToast.show(context: context, message: messageToShow);
      return null;
    }
  }

  Future<MediaInfoModel?> _pickFileInternal(
    ImageSource source,
    BuildContext context,
  ) async {
    try {
      final pickedFile = await _imagePicker.pickImage(source: source);
      if (pickedFile != null) {
        final bytes = await pickedFile.readAsBytes();
        if (!isFileSizeValid(context,
            extension: pickedFile.name.split('.').last, size: bytes.length)) {
          return null;
        }
        return MediaInfoModel(
          path: pickedFile.path,
          bytes: bytes,
          ext: pickedFile.name.contains('.')
              ? pickedFile.name
                  .substring(pickedFile.name.lastIndexOf(".") + 1)
                  .toLowerCase()
              : '', // Handle case with no extension
          name: pickedFile.name,
        );
      }
      return null; // User cancelled picker
    } catch (e) {
      debugPrint("Error picking file: $e");
      // Specific errors like 'photo_access_denied' might be caught here too if permission was .limited
      CustomToast.show(
        context: context,
        message: AppStrings.somethingWentWrong,
      );
      return null;
    }
  }

  Future<MediaInfoModel?> pickAnyFile(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
          'pdf',
          'doc',
          'docx',
          'txt',
          'csv',
          'mp3',
          'mp4',
          'wav',
          'mov',
          'webm',
          'mkv',
          'avi'
        ],
      );
      if (result != null) {
        final pickedFile = result.files.first;
        if (kIsWeb) {
        if (pickedFile.bytes == null ||
            pickedFile.bytes!.isEmpty ||
            pickedFile.extension == null ||
            pickedFile.extension!.isEmpty) {
          return null;
          }
          if (!isFileSizeValid(context,
              extension: pickedFile.extension!,
              size: pickedFile.bytes!.length)) {
            return null;
          }
          return MediaInfoModel(
            name: pickedFile.name,
            // url: pickedFile.path,
            bytes: pickedFile.bytes!,
            ext: pickedFile.extension!,
          );
        } else {
          final fileBytes = await File(pickedFile.path!).readAsBytes();
          if (fileBytes.isEmpty ||
              pickedFile.extension == null ||
              pickedFile.extension!.isEmpty) {
            return null;
          }
          if (!isFileSizeValid(context,
              extension: pickedFile.extension!, size: fileBytes.length)) {
            return null;
          }
          return MediaInfoModel(
            name: pickedFile.name,
            path: pickedFile.path,
            url: pickedFile.path,
            bytes: fileBytes,
            ext: pickedFile.extension!,
          );
        }
      }
      return null;
    } catch (e) {
      debugPrint("Error picking file: $e");
      return null;
    }
  }

  bool isFileSizeValid(BuildContext context,
      {required String extension, required int size}) {
    final ext = extension.toLowerCase();
    if ((ext == 'mp4' ||
            ext == 'mov' ||
            ext == 'avi' ||
            ext == 'mkv' ||
            ext == 'webm') &&
        size >= 50000000) {
      CustomToast.show(
        context: context,
        message: "Video size cannot exceed 50MB.",
      );
      return false;
    }
    if ((ext == 'jpg' || ext == 'jpeg' || ext == 'png') &&
        size >= 10000000) {
      CustomToast.show(
        context: context,
        message: "Image size cannot exceed 10MB.",
      );
      return false;
    }
    if ((ext == 'mp3' || ext == 'wav') && size >= 10000000) {
      CustomToast.show(
        context: context,
        message: "Audio size cannot exceed 10MB.",
      );
      return false;
    }
    if ((ext == 'pdf' ||
            ext == 'doc' ||
            ext == 'docx' ||
            ext == 'txt' ||
            ext == 'csv') &&
        size >= 10000000) {
      CustomToast.show(
        context: context,
        message: "Document size cannot exceed 10MB.",
      );
      return false;
    }
    return true;
  }
}
