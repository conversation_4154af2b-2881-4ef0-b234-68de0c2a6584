enum Environment { dev, uat, prod }

class EnvironmentConfig {
  static Environment currentEnvironment = Environment.dev;

  static const Map<Environment, String> apiBaseUrls = {
    Environment.dev: "https://dev-api.thevoicedirectory.in",
    Environment.uat: "https://uat-api.thevoicedirectory.in",
    Environment.prod: "https://prod-api.thevoicedirectory.in",
  };

  static const Map<Environment, String> imageBaseUrls = {
    Environment.dev: "https://be-dev-tvd-media.s3.ap-south-1.amazonaws.com/",
    Environment.uat: "https://be-uat-tvd-media.s3.ap-south-1.amazonaws.com/",
    Environment.prod: "https://be-prod-tvd-media.s3.ap-south-1.amazonaws.com/",
  };

  static const Map<Environment, String> blockUserCollections = {
    Environment.dev: "dev_blocked_users",
    Environment.uat: "uat_blocked_users",
    Environment.prod: "prod_blocked_users", 
  };

  static const Map<Environment, String> firebaseChatsCollections = {
    Environment.dev: "dev_chats",
    Environment.uat: "uat_chats",
    Environment.prod: "prod_chats", 
  };

  static const Map<Environment, String> firestoreChatFilesCollections = {
    Environment.dev: "dev_chat_files",
    Environment.uat: "uat_chat_files",
    Environment.prod: "prod_chat_files", 
  };

  static String get firestoreChatFilesCollection {
    return firestoreChatFilesCollections[currentEnvironment] ?? '';
  }

  static String get firebaseChatsCollection {
    return firebaseChatsCollections[currentEnvironment] ?? '';
  }

  static String get blockUserCollection {
    return blockUserCollections[currentEnvironment] ?? '';
  }

  static String get baseUrl {
    return apiBaseUrls[currentEnvironment] ?? '';
  }

  static String get apiBaseUrl {
    String baseUrl = apiBaseUrls[currentEnvironment] ?? '';
    return baseUrl.isNotEmpty ? "$baseUrl/api" : '';
  }

  static String get imageBaseUrl {
    return imageBaseUrls[currentEnvironment] ?? '';
  }
}
