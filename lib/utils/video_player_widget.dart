import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:video_player/video_player.dart';

import '../core/theme/app_theme.dart';
import '../utils/string_constants/app_images.dart';
import '../widgets/loading_dialog.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final bool showVideoName;
  final double? height;
  final double? width;

  const VideoPlayerWidget({
    super.key,
    required this.videoUrl,
    this.showVideoName = true,
    this.height,
    this.width,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

    await _videoPlayerController.initialize();

    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController,
      autoInitialize: true,
      looping: false,
      allowFullScreen: false,
      showControls: false,
      showOptions: false,
      aspectRatio: _videoPlayerController.value.aspectRatio,
      materialProgressColors: ChewieProgressColors(
        playedColor: const Color(0xffD5E04D),
        bufferedColor: const Color(0xffD5E04D).withOpacity(0.2),
      ),
    );
    if (mounted) {
      setState(() {});
    }
  }

  void _showFullScreenVideo() {
    final tempController = ChewieController(
      videoPlayerController: _videoPlayerController,
      autoPlay: true,
      looping: false,
      showControls: true,
      allowFullScreen: false,
      showOptions: false,
      materialProgressColors: ChewieProgressColors(
        playedColor: Theme.of(context).colorScheme.primary,
        bufferedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
      ),
      customControls: const MaterialControls(),
    );
    
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "Video",
      pageBuilder: (context, animation1, animation2) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.black,
            body: Stack(
              fit: StackFit.expand,
              children: [
                Center(
                  child: AspectRatio(
                    aspectRatio: _videoPlayerController.value.aspectRatio,
                    child: Chewie(controller: tempController),
                  ),
                ),
                Positioned(
                  top: 16.h,
                  right: 16.w,
                  child: GestureDetector(
                    onTap: () {
                      tempController.pause();
                      tempController.dispose();
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24.r,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      transitionBuilder: (context, animation1, animation2, child) {
        return FadeTransition(
          opacity: animation1,
          child: child,
        );
      },
    ).then((_) {
      tempController.dispose();
    });
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return InkWell(
      onTap: _showFullScreenVideo,
      child: Container(
        margin: EdgeInsets.only(top: 16.h),
        padding: EdgeInsets.all(12.h),
        decoration: BoxDecoration(
          color: colorScheme.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: colorScheme.lightGreyD9D9D9,
          ),
        ),
        child: _chewieController != null &&
                _chewieController!.videoPlayerController.value.isInitialized
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Video thumbnail or icon
                  Container(
                    width: widget.width ?? 60.w,
                    height: widget.height ?? 60.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        AspectRatio(
                          aspectRatio: _videoPlayerController.value.aspectRatio,
                          child: Chewie(controller: _chewieController!),
                        ),
                        CircleAvatar(
                          backgroundColor: Colors.white,
                          radius: 14,
                          child: SvgPicture.asset(
                            AppImages.play,
                            width: 24,
                            height: 24,
                          ),
                        ),
                      ],
                  ),
                  ),
                  if (widget.showVideoName) ...[
                  SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.videoUrl.split('/').last,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  ]
                ],
            )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: widget.width ?? 60.w,
                    height: widget.height ?? 60.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: const Center(
                      child: Loader(),
                    ),
                  ),
                  if (widget.showVideoName) ...[
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Text(
                        'Loading video...',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ],
              ),
      ),
    );
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }
}
