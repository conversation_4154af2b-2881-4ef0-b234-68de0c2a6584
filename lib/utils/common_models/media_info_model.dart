import 'package:flutter/services.dart';
import 'package:hive/hive.dart';
part 'media_info_model.g.dart';

@HiveType(typeId: 13)
class MediaInfoModel {
  @HiveField(0)
  String name;
  @HiveField(1)
  String ext;
  @HiveField(2)
  Uint8List bytes;
  @HiveField(3)
  String? path;
  @HiveField(4)
  int? id;
  @HiveField(5)
  String? url;

  MediaInfoModel({
    required this.bytes,
    required this.ext,
    required this.name,
    this.path,
    this.id,
    this.url,
  });
}
