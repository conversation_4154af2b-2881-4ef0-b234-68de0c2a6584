import 'package:hive_flutter/hive_flutter.dart';

import '../../features/common/user_data/data/user_data_model.dart';

part 'dropdown_data_menu_model.g.dart';

class DropDownResponseModel {
  List<DropdownData>? industry;
  List<DropdownData>? projectType;
  List<DropdownData>? voiceCharacter;
  List<DropdownData>? voiceType;
  List<DropdownData>? voiceLanguage;
  List<DropdownData>? accent;
  List<AgeRange>? ageRange;
  List<DropdownData>? gender;
  List<DropdownData>? experience;
  List<DropdownData>? services;

  DropDownResponseModel(
      {this.industry,
      this.projectType,
      this.voiceCharacter,
      this.voiceType,
      this.voiceLanguage,
      this.accent,
      this.ageRange,
      this.gender,
      this.services});

  DropDownResponseModel.fromJson(Map<String, dynamic> json) {
    if (json['industry'] != null) {
      industry = <DropdownData>[];
      json['industry'].forEach((v) {
        industry!.add(DropdownData.fromJson(v));
      });
    }
    if (json['project_type'] != null) {
      projectType = <DropdownData>[];
      json['project_type'].forEach((v) {
        projectType!.add(DropdownData.fromJson(v));
      });
    }
    if (json['voice_character'] != null) {
      voiceCharacter = <DropdownData>[];
      json['voice_character'].forEach((v) {
        voiceCharacter!.add(DropdownData.fromJson(v));
      });
    }
    if (json['voice_type'] != null) {
      voiceType = <DropdownData>[];
      json['voice_type'].forEach((v) {
        voiceType!.add(DropdownData.fromJson(v));
      });
    }
    if (json['voice_language'] != null) {
      voiceLanguage = <DropdownData>[];
      json['voice_language'].forEach((v) {
        voiceLanguage!.add(DropdownData.fromJson(v));
      });
    }
    if (json['accent'] != null) {
      accent = <DropdownData>[];
      json['accent'].forEach((v) {
        accent!.add(DropdownData.fromJson(v));
      });
    }
    if (json['age_range'] != null) {
      ageRange = <AgeRange>[];
      json['age_range'].forEach((v) {
        ageRange!.add(AgeRange.fromJson(v));
      });
    }
    if (json['gender'] != null) {
      gender = <DropdownData>[];
      json['gender'].forEach((v) {
        gender!.add(DropdownData.fromJson(v));
      });
    }
     if (json['experience'] != null) {
      experience = <DropdownData>[];
      json['experience'].forEach((v) {
        experience!.add(DropdownData.fromJson(v));
      });
    }
    if (json['services'] != null) {
      services = <DropdownData>[];
      json['services'].forEach((v) {
        services!.add(DropdownData.fromJson(v));
      });
    }
  }
}

@HiveType(typeId: 10)
class DropdownData {
  @HiveField(0)
  int? id;

  @HiveField(1)
  String? name;

  DropdownData({this.id, this.name});

  DropdownData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  @override
  bool operator ==(Object other) =>
      other is DropdownData &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;
          
  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
