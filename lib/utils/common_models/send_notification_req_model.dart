class SendNoticationReqModel {
  List<int>? recipientUserIds;
  String? message;
  String? title;
  ExtraData? payload;

  SendNoticationReqModel(
      {this.recipientUserIds, this.message, this.title, this.payload});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipient_user_ids'] = recipientUserIds;
    data['message'] = message;
    data['title'] = title;
    if (payload != null) {
      data['data'] = payload!.toJson();
    }
    return data;
  }
}

class ExtraData {
  String? routeName;
  String? routePath;
  String? module;
  Payload? extraData;

  ExtraData({this.routeName, this.routePath, this.module, this.extraData});
  
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['route_name'] = routeName;
    data['route_path'] = routePath;
    data['module'] = module;
    data['extra_data'] = extraData?.toJson(); 
    return data;
  }
}

class Payload {
  String? jobName;
  String? groupId;
  String? jobId;
  String? name;
  String? idFrom;
  String? idTo;
  String? profileImg;
  String? myProfileImg;
  String? myName;
  bool? isJobChat;

  Payload(
      {this.jobName,
      this.groupId,
      this.jobId,
      this.name,
      this.idFrom,
      this.idTo,
      this.profileImg,
      this.myProfileImg,
      this.myName,
      this.isJobChat});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['job_name'] = jobName;
    data['group_id'] = groupId;
    data['job_id'] = jobId;
    data['name'] = name;
    data['id_from'] = idFrom;
    data['id_to'] = idTo;
    data['profile_img'] = profileImg;
    data['my_profile_img'] = myProfileImg;
    data['my_name'] = myName;
    data['is_job_chat'] = isJobChat;
    return data;
  }
}
