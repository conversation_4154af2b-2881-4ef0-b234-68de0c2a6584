class UserInfoRequestModel {
  String? email;
  String? firstName;
  String? lastName;
  String? phoneNumber;
  String? role;
  String? countryName;
  String? countryAbbr;
  String? countryCode;
  String? profilePic;
  String? streetAddress;
  String? state;
  int? postalCode;
  String? country;
  String? city;
  int? intermediateStep;
  String? bio;
  String? company;
  int? industry;
  int? gender;
  int? voiceExperience;
  List<int>? audioPathToDelete;
  List<int?>? voiceGender;
  List<int?>? voiceType;
  List<int?>? voiceCharacter;
  List<int?>? voiceLanguage;
  List<int?>? projectType;
  List<int?>? accent;
  List<int?>? ageRange;
  List<String?>? tag;
  List<String?>? audioPath;
  double? lat;
  double? lng;
  bool? isProfileCompleted;
  List<int?>? services;

  UserInfoRequestModel({
    this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.role,
    this.countryName,
    this.countryAbbr,
    this.countryCode,
    this.profilePic,
    this.streetAddress,
    this.state,
    this.postalCode,
    this.country,
    this.city,
    this.intermediateStep,
    this.voiceGender,
    this.audioPathToDelete,
    this.audioPath,
    this.tag,
    this.bio,
    this.company,
    this.industry,
    this.gender,
    this.voiceExperience,
    this.voiceType,
    this.voiceCharacter,
    this.voiceLanguage,
    this.projectType,
    this.accent,
    this.ageRange,
    this.lat,
    this.lng,
    this.isProfileCompleted,
    this.services,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (email != null) {
      data['email'] = email!.trim();
    }
    if (firstName != null && firstName!.isNotEmpty) {
      data['first_name'] = firstName!.trim();
    }
    if (lastName != null && lastName!.isNotEmpty) {
      data['last_name'] = lastName!.trim();
    }
    if (phoneNumber != null) {
      data['phone_number'] = phoneNumber!.trim();
    }
    if (role != null) {
      data['role'] = role;
    }
    if (countryName != null) {
      data['country_name'] = countryName!.trim();
    }
    if (countryAbbr != null) {
      data['country_abbr'] = countryAbbr!.trim();
    }
    if (countryCode != null && countryCode!.isNotEmpty) {
      data['country_code'] = countryCode!.trim();
    }
    if (profilePic != null) {
      data['profile_pic'] = profilePic;
    }
    if (postalCode != null) {
      data['postal_code'] = postalCode;
    }
    if (intermediateStep != null) {
      data['intermediate_step'] = intermediateStep;
    }
    if (voiceGender != null && voiceGender!.isNotEmpty) {
      data['voice_gender'] = voiceGender;
    }
    if (audioPathToDelete != null && audioPathToDelete!.isNotEmpty) {
      data['audio_path_to_delete'] = audioPathToDelete;
    }
    if (audioPath != null && audioPath!.isNotEmpty) {
      data['audio_path'] = audioPath;
    }
    if (city != null && city!.isNotEmpty) {
      data['city'] = city!.trim();
    }
    if (country != null && country!.isNotEmpty) {
      data['country'] = country!.trim();
    }
    if (postalCode != null) {
      data['postal_code'] = postalCode;
    }
    if (state != null && state!.isNotEmpty) {
      data['state'] = state!.trim();
    }
    if (streetAddress != null && streetAddress!.isNotEmpty) {
      data['street_address'] = streetAddress!.trim();
    }
    if (bio != null && bio!.isNotEmpty) {
      data['bio'] = bio!.trim();
    }
    if (company != null) {
      data['company'] = company!.trim();
    }
    if (industry != null) {
      data['industry'] = industry;
    }
    if (gender != null) {
      data['gender'] = gender;
    }
    if (voiceExperience != null) {
      data['voice_experience'] = voiceExperience;
    }
    if (voiceGender != null && voiceGender!.isNotEmpty) {
      data['voice_gender'] = voiceGender;
    }
    if (voiceType != null && voiceType!.isNotEmpty) {
      data['voice_type'] = voiceType;
    }
    if (voiceCharacter != null && voiceCharacter!.isNotEmpty) {
      data['voice_character'] = voiceCharacter;
    }
    if (voiceLanguage != null && voiceLanguage!.isNotEmpty) {
      data['voice_language'] = voiceLanguage;
    }
    if (projectType != null && projectType!.isNotEmpty) {
      data['project_type'] = projectType;
    }
    if (accent != null && accent!.isNotEmpty) {
      data['voice_accent'] = accent;
    }
    if (ageRange != null && ageRange!.isNotEmpty) {
      data['age_range'] = ageRange;
    }
    if (tag != null) {
      data['tags'] = tag;
    }

    if (lat != null && lng != null) {
      data['location'] = {
        'lat': lat,
        'lng': lng,
      };
    }
    if (isProfileCompleted != null) {
      data['is_profile_completed'] = isProfileCompleted;
    }
    if (services != null && services!.isNotEmpty) {
      data['services'] = services;
    }
    return data;
  }
}
