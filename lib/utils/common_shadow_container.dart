import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';

class CommonShadowContainer extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final double blurRadius;
  final double? spreadRadius;
  final double offsetX;
  final double offsetY;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? shadowOpacity;
  final double? height;
  final double? width;

  const CommonShadowContainer({
    super.key,
    required this.child,
    this.borderRadius = 12.0,
    this.blurRadius = 25.0,
    this.spreadRadius = 0.0,
    this.offsetX = 0.0,
    this.offsetY = 0.0,
    this.margin,
    this.height,
    this.width,
    this.padding,
    this.shadowOpacity,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    return Container(
      margin: margin,
      height: height,
      width: width,
      padding: padding,
      decoration: BoxDecoration(
        color: colorScheme.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: colorScheme.black.withOpacity(shadowOpacity ?? (kIsWeb ? 0.07 : 0.2)),
            offset: Offset(offsetX, offsetY),
            blurRadius: blurRadius,
            spreadRadius:
                spreadRadius ?? (!Responsive.isDesktop(context) ? 8.r : 2.r),
          ),
        ],
      ),
      child: child,
    );
  }
}
