import 'package:flutter/services.dart';

class NoLeadingZeroFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // If input is empty, return it
    if (newValue.text.isEmpty) return newValue;

    // Remove leading zeros, but allow a single "0"
    String value = newValue.text.replaceFirst(RegExp(r'^0+'), '');
    
    // If the result is empty, keep a single "0"
    if (value.isEmpty) value = '0';

    return newValue.copyWith(
      text: value,
      selection: TextSelection.collapsed(offset: value.length),
    );
  }
}
