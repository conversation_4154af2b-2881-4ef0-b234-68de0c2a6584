import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:universal_html/html.dart' as html; // for Web
import 'package:dio/dio.dart';

import 'custom_toast.dart';
import 'string_constants/app_strings.dart';

class DownloadFileManager {
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid &&
        await DeviceInfoPlugin()
                .androidInfo
                .then((value) => value.version.sdkInt) <
            33) {
      PermissionStatus status = await Permission.storage.request();
      if (status.isGranted) {
        return true;
      } else if (status.isPermanentlyDenied) {
        openAppSettings();
        return false;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  static Future<Directory?> _getDownloadDirectory() async {
    if (Platform.isIOS) {
      return await getApplicationDocumentsDirectory();
    }
    if (await DeviceInfoPlugin()
            .androidInfo
            .then((value) => value.version.sdkInt) <
        30) {
      return await getExternalStorageDirectory();
    }
    return Directory('/storage/emulated/0/Download');
  }

  static Future<void> downloadFile(
      BuildContext context, String url, String fileName) async {
    try {
      if (!kIsWeb && !await _requestPermissions()) {
        throw Exception('Permission denied');
      }
      Dio dio = Dio();

      if (kIsWeb) {
        // Web download
        final response = await dio.get<List<int>>(
          url,
          options: Options(responseType: ResponseType.bytes),
        );
        final blob = html.Blob([response.data]);
        html.AnchorElement(href: html.Url.createObjectUrlFromBlob(blob))
          ..setAttribute("download", fileName)
          ..click();
      } else {
        Directory? dir;
        if (Platform.isAndroid || Platform.isIOS) {
          dir = await _getDownloadDirectory();
          if (dir == null) {
            bool hasExisted = await dir!.exists();
            if (!hasExisted) {
              await dir.create();
            }
          }
          String savePath = '${dir.path}/$fileName';
          Response response = await dio.download(
            url,
            savePath,
          );
          if (response.statusCode == 200) {
            CustomToast.show(
                context: context,
                message: AppStrings.fileDownloadedSuccessfully,
                isSuccess: true);

            await OpenFile.open(savePath);
          }
        }
      }
    } catch (e) {
      CustomToast.show(
          context: context,
          message: AppStrings.fileDownloadFailed,
          isSuccess: false);
    }
  }
}
