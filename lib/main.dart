import 'dart:async';
import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:the_voice_directory_flutter/core/api/api_service.dart';
import 'package:the_voice_directory_flutter/core/dependency_injection/service_locator.dart';
import 'package:the_voice_directory_flutter/core/routes/app_routes.dart';
import 'package:the_voice_directory_flutter/core/services/hive/hive_storage_helper.dart';
import 'package:the_voice_directory_flutter/core/theme/app_theme.dart';
import 'package:the_voice_directory_flutter/features/common/social_sign_in/bloc/social_login_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/bloc/user_bloc.dart';
import 'package:the_voice_directory_flutter/features/common/user_data/data/user_data_model.dart';
import 'package:the_voice_directory_flutter/features/dashboard/bloc/dashboard_cubit.dart';
import 'package:the_voice_directory_flutter/features/delete_account/bloc/delete_account_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/book_event_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/disconnect_calender_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_calender_event_bloc.dart';
import 'package:the_voice_directory_flutter/features/google_calender/bloc/get_server_auth_code_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/favorite_job_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/invited_user_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/job_detail_bloc.dart';
import 'package:the_voice_directory_flutter/features/job_details/bloc/update_invitee_bloc.dart';
import 'package:the_voice_directory_flutter/features/login/bloc/login_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/notification_list_bloc.dart';
import 'package:the_voice_directory_flutter/features/notification/bloc/read_notification_bloc.dart';
import 'package:the_voice_directory_flutter/features/post_job/bloc/post_a_job_cubit.dart';
import 'package:the_voice_directory_flutter/features/post_job/data/model/users_list.dart';
import 'package:the_voice_directory_flutter/features/report/bloc/report_bloc.dart';
import 'package:the_voice_directory_flutter/features/signup/bloc/signup_bloc.dart';
import 'package:the_voice_directory_flutter/utils/responsive.dart';

import 'core/services/hive/hive_keys.dart';
import 'features/chat/bloc/chat_bloc.dart';
import 'features/create_profile/bloc/logout_bloc.dart';
import 'features/jobs/bloc/jobs_cubit.dart';
import 'features/notification/bloc/notification_count_bloc.dart';
import 'features/post_job/data/enums/budget_type_enum.dart';
import 'features/post_job/data/enums/job_post_type.dart';
import 'features/post_job/data/enums/location_type_enum.dart';
import 'features/post_job/data/enums/sample_script_type.dart';
import 'features/post_job/models/job_post_model.dart';
import 'firebase_options.dart';
import 'utils/common_models/dropdown_data_menu_model.dart';
import 'utils/common_models/media_info_model.dart';
import 'utils/environment_config.dart';
import 'package:the_voice_directory_flutter/core/services/push_notification_service.dart';

Future<void> main() async {
  await runZonedGuarded(() async {
    EnvironmentConfig.currentEnvironment = Environment.dev;

    WidgetsFlutterBinding.ensureInitialized();
    await Firebase.initializeApp(
      name: !kIsWeb ? "tvd-dev" : null,
      options: DefaultFirebaseOptions.currentPlatform,
    );
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
      ),
    );

    GoRouter.optionURLReflectsImperativeAPIs = true;

    // Initialize Hive
    await Hive.initFlutter();

    // Register Adapters
    Hive.registerAdapter(UserDataModelAdapter());
    Hive.registerAdapter(UserTypeAdapter());
    Hive.registerAdapter(NameIdModelAdapter());
    Hive.registerAdapter(AgeRangeAdapter());
    Hive.registerAdapter(AudioURLAdapter());
    Hive.registerAdapter(JobPostModelAdapter());
    Hive.registerAdapter(LocationTypeAdapter());
    Hive.registerAdapter(SampleScriptTypeAdapter());
    Hive.registerAdapter(BudgetTypeAdapter());
    Hive.registerAdapter(JobPostTypeAdapter());
    Hive.registerAdapter(DropdownDataAdapter());
    Hive.registerAdapter(UsersListAdapter());
    Hive.registerAdapter(UserAdapter());
    Hive.registerAdapter(MediaInfoModelAdapter());
    Hive.registerAdapter(ReviewModelAdapter());
    Hive.registerAdapter(RatingCountAdapter());

    await setupLocator();

    setToken();
    await ScreenUtil.ensureScreenSize();

    // FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    // FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    if (!kIsWeb) PushNotificationService.getInstance()?.setupNotifications();

    runApp(
      const MyApp(),
    );
  }, (error, stackTrace) {});
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  UserDataModel? userData;
  @override
  void initState() {
    // AppConfig.init(
    //   flavor: Flavor.dev,
    //   baseUrl: BaseUrl.getBaseUrl(Flavor.dev),
    // );
    userData = HiveStorageHelper.getData<UserDataModel>(HiveBoxName.user, HiveKeys.userData);
    super.initState();
  }
  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.resumed) {
  //     serviceLocator<InternetConnectivityCubit>().refreshState();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => LoginBloc(),
        ),
        BlocProvider(
          create: (context) => SocialLoginBloc(),
        ),
        BlocProvider(
          create: (context) => SignUpBloc(),
        ),
        BlocProvider(
          create: (context) => UserBloc(),
        ),
        BlocProvider(
          create: (_) => NotificationListBloc(),
        ),
        BlocProvider(
          create: (context) => ReadNotificationBloc(),
        ),
        BlocProvider(
          create: (_) => NotificationCountBloc(),
        ),
        BlocProvider(
          create: (_) => JobsCubit(),
        ),
        BlocProvider(
          create: (_) => JobDetailBloc(),
        ),
        BlocProvider(
          create: (_) => DeleteAccountBloc()
        ),
        BlocProvider(
          create: (_) => FavoriteJobBloc(),
        ),
        BlocProvider(
          create: (context) => DashboardCubit(hasClientTab: userData?.role == UserType.client || userData?.role == UserType.ancillaryService),
        ),
        BlocProvider(create: (_) => LogoutBloc()),
        BlocProvider(create: (_) => GetCalenderEventBloc()),
        BlocProvider(create: (_) => BookEventBloc()),
        BlocProvider(create:  (_) => GetServerAuthCodeBloc()),
        BlocProvider(create: (_) => PostAJobCubit(isEditing: false)),
        BlocProvider(create: (_) => DisconnectCalenderBloc()),
        BlocProvider(create: (_) => ChatBloc()),
        BlocProvider(create: (_) => FavoriteJobBloc()),
        BlocProvider(create: (_) => ReportBloc()),
        BlocProvider(create: (_) => UpdateInviteesCubit()),
        BlocProvider(create: (_) => InvitedUserCubit()),
      ],
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          //currentFocus node check primary focus if doest not then call un focus
          if (!currentFocus.hasPrimaryFocus &&
              currentFocus.focusedChild != null) {
            currentFocus.focusedChild!.unfocus();
          }
        },
        child: MaterialApp.router(
          debugShowCheckedModeBanner: false,
          routerConfig: router,
          builder: (context, child) {
            ScreenUtil.init(
              context,
              minTextAdapt: true,
              designSize: Responsive.isDesktop(context)
                  ? const Size(1280, 832)
                  : const Size(375, 812),
            );
            return Theme(data: AppTheme.lightThemeData, child: child!);
          },
        ),
      ),
    );
  }
}

void setToken() {
  String? token = HiveStorageHelper.getData<String>(HiveBoxName.user, HiveKeys.userToken);
  log("token = $token");
  ApiService.instance.setPartnerGatewayToken(token ?? "");
}
