{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Dev the_voice_directory_app",
            "request": "launch",
            "type": "dart",
            // "args": [
            //     "-t",
            //     "lib/flavors/main_dev.dart",
            //     "--flavor",
            //     "development"
            // ]
        }, 
        {
            "name": "UAT the_voice_directory_app",
            "request": "launch",
            "type": "dart",
            // "args": [
            //     "-t",
            //     "lib/flavors/main_dev.dart",
            //     "--flavor",
            //     "development"
            // ]
        },
        {
            "name": "Prod the_voice_directory_app",
            "request": "launch",
            "type": "dart",
            // "args": [
            //     "-t",
            //     "lib/flavors/main_prod.dart",
            //     "--flavor",
            //     "production"
            // ]
        },
        {
            "name": "Flutter Web (Custom Port)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "-d",
                "chrome",
                "--web-port",
                "5000",
                "--web-browser-flag=--disable-web-security"
            ]
        }
    ]
}