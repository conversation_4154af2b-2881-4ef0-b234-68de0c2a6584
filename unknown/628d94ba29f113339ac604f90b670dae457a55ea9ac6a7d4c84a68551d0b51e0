import 'package:hive/hive.dart';

class HiveStorageHelper {
  static final HiveStorageHelper _instance = HiveStorageHelper._internal();
  static final Map<String, Box> _openBoxes = {};

  factory HiveStorageHelper() {
    return _instance;
  }

  HiveStorageHelper._internal();

  /// **📌 Initialize boxes at app startup**
  static Future<void> init(List<String> boxNames) async {
    for (var boxName in boxNames) {
      if (!Hive.isBoxOpen(boxName)) {
        _openBoxes[boxName] = await Hive.openBox(boxName);
      } else {
        _openBoxes[boxName] = Hive.box(boxName);
      }
    }
  }

  /// **📌 Save Data (String, int, bool, or Models)**
  static Future<void> saveData<T>(String boxName, String key, T value) async {
    await _openBoxes[boxName]?.put(key, value);
  }

  /// **📌 Retrieve Data (Returns null if not found) **
  static T? getData<T>(String boxName, String key) {
    return _openBoxes[boxName]?.get(key) as T?;
  }

  /// **📌 Delete a Key**
  static Future<void> deleteData(String boxName, String key) async {
    await _openBoxes[boxName]?.delete(key);
  }

  /// **📌 Check if Key Exists**
  static bool containsKey(String boxName, String key) {
    return _openBoxes[boxName]?.containsKey(key) ?? false;
  }

  /// **📌 Clear Data in a Key of a Box**
  static void deleteKeyInBox({required String boxName, required String key}) {
    _openBoxes[boxName]?.delete(key);
  }

  /// **📌 Clear All Data in a Box**
  static Future<void> clearBox(String boxName) async {
    await _openBoxes[boxName]?.clear();
  }
}
